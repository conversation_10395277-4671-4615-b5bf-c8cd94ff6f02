{"name": "resume", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@fingerprintjs/fingerprintjs": "^4.6.2", "@hello-pangea/dnd": "^18.0.1", "@hookform/resolvers": "^5.0.1", "@leafer-in/resize": "^1.6.1", "@leafer-ui/core": "^1.6.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@react-spring/web": "^10.0.0", "@toast-ui/editor": "^3.2.2", "@toast-ui/react-editor": "^3.2.3", "ahooks": "^3.8.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "immer": "^10.1.1", "lucide-react": "^0.503.0", "next": "15.3.1", "next-themes": "^0.4.6", "react": "^19.0.0", "react-beautiful-dnd": "^13.1.1", "react-day-picker": "9.8.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "react-markdown": "^10.1.0", "react-qr-code": "^2.0.15", "react-resizable-panels": "^3.0.2", "react-sortablejs": "^6.1.4", "server-only": "^0.0.1", "sonner": "^2.0.3", "sortablejs": "^1.15.6", "tailwind-merge": "^3.2.0", "vaul": "^1.1.2", "zod": "^3.25.28", "zustand": "^5.0.3"}, "devDependencies": {"@builder.io/partytown": "^0.10.3", "@eslint/eslintrc": "^3", "@svgr/webpack": "^8.1.0", "@types/node": "^20.17.32", "@types/react": "^19", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "^19", "@types/sortablejs": "^1.15.8", "@types/webpack": "^5.28.5", "autoprefixer": "^10.4.21", "critters": "^0.0.25", "eslint": "^9", "eslint-config-next": "15.3.1", "eslint-plugin-tailwindcss": "^3.18.0", "postcss": "^8.5.6", "tailwindcss": "3.4.15", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.8", "typescript": "^5"}}