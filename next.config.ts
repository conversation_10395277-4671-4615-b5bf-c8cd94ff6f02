import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  trailingSlash: true,
  skipTrailingSlashRedirect: true,

  // 输出模式配置，用于 Docker 部署
  output: 'standalone',

  // 图片域名配置
  images: {
    domains: ['cdn.avrilko.com', 'mp.weixin.qq.com','cdn.pandaresume.com'],
  },

  // 关闭 React 严格模式，防止开发环境中 useEffect 被调用两次
  reactStrictMode: false,

  // 跳过 ESLint 检查
  eslint: {
    ignoreDuringBuilds: true,
  },

  webpack(config) {
    // SVG 处理配置
    config.module.rules.push({
      test: /\.svg$/,
      use: ['@svgr/webpack'],
    });

    return config;
  },
};

export default nextConfig;