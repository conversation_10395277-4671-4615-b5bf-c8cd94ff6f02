"use client"

import { useEffect, useRef, useMemo, useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useModuleStore } from '@/store/useModuleStore';
import { useUserStore } from '@/store/useUserStore';
import { ResumeDetailResponse } from '@/api/server/types/resume';
import DetailHeader from '@/views/resume/components/DetailHeader';
import { UserResponse, UserType } from '@/api/client/types/user';
import { userApi } from '@/api/client/user';
import ResumeArea from '@/components/ResumeArea';
import Toolbar from '@/components/Toolbar';
import { useCompletenessScore } from '@/hooks/useCompletenessScore';

import { CheckCircle, AlertCircle, XCircle, Edit3 } from 'lucide-react';
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from '@/components/ui/accordion';
import LoginDialog from '@/components/Auth/LoginDialog';
import MembershipWithDownloadCouponDialog from '@/components/MembershipWithDownloadCouponDialog';
import EmailShareDialog from '@/components/EmailShareDialog';
import { toast } from 'sonner';

interface PreviewPageProps {
  resumeDetail: ResumeDetailResponse | null;
  initialUserInfo?: UserResponse | null;
  onlineUsers?: number;
  hideOptimize?: boolean;
}

export default function PreviewPage({
  resumeDetail,
  initialUserInfo = null,
  onlineUsers = 128,
  hideOptimize = true
}: PreviewPageProps) {
  const router = useRouter();
  const { initializeFromResumeDetail, modules, customModules, resumeName, saveResume } = useModuleStore();
  const { is_logged_in, user_type, getGuestUserInfo } = useUserStore();

  // 弹窗状态管理
  const [isEmailShareDialogOpen, setIsEmailShareDialogOpen] = useState(false);
  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false);
  const [isMembershipDialogOpen, setIsMembershipDialogOpen] = useState(false);

  // 获取完整度评分数据
  const scoreData = useCompletenessScore();

  // 获取所有模块的状态（包括必填模块和额外模块）
  const allModulesStatus = useMemo(() => {
    const allModules: Array<{
      id: string;
      name: string;
      status: 'complete' | 'partial' | 'empty';
      isVisible: boolean;
      isRequired: boolean;
    }> = [];

    // 添加必填模块
    allModules.push(
      {
        id: 'basic_info',
        name: '基本信息',
        status: scoreData.requiredModules.basicInfo.status,
        isVisible: true,
        isRequired: true
      },
      {
        id: 'education',
        name: '教育经历',
        status: scoreData.requiredModules.education.status,
        isVisible: true,
        isRequired: true
      },
      {
        id: 'work',
        name: '工作经历',
        status: scoreData.requiredModules.work.status,
        isVisible: true,
        isRequired: true
      },
      {
        id: 'personal_summary',
        name: '个人总结',
        status: scoreData.requiredModules.personalSummary.status,
        isVisible: true,
        isRequired: true
      }
    );

    // 添加额外模块
    const extraModuleIds = ['project', 'research', 'team', 'portfolio', 'other', 'honors', 'skills'];

    // 检查传统额外模块
    extraModuleIds.forEach(id => {
      const moduleItem = modules[id];
      if (moduleItem && moduleItem.is_visible) {
        let status: 'complete' | 'partial' | 'empty' = 'empty';

        if (moduleItem.item) {
          if (Array.isArray(moduleItem.item)) {
            // 对于数组类型的模块，检查是否有条目且条目内容完整
            if (moduleItem.item.length === 0) {
              status = 'empty';
            } else {
              // 根据不同模块类型检查必填字段
              const hasCompleteContent = moduleItem.item.some((item: unknown) => {
                if (typeof item === 'object' && item !== null) {
                  const itemObj = item as Record<string, unknown>;

                  // 定义每个模块的必填字段
                  let requiredFields: string[] = [];
                  switch (moduleItem.type) {
                    case 'education':
                      requiredFields = ['school_name', 'major', 'degree', 'start_date', 'end_date'];
                      break;
                    case 'work':
                      requiredFields = ['company', 'job', 'start_month', 'end_month'];
                      break;
                    case 'project':
                      requiredFields = ['name', 'role', 'start_month', 'end_month', 'desc'];
                      break;
                    case 'research':
                      requiredFields = ['name', 'role', 'start_month', 'end_month', 'desc'];
                      break;
                    case 'team':
                      requiredFields = ['name', 'role', 'start_month', 'end_month', 'desc'];
                      break;
                    case 'portfolio':
                      requiredFields = ['name', 'url'];
                      break;
                    case 'other':
                      requiredFields = ['name', 'desc'];
                      break;
                    default:
                      // 对于其他模块，检查所有非id、index字段
                      requiredFields = Object.keys(itemObj).filter(key => key !== 'id' && key !== 'index');
                  }

                  // 检查必填字段是否都有值
                  return requiredFields.every(fieldName => {
                    const field = itemObj[fieldName];
                    if (field && typeof field === 'object' && field !== null && 'value' in field) {
                      const fieldWithValue = field as { value: unknown };
                      return fieldWithValue.value && fieldWithValue.value.toString().trim() !== '';
                    }
                    return field && field.toString().trim() !== '';
                  });
                }
                return false;
              });

              // 如果有完整的条目则为complete，否则为partial
              status = hasCompleteContent ? 'complete' : 'partial';
            }
          } else if (typeof moduleItem.item === 'object') {
            // 对于对象类型的模块（如荣誉、技能）
            if (moduleItem.type === 'honors') {
              const honorsItem = moduleItem.item as { values?: { value?: unknown[] } };
              status = honorsItem.values?.value?.length ? 'complete' : 'empty';
            } else if (moduleItem.type === 'skills') {
              const skillsItem = moduleItem.item as { values?: { value?: unknown[] } };
              status = skillsItem.values?.value?.length ? 'complete' : 'empty';
            }
          }
        }

        allModules.push({
          id: moduleItem.id,
          name: moduleItem.name,
          status,
          isVisible: true,
          isRequired: false
        });
      }
    });

    // 检查自定义模块
    customModules.forEach(customModule => {
      let status: 'complete' | 'partial' | 'empty' = 'empty';

      if (!customModule.items || customModule.items.length === 0) {
        status = 'empty';
      } else {
        // 检查是否有完整的条目（所有必填字段都填写）
        const hasCompleteContent = customModule.items.some(item => {
          const requiredFields: (keyof typeof item)[] = ['name', 'role', 'start_month', 'end_month', 'desc'];
          return requiredFields.every(fieldName => {
            const field = item[fieldName];
            if (field && typeof field === 'object' && 'value' in field) {
              return field.value && field.value.toString().trim() !== '';
            }
            return false;
          });
        });

        // 检查是否有任何内容
        const hasAnyContent = customModule.items.some(item =>
          item.name?.value || item.role?.value || item.desc?.value ||
          item.start_month?.value || item.end_month?.value
        );

        if (hasCompleteContent) {
          status = 'complete';
        } else if (hasAnyContent) {
          status = 'partial';
        } else {
          status = 'empty';
        }
      }

      allModules.push({
        id: customModule.id,
        name: customModule.name,
        status,
        isVisible: true,
        isRequired: false
      });
    });

    // 按状态排序：有问题的模块排在前面
    return allModules.sort((a, b) => {
      // 定义状态优先级：empty > partial > complete
      const statusPriority = { 'empty': 0, 'partial': 1, 'complete': 2 };
      return statusPriority[a.status] - statusPriority[b.status];
    });
  }, [modules, customModules, scoreData.requiredModules]);

  // 获取第一个不是已完成状态的模块ID（用于默认展开）
  const firstIncompleteModuleId = useMemo(() => {
    return allModulesStatus.find(module => module.status !== 'complete')?.id;
  }, [allModulesStatus]);



  // 获取状态图标
  const getStatusIcon = (status: 'complete' | 'partial' | 'empty') => {
    switch (status) {
      case 'complete':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'partial':
        return <AlertCircle className="w-4 h-4 text-yellow-500" />;
      case 'empty':
        return <XCircle className="w-4 h-4 text-red-500" />;
    }
  };

  // 获取状态文本
  const getStatusText = (status: 'complete' | 'partial' | 'empty') => {
    switch (status) {
      case 'complete':
        return '已完成';
      case 'partial':
        return '待完善';
      case 'empty':
        return '未填写';
    }
  };

  const toolbarRef = useRef<HTMLDivElement>(null);

  // 处理邮件分享按钮点击
  const handleEmailShareClick = async () => {
    // 使用store中的状态
    // 检查登录状态
    if (!is_logged_in) {
      setIsLoginDialogOpen(true);
      return;
    }

    // 检查会员状态
    if (user_type !== UserType.Member) {
      try {
        // 调用接口检查下载券数量
        const couponsData = await userApi.getDownloadCouponsCount();
        if (couponsData.count > 0) {
          // 有下载券，允许邮件分享
          setIsEmailShareDialogOpen(true);
          return;
        }
      } catch (error) {
        console.error('获取下载券数量失败:', error);
      }
      // 没有下载券或获取失败，显示会员弹窗
      setIsMembershipDialogOpen(true);
      return;
    }

    // 都满足条件，打开邮件分享弹窗
    setIsEmailShareDialogOpen(true);
  };

  // 处理下载按钮点击
  const handleDownloadClick = async (e: React.MouseEvent) => {
    // 使用store中的状态
    // 检查登录状态
    if (!is_logged_in) {
      e.preventDefault();
      setIsLoginDialogOpen(true);
      return;
    }

    // 检查会员状态
    if (user_type !== UserType.Member) {
      e.preventDefault();
      try {
        // 调用接口检查下载券数量
        const couponsData = await userApi.getDownloadCouponsCount();
        if (couponsData.count > 0) {
          // 有下载券，允许正常跳转
          window.location.href = resumeDetail?.id ? `/make/download/${resumeDetail.id}` : '#';
          return;
        }
      } catch (error) {
        console.error('获取下载券数量失败:', error);
      }
      // 没有下载券或获取失败，显示会员弹窗
      setIsMembershipDialogOpen(true);
      return;
    }

    // 都满足条件，允许正常跳转（不阻止默认行为）
  };

  // 处理一键优化按钮点击
  const handleOptimizeClick = () => {
    if (!resumeDetail?.id) {
      return;
    }

    // 检查登录状态，优先使用initialUserInfo中的状态，如果没有则使用store中的状态
    const isLoggedIn = initialUserInfo ? initialUserInfo.is_logged_in : is_logged_in;

    if (!isLoggedIn) {
      // 未登录，显示登录弹窗
      setIsLoginDialogOpen(true);
      return;
    }

    // 已登录，跳转到优化页面，带上resume_id参数
    router.push(`/youhua/?resume_id=${resumeDetail.id}`);
  };

  // 处理一键打分按钮点击
  const handleScoreClick = async () => {
    if (!resumeDetail?.id) {
      return;
    }

    // 检查登录状态，优先使用initialUserInfo中的状态，如果没有则使用store中的状态
    const isLoggedIn = initialUserInfo ? initialUserInfo.is_logged_in : is_logged_in;

    if (!isLoggedIn) {
      // 未登录，显示登录弹窗
      setIsLoginDialogOpen(true);
      return;
    }

    // 在跳转前先保存简历
    try {
      await saveResume();
    } catch (error) {
      // 保存失败时不阻止跳转，但显示警告
      toast.warning('保存失败，但仍将继续打分', {
        position: 'top-center'
      });
    }

    // 已登录，跳转到打分页面，带上resume_id参数
    router.push(`/zhenduan/?resume_id=${resumeDetail.id}`);
  };

  // 初始化用户store数据
  useEffect(() => {
    const initUserStore = async () => {
      if (initialUserInfo) {
        // 如果有初始用户信息，直接使用它来初始化用户状态
        useUserStore.setState({
          id: initialUserInfo.id,
          username: initialUserInfo.username,
          avatar: initialUserInfo.avatar || 'https://cdn.shineresume.com/avatar/6.svg',
          phone: initialUserInfo.phone || '',
          email: initialUserInfo.email || '',
          user_type: initialUserInfo.user_type,
          created_at: initialUserInfo.created_at,
          is_logged_in: initialUserInfo.is_logged_in,
          open_id: initialUserInfo.open_id || '',
          loading: false,
        });
      } else {
        // 如果没有初始用户信息，获取用户信息
        await getGuestUserInfo();
      }
    };

    initUserStore();
  }, [initialUserInfo, getGuestUserInfo]);

  // 预览页面不应该重新初始化 store 数据，因为它应该显示用户当前编辑的最新状态
  // 而不是服务器上的旧数据。如果 store 中没有数据，才从 resumeDetail 初始化
  useEffect(() => {
    if (resumeDetail && initializeFromResumeDetail) {
      // 检查 store 中是否已有数据（通过 resumeId 判断）
      const currentState = useModuleStore.getState();
      if (!currentState.resumeId || currentState.resumeId !== resumeDetail.id) {
        // 只有当 store 中没有对应简历的数据时，才初始化
        initializeFromResumeDetail(resumeDetail);
      }
    }
  }, [resumeDetail, initializeFromResumeDetail]);



  return (
    <div className="min-h-screen bg-[#f7f7fc]">
      {/* 头部导航 */}
      <div className='w-full bg-white'>
      <div className="bg-white border-b border-gray-200 w-[1200px] mx-auto py-4">
        <DetailHeader
          onlineUsers={onlineUsers}
          initialUserInfo={initialUserInfo}
          hideOptimize={hideOptimize}
          backUrl={resumeDetail?.id ? `/make/${resumeDetail.id}` : '/resume'}
        />
      </div>
      </div>


      {/* 预览内容区域 */}
      <div className="flex py-8 w-[1200px] mx-auto">
        {/* 左侧预览区域 */}
        <div className="w-[800px] flex justify-center">
          <div className="w-full">
            <div className="flex flex-col justify-start items-center w-full px-4 relative">
              {/* 修改简历按钮 - 绝对定位在Toolbar左侧外部 */}
              {resumeDetail?.id && (
                <Link
                  href={`/make/${resumeDetail.id}/`}
                  className="absolute -left-[5rem] top-0 z-10 h-16 flex flex-col items-center justify-center cursor-pointer group rounded-md px-3 hover:bg-muted transition-all bg-white"
                >
                  <div className="h-8 w-8 text-foreground group-hover:text-primary transition-colors flex items-center justify-center">
                    <Edit3 className="h-5 w-5" />
                  </div>
                  <span className="text-sm mt-0 text-foreground group-hover:text-primary transition-colors">
                    修改简历
                  </span>
                </Link>
              )}

              <div ref={toolbarRef} className="w-full mb-4">
                <Toolbar className="rounded-lg" />
              </div>
              <div className="w-full h-full overflow-y-scroll hide-scrollbar">
                <ResumeArea containerRef={toolbarRef} />
              </div>
            </div>
          </div>
        </div>

        {/* 右侧区域 */}
        <div className="flex-1 px-4">
          {/* 右侧内容区域 */}
          <div className="bg-white rounded-lg p-6 space-y-4">
            {/* 操作按钮区域 */}
            <div className="space-y-3">
              {/* 下载按钮 */}
              <Link
                href={resumeDetail?.id ? `/make/download/${resumeDetail.id}` : '#'}
                onClick={handleDownloadClick}
                className="w-full bg-[#824dfc] text-white py-3 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-[#7340e8] transition-colors cursor-pointer"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-4-4m4 4l4-4m5-8H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2z" />
                </svg>
                下载
              </Link>

              {/* 邮件分享按钮 */}
              <button
                onClick={handleEmailShareClick}
                className="w-full border-2 border-[#824dfc] text-[#824dfc] py-3 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-[#824dfc] hover:text-white transition-colors cursor-pointer"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
                邮件分享
              </button>

              {/* 简历打分按钮 */}
              <button
                onClick={handleScoreClick}
                className="w-full border-2 border-[#824dfc] text-[#824dfc] py-3 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-[#824dfc] hover:text-white transition-colors cursor-pointer"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />
                </svg>
                简历打分
              </button>

              {/* 一键优化按钮 */}
              <button
                onClick={handleOptimizeClick}
                className="w-full border-2 border-[#824dfc] text-[#824dfc] py-3 px-4 rounded-lg flex items-center justify-center gap-2 hover:bg-[#824dfc] hover:text-white transition-colors cursor-pointer relative"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                一键优化
              </button>
            </div>


          </div>
          <div className="bg-white rounded-lg p-6 space-y-4 mt-6">
            {/* 简历完整度检查 */}
            <div className="">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-800">简历完整度</h3>
                <span className="text-2xl font-bold text-[#824dfc]">{scoreData.totalScore}%</span>
              </div>

              {/* 完成度进度条 */}
              <div className="mb-6">
                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div
                    className="bg-gradient-to-r from-purple-500 to-blue-500 h-3 rounded-full transition-all duration-500 ease-out"
                    style={{ width: `${scoreData.totalScore}%` }}
                  />
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  有 {scoreData.suggestions.length} 项待核对
                </div>
              </div>

              {/* 模块完成情况 */}
              <div>
                <Accordion type="single" collapsible className="w-full" defaultValue={firstIncompleteModuleId}>
                  {/* 所有模块（按状态排序） */}
                  {allModulesStatus.map((moduleInfo) => (
                    <AccordionItem key={moduleInfo.id} value={moduleInfo.id} className="border-gray-100">
                      <AccordionTrigger className="py-3 hover:no-underline">
                        <div className="flex items-center justify-between w-full pr-4">
                          <div className="flex items-center space-x-3">
                            {getStatusIcon(moduleInfo.status)}
                            <span className="text-base font-medium">{moduleInfo.name}</span>
                            {moduleInfo.id === 'basic_info' && scoreData.requiredModules.basicInfo.missingFields.length > 0 && (
                              <span className="text-sm text-red-500 bg-red-50 px-2 py-1 rounded">
                                {scoreData.requiredModules.basicInfo.missingFields.length}
                              </span>
                            )}
                          </div>
                          <span className="text-sm text-gray-500">
                            {getStatusText(moduleInfo.status)}
                          </span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="pt-3 pb-5">
                        <div className="space-y-4">
                          {/* 基本信息模块的特殊处理 */}
                          {moduleInfo.id === 'basic_info' && moduleInfo.status !== 'complete' && (
                            <>
                              {scoreData.requiredModules.basicInfo.missingFields.map((field, index) => (
                                <div key={index} className="flex items-start space-x-3">
                                  <div className="w-1.5 h-1.5 bg-[#824dfc] rounded-full mt-2 flex-shrink-0"></div>
                                  <div className="flex-1">
                                    <p className="text-sm text-gray-600 leading-relaxed">
                                      {field === '姓名' && '姓名是企业识别候选人的基本信息，请补充'}
                                      {field === '手机号' && '手机号是企业联系你的必要信息，请补充'}
                                      {field === '邮箱' && '邮箱是企业联系你的重要方式，请补充'}
                                      {field === '意向城市' && '写明意向城市，有利于有该城市岗位需求的企业主动联系你，建议补充'}
                                      {field === '职业状态' && '当前职业状态是HR判断是否合适的重要信息，建议补充'}
                                      {field === '期望薪资' && '写明期望薪资可以节省双方的筛选时间，如果担心影响薪资可以设当前薪资范围大一些'}
                                      {resumeDetail?.id && (
                                        <Link
                                          href={`/make/${resumeDetail.id}/?active=${moduleInfo.id}`}
                                          className="text-xs text-[#824dfc] hover:underline hover:bg-purple-50 px-1 py-1 rounded cursor-pointer transition-all duration-200 ml-1"
                                        >
                                          去核对
                                        </Link>
                                      )}
                                    </p>
                                  </div>
                                </div>
                              ))}
                            </>
                          )}

                          {/* 其他模块的详细处理 */}
                          {moduleInfo.id !== 'basic_info' && moduleInfo.status !== 'complete' && (
                            <div className="flex items-start space-x-3">
                              <div className="w-1.5 h-1.5 bg-[#824dfc] rounded-full mt-2 flex-shrink-0"></div>
                              <div className="flex-1">
                                <p className="text-sm text-gray-600 leading-relaxed">
                                  {moduleInfo.id === 'education' && moduleInfo.status === 'empty' && '教育背景是HR评估候选人的重要依据，建议补充'}
                                  {moduleInfo.id === 'education' && moduleInfo.status === 'partial' && '教育经历信息不完整，请补充学校名称、专业、学历、起止时间等信息'}

                                  {moduleInfo.id === 'work' && moduleInfo.status === 'empty' && '工作经历是展示能力和经验的核心部分，建议补充'}
                                  {moduleInfo.id === 'work' && moduleInfo.status === 'partial' && '工作经历信息不完整，请补充公司名称、职位、起止时间等信息'}

                                  {moduleInfo.id === 'personal_summary' && '个人总结能让HR快速了解你的核心优势，建议补充'}

                                  {moduleInfo.id === 'project' && moduleInfo.status === 'empty' && '项目经历能展示你的实践能力，建议补充'}
                                  {moduleInfo.id === 'project' && moduleInfo.status === 'partial' && '项目经历信息不完整，请补充项目名称、担任角色、起止时间、项目描述等信息'}

                                  {moduleInfo.id === 'research' && moduleInfo.status === 'empty' && '研究经历能体现你的学术能力，建议补充'}
                                  {moduleInfo.id === 'research' && moduleInfo.status === 'partial' && '研究经历信息不完整，请补充研究名称、担任角色、起止时间、研究描述等信息'}

                                  {moduleInfo.id === 'team' && moduleInfo.status === 'empty' && '社团经历能展示你的团队协作能力，建议补充'}
                                  {moduleInfo.id === 'team' && moduleInfo.status === 'partial' && '社团经历信息不完整，请补充社团名称、担任角色、起止时间、经历描述等信息'}

                                  {moduleInfo.id === 'portfolio' && moduleInfo.status === 'empty' && '作品集能直观展示你的专业能力，建议补充'}
                                  {moduleInfo.id === 'portfolio' && moduleInfo.status === 'partial' && '作品集信息不完整，请补充作品名称、作品链接等信息'}

                                  {moduleInfo.id === 'other' && moduleInfo.status === 'empty' && '其他信息可以补充更多亮点，建议完善'}
                                  {moduleInfo.id === 'other' && moduleInfo.status === 'partial' && '其他信息不完整，请补充标题和描述等信息'}

                                  {moduleInfo.id === 'honors' && '荣誉奖项能为简历增色，建议补充'}
                                  {moduleInfo.id === 'skills' && '技能专长是HR关注的重点，建议补充'}

                                  {moduleInfo.id.startsWith('custom-') && moduleInfo.status === 'empty' && `${moduleInfo.name}模块已添加但未填写内容，建议完善或移除`}
                                  {moduleInfo.id.startsWith('custom-') && moduleInfo.status === 'partial' && `${moduleInfo.name}模块信息不完整，请补充项目名称、担任角色、起止时间、项目描述等信息`}
                                  {resumeDetail?.id && (
                                    <Link
                                      href={`/make/${resumeDetail.id}/?active=${moduleInfo.id}`}
                                      className="text-xs text-[#824dfc] hover:underline hover:bg-purple-50 px-1 py-1 rounded cursor-pointer transition-all duration-200 ml-1"
                                    >
                                      去核对
                                    </Link>
                                  )}
                                </p>
                              </div>
                            </div>
                          )}

                          {/* 完成状态的模块 */}
                          {moduleInfo.status === 'complete' && (
                            <div className="flex items-start space-x-3">
                              <div className="w-1.5 h-1.5 bg-green-500 rounded-full mt-2 flex-shrink-0"></div>
                              <div className="flex-1">
                                <p className="text-sm text-gray-600 leading-relaxed">
                                  {moduleInfo.name}模块内容已完善，为简历加分！
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  ))}
                </Accordion>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 邮件分享弹窗 */}
      <EmailShareDialog
        isOpen={isEmailShareDialogOpen}
        onClose={() => setIsEmailShareDialogOpen(false)}
        resumeId={resumeDetail?.id?.toString() || ''}
        fileName={resumeName || '我的简历'}
      />

      {/* 登录弹窗 */}
      <LoginDialog
        isOpen={isLoginDialogOpen}
        onClose={() => setIsLoginDialogOpen(false)}
      />

      {/* 充值套餐弹窗 */}
      <MembershipWithDownloadCouponDialog
        isOpen={isMembershipDialogOpen}
        onClose={() => setIsMembershipDialogOpen(false)}
      />
    </div>
  );
}
