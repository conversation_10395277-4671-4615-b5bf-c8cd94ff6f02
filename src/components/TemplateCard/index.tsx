'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { exampleApi } from '@/api/client';
import { PrivilegeType } from '@/api/client/types/ai';
import { usePrivilegeValidation } from '@/components/PrivilegeValidation';
import { toast } from 'sonner';

interface TemplateCardProps {
  id: string;
  title: string;
  imageUrl: string;
  usageCount: number;
  tags?: string[];
  isHot?: boolean;
  hideMetadata?: boolean; // 是否隐藏标签区域和使用人数区域，默认false（不隐藏）
}

const TemplateCard: React.FC<TemplateCardProps> = ({
  id,
  title,
  imageUrl,
  usageCount,
  tags = [],
  isHot = false,
  hideMetadata = false
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // 权限校验 Hook
  const { validatePrivileges, PrivilegeValidationDialog } = usePrivilegeValidation();

  // 处理点击预览 - 直接跳转到预览页面
  const handlePreviewClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(`/jianli/${id}.html`, '_blank', 'noopener,noreferrer');
  };

  // 使用模板按钮点击处理
  const handleUseTemplate = async (e: React.MouseEvent) => {
    e.stopPropagation();

    const exampleId = parseInt(id);
    if (!exampleId || isNaN(exampleId)) {
      toast.error('模板ID不存在');
      return;
    }

    setIsLoading(true);

    try {
      // 权限校验 - 校验简历创建权限
      const hasPrivilege = await validatePrivileges([
        PrivilegeType.PrivilegeResumeCreate
      ]);

      // 如果权限校验失败，直接返回
      if (!hasPrivilege) {
        return;
      }

      const result = await exampleApi.useExample(exampleId);
      toast.success('模板创建成功，正在跳转...');

      // 在新窗口中打开简历编辑页面
      window.open(`/make/${result.resume_id}`, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('使用模板失败:', error);
      toast.error('使用模板失败，请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div
      className="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 overflow-hidden relative cursor-zoom-in"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* 悬停预览层 - 覆盖整个卡片，z-index确保覆盖使用人数标签 */}
      {isHovered && (
        <div className={`absolute inset-0 z-20 bg-black bg-opacity-40 flex flex-col items-center ${hideMetadata ? 'pt-20 pb-16' : 'pt-32 pb-24'}`}>
          {/* 预览图标和文字 - 缩小并靠近按钮 */}
          <div
            className="flex flex-col items-center cursor-pointer hover:scale-105 transition-transform"
            onClick={handlePreviewClick}
          >
            <img src="/home/<USER>" alt="预览" className="w-8 h-8" />
            <span className="text-white text-base font-medium mt-1">点击预览</span>
          </div>

          <div className="flex flex-col items-center mt-auto">
            {/* 使用此模板按钮 - 使用主题色和鼠标可点击样式 */}
            <button
              onClick={handleUseTemplate}
              disabled={isLoading}
              className="bg-[#824dfc] text-white px-8 py-2 rounded-md font-medium hover:bg-[#6a3dd0] transition-colors cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoading ? '创建中...' : '使用此模板'}
            </button>
          </div>
        </div>
      )}

      {/* 模板预览图 - 大幅减小高度 */}
      <div className="relative aspect-[14/15] overflow-hidden">
        <img
          src={imageUrl}
          alt={`${title}简历模板`}
          className="w-full h-full object-cover object-top"
          loading="lazy"
        />

        {/* 使用人数标签 - 根据isHot属性显示不同样式，根据hideMetadata控制显示 */}
        {!hideMetadata && (
          isHot ? (
            <div className="absolute bottom-2 right-2 bg-orange-100 border border-orange-300 rounded-full px-3 py-2 text-xs font-medium text-orange-500 flex items-center z-0">
              <span className="mr-1">
                <img src="/home/<USER>" alt="热门" className="w-3 h-4" loading="lazy" />
              </span>
              {usageCount + '人使用'}
            </div>
          ) : (
            <div className="absolute bottom-2 right-2 bg-purple-100 border border-purple-200 rounded-full px-3 py-2 text-xs font-medium text-purple-600 flex items-center z-0">
              {usageCount + '人使用'}
            </div>
          )
        )}
      </div>

      {/* 卡片内容区域 */}
      <div className="p-3">
        {/* 支持功能标签和功能标签放在同一行 - 根据hideMetadata控制显示 */}
        {!hideMetadata && tags.length > 0 && (
          <div className="flex items-center flex-wrap gap-2 mb-2">
            <span className="text-xs text-gray-500 mr-1">支持功能:</span>
            {tags.map((tag, index) => {
              // 为不同的标签分配不同的颜色
              let colorClass = '';
              switch (index % 5) {
                case 0:
                  colorClass = 'bg-blue-50 text-blue-500';
                  break;
                case 1:
                  colorClass = 'bg-green-50 text-green-500';
                  break;
                case 2:
                  colorClass = 'bg-purple-50 text-purple-500';
                  break;
                case 3:
                  colorClass = 'bg-orange-50 text-orange-500';
                  break;
                case 4:
                  colorClass = 'bg-pink-50 text-pink-500';
                  break;
                default:
                  colorClass = 'bg-gray-50 text-gray-500';
              }

              return (
                <span
                  key={index}
                  className={`text-xs px-1 py-1 rounded-sm ${colorClass}`}
                >
                  {tag}
                </span>
              );
            })}
          </div>
        )}

        {/* 模板标题放在最下面 */}
        <Link
          href={`/jianli/${id}.html`}
          target="_blank"
          rel="noopener noreferrer"
          className={`font-medium text-gray-800 hover:text-[#824dfc] transition-colors cursor-pointer ${hideMetadata ? 'text-xs' : 'text-base'}`}
          title={`${title}简历模板`}
        >
          {title}
        </Link>
      </div>

      {/* 权限校验对话框 */}
      <PrivilegeValidationDialog />
    </div>
  );
};

export default TemplateCard;
