'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import { CalendarIcon, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';

interface YearMonthPickerProps {
  id: string;
  label: string;
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

const MONTHS_ZH = [
  '一月', '二月', '三月', '四月',
  '五月', '六月', '七月', '八月',
  '九月', '十月', '十一月', '十二月'
];

const YearMonthPicker: React.FC<YearMonthPickerProps> = ({
  id,
  label,
  value,
  onChange,
  placeholder = `如：${new Date().getFullYear()}-05`,
  className
}) => {
  // 解析初始日期
  const getInitialYear = useCallback((): number => {
    if (value && value !== '至今') {
      const parts = value.split('-');
      if (parts.length === 2) {
        const year = parseInt(parts[0], 10);
        if (!isNaN(year)) {
          return year;
        }
      }
    }
    return new Date().getFullYear();
  }, [value]);

  const getInitialMonth = useCallback((): number => {
    if (value && value !== '至今') {
      const parts = value.split('-');
      if (parts.length === 2) {
        const month = parseInt(parts[1], 10);
        if (!isNaN(month)) {
          return month - 1; // 0-based month
        }
      }
    }
    return new Date().getMonth();
  }, [value]);

  const [year, setYear] = useState<number>(getInitialYear());
  const [selectedMonth, setSelectedMonth] = useState<number | null>(getInitialMonth());
  const [inputValue, setInputValue] = useState(value);
  const [open, setOpen] = useState(false);

  // 当外部 value 变化时更新内部状态
  useEffect(() => {
    setInputValue(value);
    if (value && value !== '至今') {
      setYear(getInitialYear());
      setSelectedMonth(getInitialMonth());
    } else if (value === '至今') {
      setSelectedMonth(null);
    }
  }, [value, getInitialYear, getInitialMonth]);

  // 处理年份变化
  const handleYearChange = (increment: number) => {
    setYear(prevYear => prevYear + increment);
  };

  // 处理月份选择
  const handleMonthSelect = (month: number) => {
    setSelectedMonth(month);
    const formattedMonth = (month + 1).toString().padStart(2, '0');
    const formattedDate = `${year}-${formattedMonth}`;
    setInputValue(formattedDate);
    onChange(formattedDate);
    setOpen(false);
  };

  // 处理输入框变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // 验证输入格式是否为 YYYY-MM 或 "至今"
    const regex = /^\d{4}-\d{2}$/;
    if (regex.test(newValue) || newValue === '至今') {
      onChange(newValue);
      if (newValue === '至今') {
        setSelectedMonth(null);
      } else {
        const parts = newValue.split('-');
        const year = parseInt(parts[0], 10);
        const month = parseInt(parts[1], 10) - 1;
        if (!isNaN(year) && !isNaN(month)) {
          setYear(year);
          setSelectedMonth(month);
        }
      }
    }
  };

  // 设置为"至今"
  const handleSetToday = () => {
    setInputValue('至今');
    onChange('至今');
    setSelectedMonth(null);
    setOpen(false);
  };

  return (
    <div className={cn("space-y-2", className)}>
      <Label htmlFor={id}>{label}</Label>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div className="relative">
            <Input
              id={id}
              type="text"
              placeholder={placeholder}
              value={inputValue}
              onChange={handleInputChange}
              className="bg-white pr-10 cursor-pointer text-sm"
            />
            <CalendarIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          </div>
        </PopoverTrigger>
        <PopoverContent className="w-[300px] p-0" align="start">
          {/* 年份导航 */}
          <div className="flex items-center justify-between p-3 border-b">
            <button
              onClick={() => handleYearChange(-10)}
              className="p-1 rounded-md hover:bg-gray-100 cursor-pointer"
            >
              <ChevronsLeft className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleYearChange(-1)}
              className="p-1 rounded-md hover:bg-gray-100 cursor-pointer"
            >
              <ChevronLeft className="h-4 w-4" />
            </button>
            <div className="text-base font-medium">{year} 年</div>
            <button
              onClick={() => handleYearChange(1)}
              className="p-1 rounded-md hover:bg-gray-100 cursor-pointer"
            >
              <ChevronRight className="h-4 w-4" />
            </button>
            <button
              onClick={() => handleYearChange(10)}
              className="p-1 rounded-md hover:bg-gray-100 cursor-pointer"
            >
              <ChevronsRight className="h-4 w-4" />
            </button>
          </div>

          {/* 月份网格 */}
          <div className="grid grid-cols-4 gap-2 p-3">
            {MONTHS_ZH.map((month, index) => (
              <button
                key={month}
                onClick={() => handleMonthSelect(index)}
                className={cn(
                  "py-2 text-center text-sm rounded-md hover:bg-gray-100 transition-colors cursor-pointer",
                  selectedMonth === index && "bg-primary text-white hover:bg-purple-600"
                )}
              >
                {month.replace('月', '')}月
              </button>
            ))}
          </div>

          {/* 底部按钮 */}
          <div className="p-3 border-t border-gray-100 flex justify-between">
            <Button
              variant="outline"
              size="sm"
              onClick={handleSetToday}
              className="text-xs cursor-pointer"
            >
              设为&quot;至今&quot;
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={() => setOpen(false)}
              className="text-xs cursor-pointer"
            >
              确定
            </Button>
          </div>
        </PopoverContent>
      </Popover>
    </div>
  );
};

export default YearMonthPicker;
