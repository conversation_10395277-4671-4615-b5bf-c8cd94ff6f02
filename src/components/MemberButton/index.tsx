import React from 'react';
import Image from 'next/image';

interface MemberButtonProps {
  /**
   * 点击事件处理函数
   */
  onClick?: () => void;
  /**
   * 自定义类名
   */
  className?: string;
}

/**
 * 会员限时优惠按钮组件
 */
export default function MemberButton({ onClick, className }: MemberButtonProps) {
  const handleClick = () => {
    // 如果提供了onClick处理函数，则调用它
    if (onClick) {
      onClick();
    } else {
      // 默认行为：导航到会员页面
      window.location.href = '/member';
    }
  };

  return (
    <div
      className={`inline-flex items-center justify-center bg-[var(--primary)] hover:bg-[#6b3dd6] text-white px-4 py-2 rounded-lg cursor-pointer transition-colors text-sm font-medium ${className || ''}`}
      onClick={handleClick}
    >
      <Image
        src="/home/<USER>"
        alt="会员图标"
        width={16}
        height={16}
        className="mr-2"
      />
      会员限时优惠
    </div>
  );
}
