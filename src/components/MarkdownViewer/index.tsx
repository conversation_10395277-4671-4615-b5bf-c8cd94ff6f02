'use client';

import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import '@toast-ui/editor/dist/toastui-editor-viewer.css';
import { useResumeStyleStore } from '@/store/useResumeStyleStore';

interface MarkdownViewerProps {
  content: string;
  className?: string;
  style?: React.CSSProperties;
  removePadding?: boolean;
  inheritLineHeight?: boolean;
}

// 动态导入 Toast UI Viewer，禁用 SSR
const Viewer = dynamic(
  () => import('@toast-ui/react-editor').then(mod => mod.Viewer),
  {
    ssr: false,
    loading: () => <div>加载中...</div>
  }
);

const MarkdownViewer: React.FC<MarkdownViewerProps> = ({
  content,
  className = '',
  style = {},
  removePadding = true,
  inheritLineHeight = true
}) => {
  const { font_family, font_size, font_gray, line_spacing } = useResumeStyleStore();
  const [mounted, setMounted] = useState(false);
  const viewerRef = useRef<{ getInstance: () => { setMarkdown: (value: string) => void } } | null>(null);

  // 在客户端标记组件已挂载
  useEffect(() => {
    setMounted(true);
  }, []);

  // 当内容变化时，更新 Viewer 的内容
  useEffect(() => {
    if (mounted && viewerRef.current && content) {
      const viewerInstance = viewerRef.current.getInstance();
      if (viewerInstance) {
        viewerInstance.setMarkdown(content);
      }
    }
  }, [content, mounted]);

  // 如果没有内容，不渲染
  if (!content) {
    return null;
  }

  // 检查是否是AI生成内容（通过className判断）
  const isAIContent = className?.includes('ai-markdown-content');

  // 构建样式对象
  const viewerStyle: React.CSSProperties = {
    ...(removePadding && { padding: 0 }),
    ...(inheritLineHeight && { lineHeight: line_spacing }),
    fontFamily: font_family,
    color: isAIContent ? 'white' : font_gray,
    ...style
  };

  // 服务器端渲染或组件未挂载时显示占位符
  if (!mounted) {
    return (
      <div
        className={className}
        style={viewerStyle}
      >
        加载中...
      </div>
    );
  }

  return (
    <div
      className={`markdown-viewer-container ${className}`}
      style={viewerStyle}
    >
      <style jsx>{`
        .markdown-viewer-container :global(.toastui-editor-contents) {
          font-family: ${font_family} !important;
          color: ${isAIContent ? 'white' : font_gray} !important;
          line-height: ${line_spacing} !important;
        }
        /* 非标题元素应用字体大小 */
        .markdown-viewer-container :global(.toastui-editor-contents p),
        .markdown-viewer-container :global(.toastui-editor-contents div),
        .markdown-viewer-container :global(.toastui-editor-contents span),
        .markdown-viewer-container :global(.toastui-editor-contents li),
        .markdown-viewer-container :global(.toastui-editor-contents ul),
        .markdown-viewer-container :global(.toastui-editor-contents ol),
        .markdown-viewer-container :global(.toastui-editor-contents blockquote),
        .markdown-viewer-container :global(.toastui-editor-contents code),
        .markdown-viewer-container :global(.toastui-editor-contents pre),
        .markdown-viewer-container :global(.toastui-editor-contents table),
        .markdown-viewer-container :global(.toastui-editor-contents td),
        .markdown-viewer-container :global(.toastui-editor-contents th) {
          font-family: ${font_family} !important;
          font-size: ${font_size} !important;
          color: ${isAIContent ? 'white' : font_gray} !important;
          line-height: ${line_spacing} !important;
        }
        /* 标题元素只应用字体族、颜色和行高，不改变字体大小 */
        .markdown-viewer-container :global(.toastui-editor-contents h1),
        .markdown-viewer-container :global(.toastui-editor-contents h2),
        .markdown-viewer-container :global(.toastui-editor-contents h3),
        .markdown-viewer-container :global(.toastui-editor-contents h4),
        .markdown-viewer-container :global(.toastui-editor-contents h5),
        .markdown-viewer-container :global(.toastui-editor-contents h6) {
          font-family: ${font_family} !important;
          color: ${isAIContent ? 'white' : font_gray} !important;
          line-height: ${line_spacing} !important;
        }
      `}</style>
      <Viewer
        ref={viewerRef}
        initialValue={content}
      />
    </div>
  );
};

export default MarkdownViewer;
