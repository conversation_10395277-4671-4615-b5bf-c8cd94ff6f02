'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Share2 } from 'lucide-react';
import { toast } from 'sonner';
import { resumeApi } from '@/api/client/resume';

interface EmailShareDialogProps {
  isOpen: boolean;
  onClose: () => void;
  resumeId: string;
  fileName?: string;
}

/**
 * 邮件分享弹窗组件
 */
export default function EmailShareDialog({ isOpen, onClose, resumeId, fileName = '我的简历' }: EmailShareDialogProps) {
  const [email, setEmail] = useState('');
  const [isSharing, setIsSharing] = useState(false);

  // 邮箱格式验证
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  // 处理分享
  const handleShare = async () => {
    if (!email.trim()) {
      toast.error('请输入邮箱地址', {
        position: 'top-center'
      });
      return;
    }

    if (!isValidEmail(email)) {
      toast.error('请输入正确的邮箱格式', {
        position: 'top-center'
      });
      return;
    }

    setIsSharing(true);

    try {
      // 调用邮件分享API
      await resumeApi.shareByEmail(resumeId, email, fileName);

      toast.success('简历已发送至指定邮箱', {
        position: 'top-center'
      });

      // 关闭弹窗并重置状态
      setEmail('');
      onClose();
    } catch (error) {
      // 显示错误提示
      const errorMsg = error instanceof Error ? error.message : '分享失败，请稍后重试';
      toast.error(errorMsg, {
        position: 'top-center',
        style: {
          color: 'white',
          backgroundColor: 'var(--destructive)',
          border: '1px solid var(--destructive)'
        }
      });
    } finally {
      setIsSharing(false);
    }
  };

  // 处理弹窗关闭
  const handleClose = () => {
    if (!isSharing) {
      setEmail('');
      onClose();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent
        className="sm:max-w-md p-6"
        onClick={(e) => e.stopPropagation()}
      >
        <DialogHeader className="text-center mb-6">
          <div className="flex items-center justify-center gap-2 mb-2">
            <Share2 className="w-6 h-6 text-[#824dfc]" />
            <DialogTitle className="text-xl font-medium text-gray-800">
              分享简历
            </DialogTitle>
          </div>
        </DialogHeader>

        <div className="space-y-4">
          {/* 说明文字 */}
          <p className="text-sm text-gray-600 -mt-2">
            输入邮箱地址，简历将以 <span className="text-[#824dfc] font-medium">PDF格式</span> 发送至指定邮箱
          </p>

          {/* 邮箱输入框 */}
          <div className="space-y-2">
            <Input
              type="email"
              placeholder="如：<EMAIL>"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="w-full"
              disabled={isSharing}
            />
          </div>

          {/* 分享按钮 */}
          <Button
            onClick={(e) => {
              e.stopPropagation();
              handleShare();
            }}
            disabled={isSharing || !email.trim() || !isValidEmail(email)}
            className={`w-full py-3 px-4 rounded-lg transition-colors ${
              isSharing || !email.trim() || !isValidEmail(email)
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-[#824dfc] hover:bg-[#7340e8] text-white'
            }`}
          >
            {isSharing ? '分享中...' : '分享'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
