'use client';

import React from 'react';
import ModuleHeader from './components/ModuleHeader';
import ModuleContent from './components/ModuleContent';
import { useModuleHeaderHeight } from '@/hooks/useModuleHeaderHeight';

interface ModuleManagerProps {
  className?: string;
}

export default function ModuleManager({ className }: ModuleManagerProps) {
  // 使用自定义hook监听ModuleHeader高度，设置200ms的防抖延迟
  const { headerRef, contentHeight } = useModuleHeaderHeight(200);

  return (
    <div style={{ backgroundColor: '#f1f1fb' }} className={`shadow-md rounded-md p-4 w-full h-full flex flex-col ${className}`}>
      {/* 模块导航头部 - 添加ref用于测量高度 */}
      <div ref={headerRef}>
        <ModuleHeader />
      </div>

      {/* 模块内容区域 - 使用计算的高度 */}
      <div
        className="mt-4 overflow-hidden"
        style={{ height: contentHeight }}
      >
        <ModuleContent />
      </div>
    </div>
  );
}
