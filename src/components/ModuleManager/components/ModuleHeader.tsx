'use client';

import React, { useRef, useEffect } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useModuleStore } from '@/store/useModuleStore';
import ModuleDropdown from './ModuleDropdown';

// 图标组件

const ManageIcon = () => (
  <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M4 8H8V4H4V8ZM10 20H14V16H10V20ZM4 20H8V16H4V20ZM4 14H8V10H4V14ZM10 14H14V10H10V14ZM16 4V8H20V4H16ZM10 8H14V4H10V8ZM16 14H20V10H16V14ZM16 20H20V16H16V20Z" fill="currentColor" fillOpacity="0.5"/>
  </svg>
);

// 模块类型定义在内联使用，不需要在这里定义

// 模块图标配置 - 只定义ID和图标，名称会从store中获取
const moduleIcons: Record<string, React.ReactNode> = {
  'basic_info': <Image src="/image/basic-info.svg" alt="基本信息" width={24} height={24} />,
  'education': <Image src="/image/education.svg" alt="教育经历" width={24} height={24} />,
  'work': <Image src="/image/work.svg" alt="工作经历" width={24} height={24} />,
  'project': <Image src="/image/project.svg" alt="项目经历" width={24} height={24} />,
  'research': <Image src="/image/research.svg" alt="研究经历" width={24} height={24} />,
  'team': <Image src="/image/team.svg" alt="社团经历" width={24} height={24} />,
  'portfolio': <Image src="/image/portfolio.svg" alt="作品集" width={24} height={24} />,
  'other': <Image src="/image/other.svg" alt="其他" width={24} height={24} />,
  'personal_summary': <Image src="/image/personal-summary.svg" alt="个人总结" width={24} height={24} />,
  'honors': <Image src="/image/honors.svg" alt="荣誉墙" width={24} height={24} />,
  'skills': <Image src="/image/skills.svg" alt="技能条" width={24} height={24} />,
};

export default function ModuleHeader() {
  const {
    activeModuleId,
    setActiveModule,
    showModuleDropdown,
    toggleModuleDropdown,
    modules: storeModules,
    customModules
  } = useModuleStore();

  const dropdownRef = useRef<HTMLDivElement>(null);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      // 检查点击的元素是否在下拉菜单内
      const dropdown = document.querySelector('.module-dropdown');
      const isClickInDropdown = dropdown && dropdown.contains(event.target as Node);

      // 检查点击的元素是否是管理模块按钮
      const isClickInButton = dropdownRef.current && dropdownRef.current.contains(event.target as Node);

      // 如果点击的既不是下拉菜单也不是按钮，且下拉菜单正在显示，则关闭下拉菜单
      if (!isClickInDropdown && !isClickInButton && showModuleDropdown) {
        // 先重置下拉菜单位置，再关闭下拉菜单
        const dropdown = document.querySelector('.module-dropdown') as HTMLElement;
        if (dropdown) {
          dropdown.style.top = '-1000px';
        }
        toggleModuleDropdown();
      }
    }

    document.addEventListener('mousedown', handleClickOutside);

    // 组件卸载时重置下拉菜单位置并关闭
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      const dropdown = document.querySelector('.module-dropdown') as HTMLElement;
      if (dropdown) {
        dropdown.style.top = '-1000px';
      }
      if (showModuleDropdown) {
        toggleModuleDropdown();
      }
    };
  }, [showModuleDropdown, toggleModuleDropdown]);

  return (
    <div className="bg-white rounded-md shadow-sm p-1 w-full">
      <div className="flex flex-wrap gap-[8px] overflow-x-auto relative pr-[70px] w-full">
        {/* 合并预设模块和自定义模块，并按照 index 排序 */}
        {(() => {
          // 创建一个类型安全的模块数组
          type VisibleModule = {
            id: string;
            name: string;
            icon: React.ReactNode;
            index: number;
            is_custom: boolean;
          };

          const visibleModules: VisibleModule[] = [];

          // 添加预设模块
          Object.keys(moduleIcons).forEach(moduleId => {
            const storeModule = storeModules[moduleId];
            if (storeModule && storeModule.is_visible) {
              visibleModules.push({
                id: moduleId,
                name: storeModule.name,
                icon: moduleIcons[moduleId],
                index: storeModule.index,
                is_custom: false
              });
            }
          });

          // 添加传统的自定义模块（custom-1 到 custom-10）
          Object.values(storeModules)
            .filter(module => module.is_custom && module.is_visible)
            .forEach(module => {
              visibleModules.push({
                id: module.id,
                name: module.name,
                icon: <Image src="/image/custom-module.svg" alt="自定义模块" width={24} height={24} />,
                index: module.index,
                is_custom: true
              });
            });

          // 添加新的自定义模块（customModules 数组中的所有模块都是可见的）
          customModules.forEach(customModule => {
              visibleModules.push({
                id: customModule.id,
                name: customModule.name,
                icon: <Image src="/image/custom-module.svg" alt="自定义模块" width={24} height={24} />,
                index: customModule.index,
                is_custom: true
              });
            });

          // 按照index排序并渲染
          return visibleModules
            .sort((a, b) => a.index - b.index)
            .map(module => (
              <div
                key={module.id}
                className={cn(
                  "flex flex-col items-center justify-center p-2 cursor-pointer rounded-md transition-colors",
                  activeModuleId === module.id
                    ? "text-primary bg-purple-50"
                    : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                )}
                onClick={() => {
                  setActiveModule(module.id);
                }}
              >
                <div className="w-5 h-5">{module.icon}</div>
                <span className="text-sm">{module.name}</span>
              </div>
            ));
        })()}

        {/* 管理模块按钮 - 始终在第一行最右侧 */}
        <div
          ref={dropdownRef}
          className="relative flex flex-col items-center justify-center p-1.5 cursor-pointer rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-50 border border border-gray-300"
          style={{
            position: 'absolute',
            right: 0,
            top: 0
          }}
          onClick={() => {
            // 计算下拉菜单的位置
            setTimeout(() => {
              if (dropdownRef.current) {
                const rect = dropdownRef.current.getBoundingClientRect();
                const dropdown = document.querySelector('.module-dropdown') as HTMLElement;
                if (dropdown) {
                  // 将下拉菜单定位到按钮正下方
                  dropdown.style.top = `${rect.bottom + window.scrollY}px`;

                  // 计算下拉菜单的左侧位置，使其右对齐到按钮右侧
                  const dropdownWidth = 280; // 下拉菜单的宽度
                  const rightAlignedLeft = rect.right - dropdownWidth + window.scrollX;
                  dropdown.style.left = `${rightAlignedLeft}px`;
                }
              }
            }, 0);
            toggleModuleDropdown();
          }}
        >
          <div className="w-5 h-5"><ManageIcon /></div>
          <span className="text-sm">管理模块</span>
        </div>

        {/* 模块管理下拉菜单 - 放在组件外部以避免被其他元素遮挡 */}
        {showModuleDropdown && <ModuleDropdown />}
      </div>
    </div>
  );
}
