'use client';

import React from 'react';
import { DialogContent, DialogPortal } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';
import CustomDialogOverlay from './CustomDialogOverlay';

interface CustomDialogContentProps extends React.ComponentProps<typeof DialogContent> {
  onMouseDown?: (e: React.MouseEvent) => void;
}

export default function CustomDialogContent({
  className,
  children,
  onMouseDown,
  ...props
}: CustomDialogContentProps) {
  return (
    <DialogPortal>
      <CustomDialogOverlay onMouseDown={onMouseDown} />
      <DialogContent
        className={cn("shadow-xl border-gray-200 cursor-default", className)}
        onMouseDown={(e) => {
          // 阻止事件冒泡，避免触发管理模块弹窗的关闭
          e.stopPropagation();
          onMouseDown?.(e);
        }}
        onClick={(e) => {
          // 阻止事件冒泡，避免触发管理模块弹窗的关闭
          e.stopPropagation();
        }}
        {...props}
      >
        {children}
      </DialogContent>
    </DialogPortal>
  );
}
