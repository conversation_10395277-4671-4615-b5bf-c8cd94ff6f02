'use client';

import React, { useState } from 'react';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface TipItem {
  question: string;
  answer?: string;
}

interface TipProps {
  title: string;
  description?: string;
  tipContent?: React.ReactNode;
  items?: TipItem[];
  className?: string;
}

export default function Tip({ title, description, tipContent, items = [], className }: TipProps) {
  const [showCustomerService, setShowCustomerService] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });

  return (
    <div className={cn("rounded-md overflow-y-scroll hide-scrollbar", className)} style={{ maxHeight: '500px' }}>
      {/* 标题区域 */}
      <div className="pb-1 px-3 font-medium " onClick={() => {}}>
        <div className="text-lg font-medium whitespace-nowrap">{title}</div>
        {description && (
          <div className="text-xs font-normal mt-1">
            {description.includes('立即反馈')
              ? (
                <>
                  {description.split('立即反馈')[0]}
                  <span
                    className="text-primary hover:underline cursor-pointer relative"
                    onMouseEnter={(e) => {
                      const rect = e.currentTarget.getBoundingClientRect();
                      setMousePosition({
                        x: rect.left,
                        y: rect.bottom + window.scrollY
                      });
                      setShowCustomerService(true);
                    }}
                    onMouseLeave={() => setShowCustomerService(false)}
                  >
                    立即反馈
                  </span>
                </>
              )
              : description
            }
          </div>
        )}
      </div>

      {/* 提示内容区域 */}
      {tipContent && (
        <div className="p-3 border-b border-gray-200 bg-blue-50 rounded-md mt-2">
          <div className="flex gap-2">
            <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
              <Image
                src="https://cdn.shineresume.com/shineResume_v2/resumeCenter/%E6%9C%BA%E5%99%A8%E4%BA%BA.svg"
                alt="机器人图标"
                width={24}
                height={24}
              />
            </div>
            <div className="text-xs">
              {tipContent}
            </div>
          </div>
        </div>
      )}

      {/* 问题列表区域 - 使用Accordion组件 */}
      {items.length > 0 && (
        <Accordion type="single" collapsible className="w-full" defaultValue="item-0">
          {items.map((item, index) => (
            <AccordionItem key={index} value={`item-${index}`}>
              <AccordionTrigger className="px-3 py-2 text-xs hover:no-underline hover:bg-gray-100 cursor-pointer rounded-md transition-colors">
                {item.question}
              </AccordionTrigger>
              {item.answer && (
                <AccordionContent className="px-3 pb-3 text-xs text-gray-600">
                  {(() => {
                    const lines = item.answer.split('\n');
                    return lines.map((line, i) => (
                      <React.Fragment key={i}>
                        {line}
                        {i < lines.length - 1 && <br />}
                      </React.Fragment>
                    ));
                  })()}
                </AccordionContent>
              )}
            </AccordionItem>
          ))}
        </Accordion>
      )}

      {/* 客服二维码悬浮窗 - 使用fixed定位避免被遮挡 */}
      {showCustomerService && (
        <div
          className="fixed z-[9999] bg-white rounded-lg shadow-lg p-6 border border-gray-200 w-40"
          style={{
            left: `${mousePosition.x}px`,
            top: `${mousePosition.y + 8}px`
          }}
          onMouseEnter={() => setShowCustomerService(true)}
          onMouseLeave={() => setShowCustomerService(false)}
        >
          <div className="text-center">
            <Image
              src="/home/<USER>"
              alt="客服二维码"
              width={120}
              height={120}
              className="rounded mx-auto"
            />
            <div className="text-sm text-gray-600 mt-4 font-medium">扫码联系客服</div>
          </div>
        </div>
      )}
    </div>
  );
}
