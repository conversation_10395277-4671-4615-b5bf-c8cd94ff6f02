'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useModuleStore } from '@/store/useModuleStore';
import { toast } from 'sonner';
import Image from 'next/image';

import { PlusIcon, ArrowUpRight } from 'lucide-react';
import {
  Dialog,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import CustomDialogContent from './CustomDialogContent';
import SortableModuleList from './SortableModuleList';
import DeleteConfirmDialog from './DeleteConfirmDialog';

export default function ModuleDropdown() {
  const {
    modules,
    customModules,
    showModuleDropdown,
    editModule,
    deleteModule,
    addCustomModule,
    restoreModule,
    setActiveModule
  } = useModuleStore();

  // 添加一个状态用于强制重新渲染
  const [renderKey, setRenderKey] = useState(0);

  // 监听 modules 和 customModules 的变化，强制重新渲染
  useEffect(() => {
    setRenderKey(prev => prev + 1);
  }, [modules, customModules]);

  // 获取 is_visible: false 的已有模块
  const hiddenModules = Object.values(modules).filter(module => !module.is_visible);

  const [editingModuleName, setEditingModuleName] = useState('');
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [currentEditModule, setCurrentEditModule] = useState<{id: string, name: string} | null>(null);
  const [moduleToDelete, setModuleToDelete] = useState<{id: string, name: string} | null>(null);
  const dialogInputRef = useRef<HTMLInputElement>(null);

  // 处理添加自定义模块
  const handleAddModule = () => {
    // 检查是否已达到最大自定义模块数量限制（10个）
    if (customModules.length >= 10) {
      // 显示提示消息
      toast.error('最多只能添加10个自定义模块', {
        position: 'top-center',
        duration: 3000,
      });
      return;
    }

    // 获取当前自定义模块数量，用于生成名称
    const customModuleCount = customModules.length + 1;

    // 创建新的自定义模块名称
    const newName = `自定义模块${customModuleCount}`;

    // 添加新的自定义模块到customModules数组
    addCustomModule(newName);

    // 不关闭弹窗，允许用户继续操作
  };

  // 处理编辑模块 - 打开对话框
  const handleEditClick = (module: { id: string; name: string }, e: React.MouseEvent) => {
    // 阻止事件冒泡，避免触发管理模块弹窗的关闭
    e.stopPropagation();

    // 设置当前编辑的模块
    setCurrentEditModule(module);
    setEditingModuleName(module.name);

    // 打开对话框
    setDialogOpen(true);
  };

  // 保存编辑的模块名称
  const handleSaveEdit = () => {
    if (currentEditModule && editingModuleName.trim()) {
      editModule(currentEditModule.id, editingModuleName.trim());

      // 关闭对话框并重置状态
      setDialogOpen(false);
      setCurrentEditModule(null);
      setEditingModuleName('');
    }
  };

  // 取消编辑
  const handleCancelEdit = () => {
    // 关闭对话框并重置状态
    setDialogOpen(false);
    setCurrentEditModule(null);
    setEditingModuleName('');
  };

  // 处理删除模块
  const handleDeleteModule = (id: string, e: React.MouseEvent) => {
    // 阻止事件冒泡，避免触发管理模块弹窗的关闭
    e.stopPropagation();

    // 获取要删除的模块（从 modules 或 customModules 中查找）
    const moduleItem = modules[id] || customModules.find(cm => cm.id === id);
    if (!moduleItem) {
      return;
    }

    // 设置要删除的模块
    setModuleToDelete({ id, name: moduleItem.name });
    // 打开删除确认对话框
    setDeleteDialogOpen(true);
  };

  // 确认删除模块
  const confirmDeleteModule = () => {
    if (moduleToDelete) {
      deleteModule(moduleToDelete.id);
      setModuleToDelete(null);
      setDeleteDialogOpen(false);
    }
  };

  // 处理点击模块，切换到对应模块
  const handleModuleClick = (id: string) => {
    // 设置当前激活的模块
    setActiveModule(id);
    // 不再关闭模块下拉菜单，允许用户继续操作
  };

  // 不再需要处理键盘事件

  if (!showModuleDropdown) return null;

  return (
    <>
      <div key={renderKey} className="module-dropdown fixed top-[-1000px] right-0 mt-1 w-[280px] bg-white border border-gray-200 rounded-md shadow-xl z-[1000] overflow-visible">
        {/* 已有模块 */}
        <div className="p-4 border-b">
          <h3 className="text-sm font-medium text-gray-700 mb-3">已有模块</h3>
          <SortableModuleList
            onEditClick={handleEditClick}
            onDeleteClick={handleDeleteModule}
            onModuleClick={handleModuleClick}
          />
        </div>

        {/* 新增模块 */}
        <div className="p-4">
          <h3 className="text-sm font-medium text-gray-700 mb-3">新增模块</h3>

          {/* 显示 is_visible: false 的已有模块 */}
          {hiddenModules.length > 0 && (
            <div className="space-y-1.5 mb-3">
              {hiddenModules.map((module) => (
                <div
                  key={module.id}
                  className="flex items-center justify-between p-2 rounded-md bg-gray-50 hover:bg-gray-100 cursor-pointer"
                  onClick={(e) => {
                    e.stopPropagation();
                    restoreModule(module.id);
                    // 切换到恢复的模块
                    setActiveModule(module.id);
                    // 不再关闭模块下拉菜单，允许用户继续操作
                  }}
                >
                  <div className="flex items-center gap-2">
                    <ArrowUpRight className="w-4 h-4 text-primary" />
                    <div className="relative">
                      <span className="text-sm">{module.name}</span>
                      {module.support_ai && (
                        <Image
                          src="/image/ai-icon.svg"
                          alt="AI支持"
                          width={16}
                          height={12}
                          className="absolute top-[-12px] right-[-16px] w-[16px] h-[12px]"
                        />
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {/* 添加自定义模块按钮 */}
          <div className="flex items-center">
            <button
              onClick={handleAddModule}
              className="flex items-center gap-2 w-full p-2.5 bg-gray-50 hover:bg-gray-100 text-gray-700 rounded-md transition-colors cursor-pointer"
            >
              <PlusIcon size={16} className="text-primary" />
              <span className="text-sm">自定义</span>
            </button>
          </div>
        </div>
      </div>

      {/* 编辑模块对话框 */}
      <Dialog
        open={dialogOpen}
        onOpenChange={(open) => {
          // 阻止事件冒泡，避免触发管理模块弹窗的关闭
          setDialogOpen(open);

          // 如果对话框关闭，添加一个延迟，防止点击事件冒泡到外部
          if (!open) {
            setTimeout(() => {
              // 这里不做任何操作，只是为了延迟事件处理
            }, 10);
          }
        }}
      >
        <CustomDialogContent
          className="sm:max-w-[425px]"
        >
          <DialogHeader>
            <DialogTitle className="text-center text-xl font-medium">修改模块标题</DialogTitle>
          </DialogHeader>

          <div className="py-4 px-4" onClick={(e) => e.stopPropagation()}>
            <input
              ref={dialogInputRef}
              type="text"
              value={editingModuleName}
              onChange={(e) => {
                e.stopPropagation();
                setEditingModuleName(e.target.value);
              }}
              onKeyDown={(e) => {
                e.stopPropagation();
                if (e.key === 'Enter') {
                  handleSaveEdit();
                } else if (e.key === 'Escape') {
                  handleCancelEdit();
                }
              }}
              onClick={(e) => e.stopPropagation()}
              onMouseDown={(e) => e.stopPropagation()}
              onMouseUp={(e) => e.stopPropagation()}
              autoFocus
              className="w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary cursor-text"
              placeholder="请输入模块名称"
            />
          </div>

          <DialogFooter className="flex justify-end gap-2">
            <DialogClose asChild>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleCancelEdit();
                }}
                className="px-4 py-2 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors cursor-pointer"
              >
                取消
              </button>
            </DialogClose>
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleSaveEdit();
              }}
              className="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-purple-600 transition-colors cursor-pointer"
            >
              确定
            </button>
          </DialogFooter>
        </CustomDialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      {moduleToDelete && (
        <DeleteConfirmDialog
          open={deleteDialogOpen}
          onOpenChange={setDeleteDialogOpen}
          moduleName={moduleToDelete.name}
          onConfirm={confirmDeleteModule}
        />
      )}
    </>
  );
}
