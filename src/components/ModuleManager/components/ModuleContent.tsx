'use client';

import React from 'react';
import { useModuleStore } from '@/store/useModuleStore';

// 导入各个模块组件
import BasicInfo from './module/BasicInfo';
import Education from './module/Education';
import Work from './module/Work';
import Project from './module/Project';
import Research from './module/Research';
import Team from './module/Team';
import Portfolio from './module/Portfolio';
import Other from './module/Other';
import PersonalSummary from './module/PersonalSummary';
import Honors from './module/Honors';
import Skills from './module/Skills';

// 导入自定义模块组件
import CustomModule from './module/CustomModule';

export default function ModuleContent() {
  // 添加样式，确保组件占满父容器高度
  const contentStyle: React.CSSProperties = {
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
  };
  const { activeModuleId, modules, customModules } = useModuleStore();

  // 如果没有选择模块
  if (!activeModuleId) {
    return (
      <div style={contentStyle} className="p-4 rounded-md flex items-center justify-center bg-white">
        <p className="text-gray-500 text-center">请选择一个模块</p>
      </div>
    );
  }

  // 获取当前选中的模块（从 modules 或 customModules 中查找）
  const currentModule = modules[activeModuleId] || customModules.find(cm => cm.id === activeModuleId);

  // 如果模块不存在
  if (!currentModule) {
    return (
      <div style={contentStyle} className="p-4 rounded-md flex items-center justify-center bg-white">
        <p className="text-gray-500 text-center">模块不存在</p>
      </div>
    );
  }

  // 渲染模块内容
  const renderModuleContent = () => {
    // 根据不同的模块ID返回不同的组件
    switch (activeModuleId) {
      case 'basic_info':
        return <BasicInfo />;
      case 'education':
        return <Education />;
      case 'work':
        return <Work />;
      case 'project':
        return <Project />;
      case 'research':
        return <Research />;
      case 'team':
        return <Team />;
      case 'portfolio':
        return <Portfolio />;
      case 'other':
        return <Other />;
      case 'personal_summary':
        return <PersonalSummary />;
      case 'honors':
        return <Honors />;
      case 'skills':
        return <Skills />;
      case 'custom-1':
      case 'custom-2':
      case 'custom-3':
      case 'custom-4':
      case 'custom-5':
      case 'custom-6':
      case 'custom-7':
      case 'custom-8':
      case 'custom-9':
      case 'custom-10':
        return <CustomModule moduleId={activeModuleId} />;
      // 如果是其他自定义模块，检查是否在 customModules 中
      default:
        // 检查是否是新的自定义模块
        const isCustomModule = customModules.some(cm => cm.id === activeModuleId);
        if (isCustomModule) {
          return <CustomModule moduleId={activeModuleId} />;
        }
        return (
          <div style={contentStyle} className="p-4 rounded-md flex items-center justify-center bg-white">
            <p className="text-gray-500 text-center">未知模块类型: {activeModuleId}</p>
          </div>
        );
    }
  };

  // 使用公共容器包装模块组件
  return (
    <div className="p-4 rounded-md h-full bg-white">
      {renderModuleContent()}
    </div>
  );
}
