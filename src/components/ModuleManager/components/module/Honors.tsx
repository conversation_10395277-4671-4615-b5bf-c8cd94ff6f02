'use client';

import React, { useState, useEffect } from 'react';
import Tip from '../Tip';
import ModuleNavigator from '@/components/ModuleNavigator';
import ScrollableContent from '../ScrollableContent';
import ModuleHeader from '@/components/ModuleHeader';
import Image from 'next/image';
import HonorStylePreview from '@/components/HonorStylePreview';
import { useModuleStore } from '@/store/useModuleStore';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  MeasuringStrategy,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// 导入store中的类型
import { HonorsItem, HonorValueItem } from '@/store/useModuleStore';
import ItemDeleteConfirmDialog from '../ItemDeleteConfirmDialog';

// 可排序的荣誉项组件
const SortableHonorItem = ({
  item,
  deleteHonorItem,
  honorItems,
  setHonorItems
}: {
  item: HonorValueItem;
  deleteHonorItem: (index: number) => void;
  honorItems: HonorValueItem[];
  setHonorItems: React.Dispatch<React.SetStateAction<HonorValueItem[]>>;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(item.name.value);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.index.toString() });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 'auto',
    position: 'relative' as const,
    touchAction: 'none',
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditName(e.target.value);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleConfirmEdit();
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      setEditName(item.name.value);
    }
  };

  const handleConfirmEdit = () => {
    if (editName.trim()) {
      const updatedItems = honorItems.map(honor =>
        honor.index === item.index ? {
          ...honor,
          name: { ...honor.name, value: editName.trim() }
        } : honor
      );
      setHonorItems(updatedItems);
    }
    setIsEditing(false);
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-3">
      <div
        className="flex items-center justify-between w-full text-[14px] text-[#2E2F66] bg-white p-4 rounded-md cursor-pointer"
        {...attributes}
      >
        <div className="flex items-center gap-3 flex-1">
          {isEditing ? (
            <div className="flex items-center gap-2 flex-1">
              <input
                type="text"
                value={editName}
                onChange={handleInputChange}
                onKeyDown={handleInputKeyDown}
                className="w-full px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-200"
                autoFocus
                onClick={(e) => e.stopPropagation()}
              />
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleConfirmEdit();
                }}
                className="p-1 text-green-500 hover:text-green-600"
                title="确认"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditing(false);
                  setEditName(item.name.value);
                }}
                className="p-1 text-red-500 hover:text-red-600"
                title="取消"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          ) : (
            <div className="flex-1 truncate" title={item.name.value}>
              {item.name.value}
            </div>
          )}
        </div>

        <div className="flex gap-5 items-center">
          <div
            className={cn(
              "cursor-grab active:cursor-grabbing p-1 rounded-md transition-colors",
              "hover:bg-gray-100 hover:text-primary"
            )}
            {...listeners}
            title="拖拽排序"
            onClick={(e) => e.stopPropagation()}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
            </svg>
          </div>
          {/* 编辑按钮 */}
          <div
            className="cursor-pointer hover:text-primary"
            onClick={(e) => {
              e.stopPropagation();
              setIsEditing(true);
            }}
            title="编辑标题"
          >
            <Image src="/assets/svg/edit-icon.svg" alt="编辑" width={20} height={20} />
          </div>
          {/* 删除按钮 */}
          <div
            className={`cursor-pointer ${honorItems.length <= 1 ? 'opacity-50' : 'hover:text-red-500'}`}
            onClick={(e) => {
              e.stopPropagation();
              if (honorItems.length > 1) {
                deleteHonorItem(item.index);
              }
            }}
            title={honorItems.length <= 1 ? "至少保留一个荣誉项" : "删除此荣誉"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M6.7251 8.77979C7.00124 8.77979 7.2251 9.00364 7.2251 9.27979V14.7341H12.7746V9.27979C12.7746 9.00364 12.9985 8.77979 13.2746 8.77979C13.5508 8.77979 13.7746 9.00364 13.7746 9.27979V15.2341C13.7746 15.5103 13.5508 15.7341 13.2746 15.7341H6.7251C6.44896 15.7341 6.2251 15.5103 6.2251 15.2341V9.27979C6.2251 9.00364 6.44896 8.77979 6.7251 8.77979Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M4.76807 6.78955C4.76807 6.51341 4.99192 6.28955 5.26807 6.28955L14.732 6.28955C15.0082 6.28955 15.232 6.51341 15.232 6.78955C15.232 7.06569 15.0082 7.28955 14.732 7.28955L5.26807 7.28955C4.99192 7.28955 4.76807 7.06569 4.76807 6.78955Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M7.76147 4.76611C7.76147 4.48997 7.98533 4.26611 8.26147 4.26611H11.7384C12.0145 4.26611 12.2384 4.48997 12.2384 4.76611C12.2384 5.04226 12.0145 5.26611 11.7384 5.26611H8.26147C7.98533 5.26611 7.76147 5.04226 7.76147 4.76611Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M8.91357 8.95068C9.18972 8.95068 9.41357 9.17454 9.41357 9.45068V13.1917C9.41357 13.4679 9.18972 13.6917 8.91357 13.6917C8.63743 13.6917 8.41357 13.4679 8.41357 13.1917V9.45068C8.41357 9.17454 8.63743 8.95068 8.91357 8.95068Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M11.0864 8.95068C11.3626 8.95068 11.5864 9.17454 11.5864 9.45068V13.1917C11.5864 13.4679 11.3626 13.6917 11.0864 13.6917C10.8103 13.6917 10.5864 13.4679 10.5864 13.1917V9.45068C10.5864 9.17454 10.8103 8.95068 11.0864 8.95068Z" fill="currentColor"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function Honors() {
  // 定义提示项
  const tipItems = [
    {
      question: '多条重复荣誉怎么写',
      answer: '可以在后面备注上次数或者时间，如：学生会优秀干部（连续3年）'
    },
    {
      question: '要写获得时间吗？',
      answer: '有时间会显得更加真实，建议填写。如：xx院商业策划大赛一等奖'
    },
    {
      question: '优先写哪些荣誉？',
      answer: '与工作技能有相关性的更优先，无相关性也可以证明自己的优秀。但是一定不要写会引起争议的荣誉。\n\n如：xx大胃王、xx省xx区第一兰陵王。如果不是求职餐饮、游戏行业建议不要写，可能会造成负面印象。'
    },
    {
      question: '实在没有荣誉怎么办？',
      answer: '实在没有就不要写，实事求是。短时间内无法变出来，不如好好钻研把起其他模块写的更好。'
    }
  ];

  // 从store中获取honors模块数据
  const { modules, updateModuleItem } = useModuleStore();
  const honorsModule = modules['honors'];

  // 使用ref来跟踪是否是本地状态更新导致的变化
  const isLocalUpdate = React.useRef(false);
  // 使用ref来跟踪是否是首次渲染
  const isFirstRender = React.useRef(true);

  // 默认荣誉墙数据
  const defaultHonorWallData: HonorsItem = {
    honorWallLayout: { label: "荣誉墙布局", value: '1' },
    honorWallStyle: { label: "荣誉墙样式", value: '1' },
    values: { label: "荣誉列表", value: [
      {
        name: { label: "荣誉名称", value: '2021年获复星医药年度优秀员工奖' },
        index: 0,
        desc: { label: "荣誉描述", value: '' },
      },
      {
        name: { label: "荣誉名称", value: '2020年获北京大学"三好学生"称号' },
        index: 1,
        desc: { label: "荣誉描述", value: '' },
      }
    ] }
  };

  // 使用store中的数据，如果没有则使用默认数据
  const [honorWallData, setHonorWallData] = useState<HonorsItem>(() => {
    if (honorsModule?.item &&
        typeof honorsModule.item === 'object' &&
        'honorWallLayout' in honorsModule.item &&
        'honorWallStyle' in honorsModule.item &&
        'values' in honorsModule.item) {
      return honorsModule.item as HonorsItem;
    }
    return defaultHonorWallData;
  });

  // 提取荣誉项目列表
  const [honorItems, setHonorItems] = useState<HonorValueItem[]>(
    honorWallData.values.value || []
  );

  // 布局和样式状态
  const [layout, setLayout] = useState(honorWallData.honorWallLayout.value || '1');
  const [style, setStyle] = useState(honorWallData.honorWallStyle.value || '1');

  // 拖拽状态
  const [activeId, setActiveId] = useState<string | null>(null);

  // 获取当前拖拽的项目
  const activeItem = activeId ? honorItems.find(item => item.index.toString() === activeId) : null;

  // 删除确认对话框状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{id: number, name: string} | null>(null);

  // 设置传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 2, // 只需要拖动2px就会触发拖拽
        tolerance: 3, // 增加容差，使拖拽更容易触发
        delay: 0, // 无延迟
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setHonorItems(items => {
        const oldIndex = items.findIndex(item => item.index.toString() === active.id);
        const newIndex = items.findIndex(item => item.index.toString() === over.id);

        // 创建新数组并重新排序
        const newItems = [...items];
        const [movedItem] = newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, movedItem);

        // 更新索引
        return newItems.map((item, index) => ({
          ...item,
          index
        }));
      });
    }

    setActiveId(null);
  };

  // 添加新的荣誉项目
  const addHonorItem = () => {
    const newItem: HonorValueItem = {
      name: { label: "荣誉名称", value: '新荣誉项' },
      index: honorItems.length,
      desc: { label: "荣誉描述", value: '' },
    };

    setHonorItems([...honorItems, newItem]);
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (index: number) => {
    const item = honorItems.find(item => item.index === index);
    if (item) {
      setItemToDelete({
        id: index,
        name: item.name.value || '荣誉'
      });
      setDeleteConfirmOpen(true);
    }
  };

  // 确认删除荣誉项目
  const confirmDeleteHonorItem = () => {
    if (itemToDelete === null) return;

    const newItems = honorItems.filter(item => item.index !== itemToDelete.id)
      .map((item, idx) => ({ ...item, index: idx }));

    setHonorItems(newItems);

    // 重置删除状态
    setItemToDelete(null);
  };

  // 保留原有的删除函数以兼容现有代码
  const deleteHonorItem = (index: number) => {
    showDeleteConfirm(index);
  };

  // 当本地状态变化时，更新store
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    isLocalUpdate.current = true;

    // 更新store中的数据
    updateModuleItem('honors', {
      honorWallLayout: { label: "荣誉墙布局", value: layout },
      honorWallStyle: { label: "荣誉墙样式", value: style },
      values: { label: "荣誉列表", value: honorItems }
    });
  }, [honorItems, layout, style, updateModuleItem]);

  // 当store中的数据变化时，更新本地状态
  useEffect(() => {
    if (isLocalUpdate.current) {
      isLocalUpdate.current = false;
      return;
    }

    if (honorsModule?.item &&
        typeof honorsModule.item === 'object' &&
        'honorWallLayout' in honorsModule.item &&
        'honorWallStyle' in honorsModule.item &&
        'values' in honorsModule.item) {
      const data = honorsModule.item as HonorsItem;
      setHonorWallData(data);
      setHonorItems(data.values.value || []);
      setLayout(data.honorWallLayout.value || '1');
      setStyle(data.honorWallStyle.value || '1');
    }
  }, [honorsModule]);

  return (
    <div className="h-full">
      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="荣誉墙怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                合适的布局和样式可以让简历看起来这更整齐美观。
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 使用 ScrollableContent 组件 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              id="honors"
              icon={<Image src="/image/honors.svg" alt="荣誉墙" width={24} height={24} />}
              title="荣誉墙"
              description="荣誉表示外部对你能力的客观认可，将极大提升你的印象分。🏆"
            />

            {/* 表单内容区域 */}
            <div className="p-6 space-y-6 bg-[#f7f8fa] rounded-md">
              {/* 荣誉墙设置 */}
              <div className="space-y-4">
                <h3 className="text-base font-medium text-gray-700">荣誉墙设置</h3>

                {/* 荣誉墙布局 */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <Label className="w-24 flex-shrink-0">荣誉墙布局</Label>
                  <div className="flex flex-wrap gap-3 flex-1">
                    <button
                      type="button"
                      className={cn(
                        "flex items-center justify-center h-9 px-3 text-sm rounded-md transition-all border cursor-pointer",
                        layout === '1'
                          ? "bg-primary text-white border-primary"
                          : "bg-white text-gray-700 border-gray-200 hover:border-primary"
                      )}
                      onClick={() => setLayout('1')}
                    >
                      标题平铺-底色
                    </button>
                    <button
                      type="button"
                      className={cn(
                        "flex items-center justify-center h-9 px-3 text-sm rounded-md transition-all border cursor-pointer",
                        layout === '2'
                          ? "bg-primary text-white border-primary"
                          : "bg-white text-gray-700 border-gray-200 hover:border-primary"
                      )}
                      onClick={() => setLayout('2')}
                    >
                      标题平铺-描边
                    </button>
                    <button
                      type="button"
                      className={cn(
                        "flex items-center justify-center h-9 px-3 text-sm rounded-md transition-all border cursor-pointer",
                        layout === '3'
                          ? "bg-primary text-white border-primary"
                          : "bg-white text-gray-700 border-gray-200 hover:border-primary"
                      )}
                      onClick={() => setLayout('3')}
                    >
                      分栏罗列-单栏
                    </button>
                    <button
                      type="button"
                      className={cn(
                        "flex items-center justify-center h-9 px-3 text-sm rounded-md transition-all border cursor-pointer",
                        layout === '4'
                          ? "bg-primary text-white border-primary"
                          : "bg-white text-gray-700 border-gray-200 hover:border-primary"
                      )}
                      onClick={() => setLayout('4')}
                    >
                      分栏罗列-双栏
                    </button>
                  </div>
                </div>

                {/* 荣誉样式 */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <Label className="w-24 flex-shrink-0">荣誉样式</Label>
                  <div className="flex-1">
                    <Select
                      value={style}
                      onValueChange={setStyle}
                    >
                      <SelectTrigger className="w-full bg-white cursor-pointer">
                        <SelectValue placeholder="请选择荣誉样式">
                          <HonorStylePreview style={style} text="校级优秀学生奖学金" />
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="1" className="cursor-pointer">
                          <HonorStylePreview style="1" text="校级优秀学生奖学金" />
                        </SelectItem>
                        <SelectItem value="2" className="cursor-pointer">
                          <HonorStylePreview style="2" text="校级优秀学生奖学金" />
                        </SelectItem>
                        <SelectItem value="3" className="cursor-pointer">
                          <HonorStylePreview style="3" text="校级优秀学生奖学金" />
                        </SelectItem>
                        <SelectItem value="4" className="cursor-pointer">
                          <HonorStylePreview style="4" text="校级优秀学生奖学金" />
                        </SelectItem>
                        <SelectItem value="5" className="cursor-pointer">
                          <HonorStylePreview style="5" text="校级优秀学生奖学金" />
                        </SelectItem>
                        <SelectItem value="6" className="cursor-pointer">
                          <HonorStylePreview style="6" text="校级优秀学生奖学金" />
                        </SelectItem>
                        <SelectItem value="7" className="cursor-pointer">
                          <HonorStylePreview style="7" text="校级优秀学生奖学金" />
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              {/* 添加荣誉 */}
              <div className="space-y-4">
                <h3 className="text-base font-medium text-gray-700">添加荣誉</h3>

                {/* 荣誉项目列表 */}
                <div className="space-y-2">
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragStart={handleDragStart}
                    onDragEnd={handleDragEnd}
                    measuring={{
                      droppable: {
                        strategy: MeasuringStrategy.Always,
                      },
                    }}
                  >
                    <SortableContext
                      items={honorItems.map(item => item.index.toString())}
                      strategy={verticalListSortingStrategy}
                    >
                      {honorItems.map((item) => (
                        <SortableHonorItem
                          key={item.index}
                          item={item}
                          deleteHonorItem={deleteHonorItem}
                          honorItems={honorItems}
                          setHonorItems={setHonorItems}
                        />
                      ))}
                    </SortableContext>

                    {/* 拖拽预览 */}
                    <DragOverlay>
                      {activeItem ? (
                        <div className="flex items-center justify-between w-full text-[14px] text-[#2E2F66] bg-white p-4 rounded-md shadow-md">
                          <div className="flex-1 truncate">
                            {activeItem.name.value}
                          </div>
                        </div>
                      ) : null}
                    </DragOverlay>
                  </DndContext>
                </div>
              </div>
            </div>

            {/* 添加荣誉墙按钮和跳转器 */}
            <div className="mt-6 flex justify-between items-center">
              <button
                type="button"
                onClick={addHonorItem}
                className="flex items-center justify-center gap-2 py-2 px-5 rounded-md transition-all bg-white text-[#333] border border-gray-200 hover:bg-gray-50 active:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 cursor-pointer shadow-sm whitespace-nowrap"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 4V12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M4 8H12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
                <span className="text-base font-medium">添加荣誉墙</span>
              </button>

              <ModuleNavigator currentModuleId="honors" />
            </div>
          </ScrollableContent>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <ItemDeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        itemName={itemToDelete?.name || ''}
        itemType="荣誉"
        onConfirm={confirmDeleteHonorItem}
        disabled={honorItems.length <= 1}
        disabledReason="至少需要保留一个荣誉"
      />
    </div>
  );
}
