'use client';

import React, { useState, useEffect } from 'react';
import Tip from '../Tip';
import ModuleNavigator from '@/components/ModuleNavigator';
import ScrollableContent from '../ScrollableContent';
import ModuleHeader from '@/components/ModuleHeader';
import Image from 'next/image';
import { useModuleStore, type PersonalSummaryItem, type WorkItem } from '@/store/useModuleStore';
import MarkdownEditor from '@/components/MarkdownEditor';
import AIGenerateButtons from '@/components/AIGenerateButtons';

export default function PersonalSummary() {
  // 从store中获取个人总结数据和更新函数
  const { modules, updateModuleItem } = useModuleStore();
  const personalSummaryModule = modules['personal_summary'];
  const workModule = modules['work'];

  // 使用state管理富文本内容
  const [summaryContent, setSummaryContent] = useState<string>('');

  // 初始化富文本内容
  useEffect(() => {
    if (personalSummaryModule?.item && typeof personalSummaryModule.item === 'object') {
      const item = personalSummaryModule.item as PersonalSummaryItem;
      if (item.summary?.value) {
        setSummaryContent(item.summary.value);
      }
    }
  }, [personalSummaryModule]);

  // 处理富文本内容变化
  const handleContentChange = (content: string) => {
    setSummaryContent(content);
    // 直接更新store
    const updatedItem: PersonalSummaryItem = {
      summary: { label: "个人总结", value: content }
    };
    updateModuleItem('personal_summary', updatedItem);
  };

  // 处理AI生成内容
  const handleAIContentGenerated = (content: string) => {
    setSummaryContent(content);
  };

  // 计算工作年限
  const calculateWorkYears = (startMonth: string, endMonth: string): number => {
    if (!startMonth || !endMonth) return 0;

    try {
      const start = new Date(startMonth + '-01');
      const end = endMonth === '至今' ? new Date() : new Date(endMonth + '-01');

      const diffTime = Math.abs(end.getTime() - start.getTime());
      const diffYears = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 365));

      return diffYears;
    } catch {
      return 0;
    }
  };

  // 从工作经历中提取信息用于AI生成
  const getPersonalSummaryInfo = () => {
    if (!workModule?.item || !Array.isArray(workModule.item) || workModule.item.length === 0) {
      return { job: '', workYears: 0 };
    }

    // 类型断言为WorkItem数组
    const workItems = workModule.item as WorkItem[];

    // 获取最新的工作经历（按索引排序，取第一个）
    const sortedWorkItems = [...workItems].sort((a, b) => a.index - b.index);
    const latestWork = sortedWorkItems[0];

    if (!latestWork) {
      return { job: '', workYears: 0 };
    }

    // 提取职位信息
    const job = latestWork.job?.value || '';

    // 计算总工作年限（所有工作经历的年限总和）
    let totalWorkYears = 0;
    for (const workItem of workItems) {
      const startMonth = workItem.start_month?.value || '';
      const endMonth = workItem.end_month?.value || '';
      totalWorkYears += calculateWorkYears(startMonth, endMonth);
    }

    return { job, workYears: totalWorkYears };
  };

  // 定义提示项
  const tipItems = [
    {
      question: '不会写总结怎么办？',
      answer: '可以根据企业的职位要求来写总结，突出你与之匹配的能力即可。\n示例：职位描述要求2年办公经验、熟悉办公软件、拥有良好沟通能力。\n个人总结可以分点突出自己软件使用能力，沟通能力，工作年限。'
    },
    {
      question: '个人总结千万不要这么写',
      answer: '尽量不要写【自己从小就想进XX行业】或者【认真、吃苦耐劳，踏实肯干】这样的通用主观描述，人人都这么写，这样就无法构成你的优势，而且这些主观内容没有标准，无法使人信服。'
    },
    {
      question: '好的个人总结有哪些特点？',
      answer: '1.使用项目符(Bullet Points)描述，能够更好的梳理思路\n2.语言尽可能精简，能用2个字描述的不要用4个字(对XX进行分析,直接写"分析XX")\n3.匹配岗位要求，不要根据自己的想法添加过多其他内容。'
    }
  ];

  return (
    <div className="h-full">
      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="个人总结怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                填写简历的核心思路就是——扬长避短，突出自己和岗位的匹配度。面试或者简历上有缺点没关系，反而更真实，但不要暴露硬伤。
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 使用 ScrollableContent 组件 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              id="personal_summary"
              icon={<Image src="/image/personal-summary.svg" alt="个人总结" width={24} height={24} />}
              title="个人总结"
              description="个人总结是一段凝练专业要点阐述，帮助你命中对方招聘需求的关键点😁"
            />

            {/* 表单内容区域 */}
            <div className="p-6 space-y-6 bg-[#f7f8fa] rounded-md">
              {/* 个人总结内容 */}
              <div className="flex-1">
                <form className="grid grid-cols-2 gap-6" onSubmit={(e) => e.preventDefault()}>
                  {/* 个人总结编辑器 - 占用整行 */}
                  <div className="col-span-2">
                    <div className="space-y-2">
                      <MarkdownEditor
                        value={summaryContent}
                        onChange={handleContentChange}
                        placeholder="请在此输入您的个人总结..."
                        height={200}
                      />
                    </div>
                    <AIGenerateButtons
                      className="w-full"
                      markdownContent={summaryContent}
                      personalSummaryInfo={getPersonalSummaryInfo()}
                      onContentGenerated={handleAIContentGenerated}
                    />
                  </div>
                </form>
              </div>
            </div>

            {/* 跳转器 */}
            <div className="mt-6 flex justify-between items-center">
              <div></div> {/* 空div用于占位，保持与其他模块布局一致 */}
              <ModuleNavigator currentModuleId="personal_summary" />
            </div>
          </ScrollableContent>
        </div>
      </div>
    </div>
  );
}
