'use client';

import React, { useState, useEffect } from 'react';
import Tip from '../Tip';
import ModuleNavigator from '@/components/ModuleNavigator';
import ScrollableContent from '../ScrollableContent';
import ModuleHeader from '@/components/ModuleHeader';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils'; // 用于条件类名
import MarkdownEditor from '@/components/MarkdownEditor';
import YearMonthPicker from '@/components/YearMonthPicker';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  MeasuringStrategy,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
  arrayMove,
} from '@dnd-kit/sortable';
import {
  useSortable,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useModuleStore, CustomItem } from '@/store/useModuleStore';
import ItemDeleteConfirmDialog from '../ItemDeleteConfirmDialog';



// 可拖拽的自定义项目组件
const SortableCustomItem = ({
  item,
  expandedItems,
  toggleExpanded,
  deleteProjectItem,
  projectItems,
  setProjectItems
}: {
  item: CustomItem;
  expandedItems: Record<string, boolean>;
  toggleExpanded: (itemId: string) => void;
  deleteProjectItem: (itemId: string) => void;
  projectItems: CustomItem[];
  setProjectItems: (items: CustomItem[]) => void;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 'auto',
    position: 'relative' as const,
    touchAction: 'none',
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-4 bg-w">
      <div
        className="flex items-center justify-between w-full text-[14px] text-[#2E2F66] bg-white p-4 rounded-md"
        {...attributes}
      >
        <div className="flex items-center gap-3">
          <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={item.name.value}>
            {item.name.value}
          </div>
        </div>
        <div>
          {item.start_month.value} <span>- {item.end_month.value}</span>
        </div>
        <div className="flex gap-5 items-center">
          <div
            className={cn(
              "cursor-grab active:cursor-grabbing p-1 rounded-md transition-colors",
              "hover:bg-gray-100 hover:text-primary"
            )}
            {...listeners}
            title="拖拽排序"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
            </svg>
          </div>
          <div
            className={`cursor-pointer ${projectItems.length <= 1 ? 'opacity-50' : 'hover:text-red-500'}`}
            onClick={(e) => {
              e.stopPropagation();
              if (projectItems.length > 1) {
                deleteProjectItem(item.id);
              }
            }}
            title={projectItems.length <= 1 ? "至少保留一个项目经历" : "删除此项目经历"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M6.7251 8.77979C7.00124 8.77979 7.2251 9.00364 7.2251 9.27979V14.7341H12.7746V9.27979C12.7746 9.00364 12.9985 8.77979 13.2746 8.77979C13.5508 8.77979 13.7746 9.00364 13.7746 9.27979V15.2341C13.7746 15.5103 13.5508 15.7341 13.2746 15.7341H6.7251C6.44896 15.7341 6.2251 15.5103 6.2251 15.2341V9.27979C6.2251 9.00364 6.44896 8.77979 6.7251 8.77979Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M4.76807 6.78955C4.76807 6.51341 4.99192 6.28955 5.26807 6.28955L14.732 6.28955C15.0082 6.28955 15.232 6.51341 15.232 6.78955C15.232 7.06569 15.0082 7.28955 14.732 7.28955L5.26807 7.28955C4.99192 7.28955 4.76807 7.06569 4.76807 6.78955Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M7.76147 4.76611C7.76147 4.48997 7.98533 4.26611 8.26147 4.26611H11.7384C12.0145 4.26611 12.2384 4.48997 12.2384 4.76611C12.2384 5.04226 12.0145 5.26611 11.7384 5.26611H8.26147C7.98533 5.26611 7.76147 5.04226 7.76147 4.76611Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M8.91357 8.95068C9.18972 8.95068 9.41357 9.17454 9.41357 9.45068V13.1917C9.41357 13.4679 9.18972 13.6917 8.91357 13.6917C8.63743 13.6917 8.41357 13.4679 8.41357 13.1917V9.45068C8.41357 9.17454 8.63743 8.95068 8.91357 8.95068Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M11.0864 8.95068C11.3626 8.95068 11.5864 9.17454 11.5864 9.45068V13.1917C11.5864 13.4679 11.3626 13.6917 11.0864 13.6917C10.8103 13.6917 10.5864 13.4679 10.5864 13.1917V9.45068C10.5864 9.17454 10.8103 8.95068 11.0864 8.95068Z" fill="currentColor"></path>
            </svg>
          </div>
          <div
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              toggleExpanded(item.id);
            }}
            style={{ transform: expandedItems[item.id] ? 'rotate(180deg)' : 'rotate(0deg)' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M4.99805 7.85151L5.825 7L9.99805 11.297L14.1711 7L14.998 7.85151L9.99805 13L4.99805 7.85151Z" fill="#707191"></path>
            </svg>
          </div>
        </div>
      </div>

      {expandedItems[item.id] && (
        <div className="pt-[22px]" style={{ transition: 'max-height 0.3s' }}>
          <form className="grid grid-cols-2 gap-6" onSubmit={(e) => e.preventDefault()}>
            {/* 经历名称 - 占用一整行 */}
            <div className="space-y-2 col-span-2">
              <Label htmlFor={`name_${item.id}`}>经历名称</Label>
              <Input
                id={`name_${item.id}`}
                type="text"
                placeholder="输入经历名称，如：志愿者活动"
                value={item.name.value}
                onChange={(e) => {
                  const updatedItems = projectItems.map(proj =>
                    proj.id === item.id ? {
                      ...proj,
                      name: { ...proj.name, value: e.target.value }
                    } : proj
                  );
                  setProjectItems(updatedItems);
                }}
                className="bg-white"
              />
            </div>

            {/* 你的角色 - 占用一整行 */}
            <div className="space-y-2 col-span-2">
              <Label htmlFor={`role_${item.id}`}>你的角色</Label>
              <Input
                id={`role_${item.id}`}
                type="text"
                placeholder="如：志愿者、学员等"
                value={item.role.value}
                onChange={(e) => {
                  const updatedItems = projectItems.map(proj =>
                    proj.id === item.id ? {
                      ...proj,
                      role: { ...proj.role, value: e.target.value }
                    } : proj
                  );
                  setProjectItems(updatedItems);
                }}
                className="bg-white"
              />
            </div>

            {/* 开始时间 - 使用年月选择器 */}
            <YearMonthPicker
              id={`start_month_${item.id}`}
              label="开始时间"
              placeholder={`如：${new Date().getFullYear() - 2}-03`}
              value={item.start_month.value}
              onChange={(value) => {
                const updatedItems = projectItems.map(proj =>
                  proj.id === item.id ? {
                    ...proj,
                    start_month: { ...proj.start_month, value: value }
                  } : proj
                );
                setProjectItems(updatedItems);
              }}
            />

            {/* 结束时间 - 使用年月选择器 */}
            <YearMonthPicker
              id={`end_month_${item.id}`}
              label="结束时间"
              placeholder={`如：${new Date().getFullYear() - 1}-01，或选择'至今'`}
              value={item.end_month.value}
              onChange={(value) => {
                const updatedItems = projectItems.map(proj =>
                  proj.id === item.id ? {
                    ...proj,
                    end_month: { ...proj.end_month, value: value }
                  } : proj
                );
                setProjectItems(updatedItems);
              }}
            />

            {/* 经历描述 */}
            <div className="col-span-2">
              <div className="space-y-2">
                <Label htmlFor={`desc_${item.id}`}>经历描述</Label>
                <MarkdownEditor
                  value={item.desc.value}
                  onChange={(value) => {
                    const updatedItems = projectItems.map(proj =>
                      proj.id === item.id ? {
                        ...proj,
                        desc: { ...proj.desc, value: value }
                      } : proj
                    );
                    setProjectItems(updatedItems);
                  }}
                  height={200}
                  placeholder="经历描述应包含经历内容、你的主要任务和成果三部分。

填写示例:

经历内容：简要描述经历的内容和背景。
主要任务：详细说明你在经历中的职责和贡献。
成果：量化你的成果，如通过XX提升了什么，获得了什么成就等。"
                />
              </div>
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

interface CustomModuleProps {
  moduleId: string;
}

export default function CustomModule({ moduleId }: CustomModuleProps) {
  // 从store中获取模块数据
  const { modules, customModules, updateModuleItem, updateCustomModuleItem, activeIndex, setActiveIndex } = useModuleStore();
  const customModule = modules[moduleId] || customModules.find(cm => cm.id === moduleId);

  // 使用ref来跟踪是否是本地状态更新导致的变化
  const isLocalUpdate = React.useRef(false);
  // 使用ref来跟踪是否是首次渲染
  const isFirstRender = React.useRef(true);

  // 使用store中的数据，如果没有则使用示例数据
  const [projectItems, setProjectItems] = useState<CustomItem[]>(() => {
    if (customModule) {
      // 如果是新的 customModules 格式
      if ('items' in customModule && Array.isArray(customModule.items)) {
        return customModule.items;
      }
      // 如果是旧的 modules 格式
      if ('item' in customModule && Array.isArray(customModule.item)) {
        return customModule.item as CustomItem[];
      }
    }
    // 返回一个默认的空项目
    return [{
      id: 'custom-item-1',
      name: { label: "项目名称", value: '' },
      role: { label: "担任角色", value: '' },
      start_month: { label: "开始时间", value: '' },
      end_month: { label: "结束时间", value: '' },
      desc: { label: "项目描述", value: '' },
      index: 0
    }];
  });

  // 拖拽相关状态
  const [activeId, setActiveId] = useState<string | null>(null);

  // 当store中的数据变化时，更新本地状态
  useEffect(() => {
    if (customModule && !isLocalUpdate.current) {
      // 如果是新的 customModules 格式
      if ('items' in customModule && Array.isArray(customModule.items)) {
        setProjectItems(customModule.items);
      }
      // 如果是旧的 modules 格式
      else if ('item' in customModule && Array.isArray(customModule.item)) {
        setProjectItems(customModule.item as CustomItem[]);
      }
    }
  }, [customModule]);

  // 当本地状态变化时，更新store，但需要防止无限循环
  useEffect(() => {
    // 如果是首次渲染，标记为非首次渲染并返回
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // 仅当不是从store更新本地状态时才更新store
    if (customModule && !isLocalUpdate.current) {
      // 标记为本地更新
      isLocalUpdate.current = true;

      // 判断是新的 customModules 还是旧的 modules
      if ('items' in customModule) {
        // 新的 customModules 格式
        updateCustomModuleItem(moduleId, projectItems);
      } else {
        // 旧的 modules 格式
        updateModuleItem(moduleId, projectItems as unknown as Record<string, unknown>);
      }

      // 重置标记
      setTimeout(() => {
        isLocalUpdate.current = false;
      }, 0);
    }
  }, [projectItems, customModule, updateModuleItem, updateCustomModuleItem, moduleId]);

  // 展开/折叠状态 - 使用对象来跟踪每个项目的展开状态
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(() => {
    // 根据 activeIndex 确定默认展开的项目
    if (activeIndex.length > 0 && projectItems.length > 0 && projectItems[activeIndex[0]]) {
      return { [projectItems[activeIndex[0]].id]: true };
    }
    // 如果没有 activeIndex 或对应项目不存在，默认展开第一个项目
    return projectItems.length > 0 ? { [projectItems[0].id]: true } : {};
  });

  // 监听 activeIndex 变化，更新展开状态
  useEffect(() => {
    if (activeIndex.length > 0 && projectItems.length > 0 && projectItems[activeIndex[0]]) {
      // 关闭所有项目，只展开 activeIndex 对应的项目
      setExpandedItems({ [projectItems[activeIndex[0]].id]: true });
    }
  }, [activeIndex, projectItems]);

  // 切换展开/折叠状态
  const toggleExpanded = (itemId: string) => {
    const itemIndex = projectItems.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    // 先更新展开状态
    setExpandedItems(prev => {
      const newExpanded = {
        ...prev,
        [itemId]: !prev[itemId]
      };

      // 在下一个事件循环中更新 activeIndex，避免在渲染过程中调用 setState
      setTimeout(() => {
        const currentActiveIndex = [...activeIndex];
        if (newExpanded[itemId]) {
          // 展开：添加到 activeIndex
          if (!currentActiveIndex.includes(itemIndex)) {
            currentActiveIndex.push(itemIndex);
            setActiveIndex(currentActiveIndex);
          }
        } else {
          // 折叠：从 activeIndex 移除
          const indexToRemove = currentActiveIndex.indexOf(itemIndex);
          if (indexToRemove > -1) {
            currentActiveIndex.splice(indexToRemove, 1);
            setActiveIndex(currentActiveIndex);
          }
        }
      }, 0);

      return newExpanded;
    });
  };

  // 删除项目
  const deleteProjectItem = (itemId: string) => {
    // 如果只剩下一个项目，不允许删除
    if (projectItems.length <= 1) {
      return;
    }

    // 从列表中移除项目
    const updatedItems = projectItems.filter(item => item.id !== itemId);

    // 更新状态
    setProjectItems(updatedItems);

    // 从展开状态中移除该项目
    const newExpandedItems = { ...expandedItems };
    delete newExpandedItems[itemId];
    setExpandedItems(newExpandedItems);
  };

  // 添加新项目
  const addProjectItem = () => {
    // 生成唯一ID
    const newId = `custom-item-${Date.now()}`;

    // 创建新的项目
    const newProjectItem: CustomItem = {
      id: newId,
      name: { label: "项目名称", value: '' },
      role: { label: "担任角色", value: '' },
      start_month: { label: "开始时间", value: '' },
      end_month: { label: "结束时间", value: '' },
      desc: { label: "项目描述", value: '' },
      index: projectItems.length // 设置索引为当前列表长度
    };

    // 添加到列表中
    setProjectItems([...projectItems, newProjectItem]);

    // 自动展开新添加的项目
    setExpandedItems(prev => ({
      ...prev,
      [newId]: true
    }));
  };

  // 拖拽处理函数
  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id as string);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (active.id !== over?.id) {
      setProjectItems((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over?.id);

        const newItems = arrayMove(items, oldIndex, newIndex);

        // 更新索引
        return newItems.map((item, index) => ({
          ...item,
          index
        }));
      });
    }

    setActiveId(null);
  };

  // 定义提示项
  const tipItems = [
    {
      question: '自定义模块适合写什么内容？',
      answer: '自定义模块适合添加简历中不属于常规类别的内容，如特殊经历、志愿者活动、培训经历、证书、专利等。'
    },
    {
      question: '如何写好自定义模块？',
      answer: '遵循简历的核心原则：突出与求职岗位相关的内容，量化成果，使用动词开头的描述语句，保持简洁明了。'
    }
  ];

  return (
    <div className="h-full">
      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="自定义模块怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                填写简历的核心思路就是——扬长避短，突出自己和岗位的匹配度。面试或者简历上有缺点没关系，反而更真实，但不要暴露硬伤。
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 使用 ScrollableContent 组件 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              id={moduleId}
              icon={<Image src="/image/custom-module.svg" alt={customModule?.name} width={24} height={24} />}
              title={customModule?.name || "自定义模块"}
              description="填写你的自定义内容，可以是任何你想展示的信息"
            />

            {/* 表单内容区域 */}
            <div className="p-6 space-y-6 bg-[#f7f8fa] rounded-md">
              {/* 自定义项目列表 */}
              <div className="flex-1">
                {/* 使用DndContext包装自定义项目列表 */}
                <DndContext
                  sensors={useSensors(
                    useSensor(PointerSensor, {
                      activationConstraint: {
                        distance: 2, // 只需要拖动2px就会触发拖拽
                        tolerance: 3, // 增加容差，使拖拽更容易触发
                        delay: 0, // 无延迟
                      },
                    }),
                    useSensor(KeyboardSensor, {
                      coordinateGetter: sortableKeyboardCoordinates,
                    })
                  )}
                  collisionDetection={closestCenter}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  measuring={{
                    droppable: {
                      strategy: MeasuringStrategy.Always,
                    },
                  }}
                >
                  <SortableContext
                    items={projectItems.map(item => item.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    {projectItems.map((item) => (
                      <SortableCustomItem
                        key={item.id}
                        item={item}
                        expandedItems={expandedItems}
                        toggleExpanded={toggleExpanded}
                        deleteProjectItem={deleteProjectItem}
                        projectItems={projectItems}
                        setProjectItems={setProjectItems}
                      />
                    ))}
                  </SortableContext>

                  {/* 拖拽预览 */}
                  <DragOverlay>
                    {activeId ? (
                      <div className="flex items-center justify-between w-full text-[14px] text-[#2E2F66] bg-white p-4 rounded-md shadow-lg border-2 border-purple-200">
                        <div className="flex items-center gap-3">
                          <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={projectItems.find(item => item.id === activeId)?.name.value}>
                            {projectItems.find(item => item.id === activeId)?.name.value || '未填写名称'}
                          </div>
                        </div>
                        <div>
                          {projectItems.find(item => item.id === activeId)?.start_month.value || '未填写'} <span>- {projectItems.find(item => item.id === activeId)?.end_month.value || '未填写'}</span>
                        </div>
                        <div className="flex gap-5 items-center">
                          <div className={cn(
                            "cursor-grab p-1 rounded-md bg-gray-100 text-primary"
                          )}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
                              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
                            </svg>
                          </div>
                        </div>
                      </div>
                    ) : null}
                  </DragOverlay>
                </DndContext>
              </div>

            </div>

            {/* 添加自定义项目按钮和跳转器 */}
            <div className="mt-6 flex justify-between items-center">
              <button
                type="button"
                onClick={addProjectItem}
                className="flex items-center justify-center gap-2 py-2 px-5 rounded-md transition-all bg-white text-[#333] border border-gray-200 hover:bg-gray-50 active:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 cursor-pointer shadow-sm whitespace-nowrap"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 4V12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M4 8H12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
                <span className="text-base font-medium">添加经历</span>
              </button>

              <ModuleNavigator currentModuleId={moduleId} />
            </div>
          </ScrollableContent>
        </div>
      </div>
    </div>
  );
}