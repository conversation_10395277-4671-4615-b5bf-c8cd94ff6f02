'use client';

import React, { useState, useEffect } from 'react';
import Tip from '../Tip';
import ModuleNavigator from '@/components/ModuleNavigator';
import ScrollableContent from '../ScrollableContent';
import ModuleHeader from '@/components/ModuleHeader';
import Image from 'next/image';
import { useModuleStore, type OtherItem as StoreOtherItem } from '@/store/useModuleStore';
import { cn } from '@/lib/utils';
import MarkdownEditor from '@/components/MarkdownEditor';

// 添加标签样式
const tagItemStyle = `
  .tag-item {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 3px 8px;
    background-color: #e8e8e8;
    border-radius: 16px;
    font-size: 12px;
    color: #333;
    cursor: pointer;
    transition: all 0.2s;
  }
  .tag-item:hover {
    background-color: #d0d0d0;
  }
`;

// 添加样式到文档
if (typeof document !== 'undefined') {
  const style = document.createElement('style');
  style.textContent = tagItemStyle;
  document.head.appendChild(style);
}
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragOverlay,
  MeasuringStrategy,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import ItemDeleteConfirmDialog from '../ItemDeleteConfirmDialog';

// 本地其他项目类型（用于表单状态）
interface OtherItem {
  id: string;
  name: string;
  desc: string;
  index: number;
}



// 可排序的其他项目组件
const SortableOtherItem = ({
  item,
  expandedItems,
  toggleExpanded,
  deleteOtherItem,
  otherItems,
  setOtherItems
}: {
  item: OtherItem;
  expandedItems: Record<string, boolean>;
  toggleExpanded: (itemId: string) => void;
  deleteOtherItem: (itemId: string) => void;
  otherItems: OtherItem[];
  setOtherItems: React.Dispatch<React.SetStateAction<OtherItem[]>>;
}) => {
  // 添加编辑状态
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(item.name);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 'auto',
    position: 'relative' as const,
    touchAction: 'none',
  };

  // 处理编辑按钮点击
  const handleEditClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    setEditName(item.name);
    setIsEditing(true);
  };

  // 处理确认编辑
  const handleConfirmEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    const updatedItems = otherItems.map(other =>
      other.id === item.id ? {...other, name: editName} : other
    );
    setOtherItems(updatedItems);
    setIsEditing(false);
  };

  // 处理取消编辑
  const handleCancelEdit = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsEditing(false);
  };

  // 处理输入框变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditName(e.target.value);
  };

  // 处理输入框按键事件
  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      const updatedItems = otherItems.map(other =>
        other.id === item.id ? {...other, name: editName} : other
      );
      setOtherItems(updatedItems);
      setIsEditing(false);
    } else if (e.key === 'Escape') {
      setIsEditing(false);
    }
  };

  // 处理标签点击的通用函数
  const handleTagClick = (tagText: string) => {
    // 获取当前富文本编辑器的内容
    let currentContent = item.desc || '';

    // 如果内容为空或只有空段落，直接设置为标签文字
    if (!currentContent || currentContent === '<p><br></p>' || currentContent === '<p></p>') {
      currentContent = `<p>${tagText}</p>`;
    } else {
      // 如果内容不为空，在最后一个</p>前添加标签文字
      const lastClosingPTag = currentContent.lastIndexOf('</p>');

      if (lastClosingPTag !== -1) {
        // 找到最后一个</p>标签的位置
        const beforeClosingTag = currentContent.substring(0, lastClosingPTag);
        const afterClosingTag = currentContent.substring(lastClosingPTag);

        // 在最后一个</p>标签前添加标签文字
        currentContent = beforeClosingTag + tagText + afterClosingTag;
      } else {
        // 如果没有</p>标签，直接添加到末尾
        currentContent += tagText;
      }
    }

    // 更新内容
    const updatedItems = otherItems.map(other =>
      other.id === item.id ? {...other, desc: currentContent} : other
    );
    setOtherItems(updatedItems);
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-4 bg-w">
      <div
        className="flex items-center justify-between w-full text-[14px] text-[#2E2F66] bg-white p-4 rounded-md"
        {...attributes}
      >
        <div className="flex items-center gap-3">
          {isEditing ? (
            <div className="flex items-center gap-2">
              <input
                type="text"
                value={editName}
                onChange={handleInputChange}
                onKeyDown={handleInputKeyDown}
                className="w-[137px] px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-200"
                autoFocus
              />
              <button
                onClick={handleConfirmEdit}
                className="p-1 text-green-500 hover:text-green-600"
                title="确认"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </button>
              <button
                onClick={handleCancelEdit}
                className="p-1 text-red-500 hover:text-red-600"
                title="取消"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
          ) : (
            <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={item.name}>
              {item.name}
            </div>
          )}
        </div>
        <div className="flex gap-5 items-center">
          <div
            className={cn(
              "cursor-grab active:cursor-grabbing p-1 rounded-md transition-colors",
              "hover:bg-gray-100 hover:text-primary"
            )}
            {...listeners}
            title="拖拽排序"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
            </svg>
          </div>
          {/* 编辑按钮 */}
          <div
            className="cursor-pointer hover:text-primary"
            onClick={handleEditClick}
            title="编辑标题"
          >
            <Image src="/assets/svg/edit-icon.svg" alt="编辑" width={20} height={20} />
          </div>
          {/* 删除按钮 */}
          <div
            className={`cursor-pointer ${otherItems.length <= 1 ? 'opacity-50' : 'hover:text-red-500'}`}
            onClick={(e) => {
              e.stopPropagation();
              if (otherItems.length > 1) {
                deleteOtherItem(item.id);
              }
            }}
            title={otherItems.length <= 1 ? "至少保留一个项目" : "删除此项目"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M6.7251 8.77979C7.00124 8.77979 7.2251 9.00364 7.2251 9.27979V14.7341H12.7746V9.27979C12.7746 9.00364 12.9985 8.77979 13.2746 8.77979C13.5508 8.77979 13.7746 9.00364 13.7746 9.27979V15.2341C13.7746 15.5103 13.5508 15.7341 13.2746 15.7341H6.7251C6.44896 15.7341 6.2251 15.5103 6.2251 15.2341V9.27979C6.2251 9.00364 6.44896 8.77979 6.7251 8.77979Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M4.76807 6.78955C4.76807 6.51341 4.99192 6.28955 5.26807 6.28955L14.732 6.28955C15.0082 6.28955 15.232 6.51341 15.232 6.78955C15.232 7.06569 15.0082 7.28955 14.732 7.28955L5.26807 7.28955C4.99192 7.28955 4.76807 7.06569 4.76807 6.78955Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M7.76147 4.76611C7.76147 4.48997 7.98533 4.26611 8.26147 4.26611H11.7384C12.0145 4.26611 12.2384 4.48997 12.2384 4.76611C12.2384 5.04226 12.0145 5.26611 11.7384 5.26611H8.26147C7.98533 5.26611 7.76147 5.04226 7.76147 4.76611Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M8.91357 8.95068C9.18972 8.95068 9.41357 9.17454 9.41357 9.45068V13.1917C9.41357 13.4679 9.18972 13.6917 8.91357 13.6917C8.63743 13.6917 8.41357 13.4679 8.41357 13.1917V9.45068C8.41357 9.17454 8.63743 8.95068 8.91357 8.95068Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M11.0864 8.95068C11.3626 8.95068 11.5864 9.17454 11.5864 9.45068V13.1917C11.5864 13.4679 11.3626 13.6917 11.0864 13.6917C10.8103 13.6917 10.5864 13.4679 10.5864 13.1917V9.45068C10.5864 9.17454 10.8103 8.95068 11.0864 8.95068Z" fill="currentColor"></path>
            </svg>
          </div>
          {/* 展开/折叠按钮 */}
          <div
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              toggleExpanded(item.id);
            }}
            style={{ transform: expandedItems[item.id] ? 'rotate(180deg)' : 'rotate(0deg)' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M4.99805 7.85151L5.825 7L9.99805 11.297L14.1711 7L14.998 7.85151L9.99805 13L4.99805 7.85151Z" fill="#707191"></path>
            </svg>
          </div>
        </div>
      </div>

      {expandedItems[item.id] && (
        <div className="pt-[22px]" style={{ transition: 'max-height 0.3s' }}>
          <form className="grid grid-cols-2 gap-6" onSubmit={(e) => e.preventDefault()}>
            {/* 内容编辑器 - 占用整行 */}
            <div className="col-span-2">
              <div className="space-y-2">
                <MarkdownEditor
                  value={item.desc}
                  onChange={(value) => {
                    const updatedItems = otherItems.map(other =>
                      other.id === item.id ? {...other, desc: value} : other
                    );
                    setOtherItems(updatedItems);
                  }}
                  height={200}
                  placeholder="请在此输入内容..."
                />
              </div>
            </div>

            {/* 如果标题为"技能"，显示技能标签 */}
            {item.name === "技能" && (
              <div className="col-span-2">
              <div className="flex flex-wrap items-center gap-[6px] pt-0" style={{ transition: 'max-height 0.3s' }}>
                <div
                  title="Office"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("Office")}
                >Office</div>
                <div
                  title="PPT"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("PPT")}
                >PPT</div>
                <div
                  title="Excel(熟练公式运用)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("Excel(熟练公式运用)")}
                >Excel(熟练公式运用)</div>
                <div
                  title="Word"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("Word")}
                >Word</div>
                <div
                  title="文案撰写(累计超过10W字)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("文案撰写(累计超过10W字)")}
                >文案撰写(累计超过10W字)</div>
                <div
                  title="数据分析"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("数据分析")}
                >数据分析</div>
                <div
                  title="音乐"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("音乐")}
                >音乐</div>
                <div
                  title="舞蹈"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("舞蹈")}
                >舞蹈</div>
              </div>
              </div>
            )}

            {/* 如果标题为"语言"，显示语言标签 */}
            {item.name === "语言" && (
              <div className="col-span-2">
              <div className="flex flex-wrap items-center gap-[6px] pt-0" style={{ transition: 'max-height 0.3s' }}>
                <div
                  title="普通话"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("普通话")}
                >普通话</div>
                <div
                  title="英语CET4"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("英语CET4")}
                >英语CET4</div>
                <div
                  title="日语"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("日语")}
                >日语</div>
                <div
                  title="普通话二甲"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("普通话二甲")}
                >普通话二甲</div>
                <div
                  title="普通话一乙"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("普通话一乙")}
                >普通话一乙</div>
                <div
                  title="普通话一甲"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("普通话一甲")}
                >普通话一甲</div>
                <div
                  title="英语CET6"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("英语CET6")}
                >英语CET6</div>
                <div
                  title="英语(日常交流)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("英语(日常交流)")}
                >英语(日常交流)</div>
                <div
                  title="韩语"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("韩语")}
                >韩语</div>
                <div
                  title="德语"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("德语")}
                >德语</div>
                <div
                  title="法语"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("法语")}
                >法语</div>
                <div
                  title="俄语"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("俄语")}
                >俄语</div>
                <div
                  title="粤语"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("粤语")}
                >粤语</div>
                <div
                  title="吴语(上海话)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("吴语(上海话)")}
                >吴语(上海话)</div>
                <div
                  title="英语专业四级"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("英语专业四级")}
                >英语专业四级</div>
                <div
                  title="西班牙语"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("西班牙语")}
                >西班牙语</div>
                <div
                  title="英语专业八级"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("英语专业八级")}
                >英语专业八级</div>
                <div
                  title="阿拉伯语"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("阿拉伯语")}
                >阿拉伯语</div>
              </div>
              </div>
            )}

            {/* 如果标题为"兴趣爱好"，显示兴趣爱好标签 */}
            {item.name === "兴趣爱好" && (
              <div className="col-span-2">
              <div className="flex flex-wrap items-center gap-[6px] pt-0" style={{ transition: 'max-height 0.3s' }}>
                <div
                  title="唱歌"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("唱歌")}
                >唱歌</div>
                <div
                  title="舞蹈"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("舞蹈")}
                >舞蹈</div>
                <div
                  title="RAP"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("RAP")}
                >RAP</div>
                <div
                  title="篮球"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("篮球")}
                >篮球</div>
                <div
                  title="看电影"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("看电影")}
                >看电影</div>
                <div
                  title="读书"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("读书")}
                >读书</div>
                <div
                  title="旅游"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("旅游")}
                >旅游</div>
                <div
                  title="citywalk"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("citywalk")}
                >citywalk</div>
                <div
                  title="探店"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("探店")}
                >探店</div>
                <div
                  title="美食"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("美食")}
                >美食</div>
                <div
                  title="单车"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("单车")}
                >单车</div>
                <div
                  title="游泳"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("游泳")}
                >游泳</div>
                <div
                  title="跑步"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("跑步")}
                >跑步</div>
                <div
                  title="羽毛球"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("羽毛球")}
                >羽毛球</div>
                <div
                  title="乒乓球"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("乒乓球")}
                >乒乓球</div>
                <div
                  title="桌球"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("桌球")}
                >桌球</div>
                <div
                  title="网球"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("网球")}
                >网球</div>
                <div
                  title="游戏"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("游戏")}
                >游戏</div>
              </div>
              </div>
            )}

            {/* 如果标题为"证书"，显示证书标签 */}
            {item.name === "证书" && (
              <div className="col-span-2">
                <div className="flex flex-wrap items-center gap-[6px] pt-0" style={{ transition: 'max-height 0.3s' }}>
                <div
                  title="CFA"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("CFA")}
                >CFA</div>
                <div
                  title="CFA一级"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("CFA一级")}
                >CFA一级</div>
                <div
                  title="CFA二级"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("CFA二级")}
                >CFA二级</div>
                <div
                  title="项目管理PMP"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("项目管理PMP")}
                >项目管理PMP</div>
                <div
                  title="注册会计师CPA"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("注册会计师CPA")}
                >注册会计师CPA</div>
                <div
                  title="特许公认会计师ACCA"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("特许公认会计师ACCA")}
                >特许公认会计师ACCA</div>
                <div
                  title="软件设计师"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("软件设计师")}
                >软件设计师</div>
                <div
                  title="证券从业资格证"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("证券从业资格证")}
                >证券从业资格证</div>
                <div
                  title="会计从业资格证"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("会计从业资格证")}
                >会计从业资格证</div>
                <div
                  title="基金从业资格证"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("基金从业资格证")}
                >基金从业资格证</div>
                <div
                  title="金融风险管理师FRM"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("金融风险管理师FRM")}
                >金融风险管理师FRM</div>
                <div
                  title="银行从业资格证"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("银行从业资格证")}
                >银行从业资格证</div>
                <div
                  title="工程师认证CAD"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("工程师认证CAD")}
                >工程师认证CAD</div>
                <div
                  title="法律职业资格证"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("法律职业资格证")}
                >法律职业资格证</div>
                <div
                  title="造价工程师"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("造价工程师")}
                >造价工程师</div>
                <div
                  title="注册建筑师"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("注册建筑师")}
                >注册建筑师</div>
                <div
                  title="人力资源从业资格证"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("人力资源从业资格证")}
                >人力资源从业资格证</div>
                <div
                  title="教师资格证"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("教师资格证")}
                >教师资格证</div>
                <div
                  title="心理咨询师"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("心理咨询师")}
                >心理咨询师</div>
                <div
                  title="执业医师"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("执业医师")}
                >执业医师</div>
                <div
                  title="导游证"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("导游证")}
                >导游证</div>
                <div
                  title="计算机二级"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("计算机二级")}
                >计算机二级</div>
                <div
                  title="驾驶证"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("驾驶证")}
                >驾驶证</div>
              </div>
              </div>
            )}

            {/* 如果标题为"荣誉"，显示荣誉标签 */}
            {item.name === "荣誉" && (
              <div className="col-span-2">
                <div className="flex flex-wrap items-center gap-[6px] pt-0" style={{ transition: 'max-height 0.3s' }}>
                <div
                  title="励志奖学金(国家级，3次)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("励志奖学金(国家级，3次)")}
                >励志奖学金(国家级，3次)</div>
                <div
                  title="xx名人奖学金(校级)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("xx名人奖学金(校级)")}
                >xx名人奖学金(校级)</div>
                <div
                  title="校园十佳歌手(第3名)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("校园十佳歌手(第3名)")}
                >校园十佳歌手(第3名)</div>
                <div
                  title="优秀干部(连续3年)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("优秀干部(连续3年)")}
                >优秀干部(连续3年)</div>
                <div
                  title="优秀员工奖(30选1)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("优秀员工奖(30选1)")}
                >优秀员工奖(30选1)</div>
                <div
                  title="优秀团体奖(8选1)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("优秀团体奖(8选1)")}
                >优秀团体奖(8选1)</div>
                <div
                  title="优秀毕业生 (2023届)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("优秀毕业生 (2023届)")}
                >优秀毕业生 (2023届)</div>
                <div
                  title="优秀干事"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("优秀干事")}
                >优秀干事</div>
              </div>
              </div>
            )}

            {/* 如果标题为"论文"，显示论文标签 */}
            {item.name === "论文" && (
              <div className="col-span-2">
                <div className="flex flex-wrap items-center gap-[6px] pt-0" style={{ transition: 'max-height 0.3s' }}>
                <div
                  title="《论文名称》 (校级优秀论文)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("《论文名称》 (校级优秀论文)")}
                >《论文名称》 (校级优秀论文)</div>
                <div
                  title="《论文名称》 (SCI.第一作者)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("《论文名称》 (SCI.第一作者)")}
                >《论文名称》 (SCI.第一作者)</div>
                <div
                  title="李晓,张三,基于深度学习的图像分类方法研究I.计算机科学与技术,2020,35(3):456-467.doi:10.1007/s11390-020-9801-2."
                  className="tag-item truncate"
                  onClick={() => handleTagClick("李晓,张三,基于深度学习的图像分类方法研究I.计算机科学与技术,2020,35(3):456-467.doi:10.1007/s11390-020-9801-2.")}
                >李晓,张三,基于深度学习的图像分类方法研究I.计算机科学与技术,2020,35(3):456-467.doi:10.1007/s11390-020-9801-2.</div>
              </div>
              </div>
            )}

            {/* 如果标题为"专利"，显示专利标签 */}
            {item.name === "专利" && (
              <div className="col-span-2">
                <div className="flex flex-wrap items-center gap-[6px] pt-0" style={{ transition: 'max-height 0.3s' }}>
                <div
                  title="一种辅助简历创作的语言算法(发明型)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("一种辅助简历创作的语言算法(发明型)")}
                >一种辅助简历创作的语言算法(发明型)</div>
                <div
                  title="一种机器狗(国家级专利奖项)"
                  className="tag-item truncate"
                  onClick={() => handleTagClick("一种机器狗(国家级专利奖项)")}
                >一种机器狗(国家级专利奖项)</div>
                <div
                  title="贝尔A.G沃森T改善电报[P].美国: US 174465,1876-03-07."
                  className="tag-item truncate"
                  onClick={() => handleTagClick("贝尔A.G沃森T改善电报[P].美国: US 174465,1876-03-07.")}
                >贝尔A.G沃森T改善电报[P].美国: US 174465,1876-03-07.</div>
                <div
                  title="瓦特J.一种改进的蒸汽机[P].英国: GB1769/0001,1769-01-05."
                  className="tag-item truncate"
                  onClick={() => handleTagClick("瓦特J.一种改进的蒸汽机[P].英国: GB1769/0001,1769-01-05.")}
                >瓦特J.一种改进的蒸汽机[P].英国: GB1769/0001,1769-01-05.</div>
              </div>
              </div>
            )}
          </form>
        </div>
      )}
    </div>
  );
};

export default function Other() {
  // 定义提示项
  const tipItems = [
    {
      question: '哪些写，哪些不写？',
      answer: '跟所申请岗位相关的就写，不相关的可以不写。\n例如：\n申请金融行业工作可以填写注册会计师、CFA Charter Holder、基金从业资格证等\n金融行业会偏向量化的技能,如编程语言(金融行业比较喜好的 Python/R等)、数据库(Bloomberg/万德等)。'
    },
    {
      question: '技能/爱好/语言只是简单罗列吗？',
      answer: '可以加括号描述这项程度。\n示例：\nExcel（熟练掌握公式）、PPT（会做模版）、日语（不带字幕看剧）、英语（雅思7.5）\n钢琴(8 级)、篮球(校队中锋)、登山(10 座海拔 > 3 千米的山)、摄影(作品集:example.com)等。'
    },
    {
      question: '千万不要这么写兴趣爱好！',
      answer: '兴趣爱好也是突出亮点的地方，而不是告诉企业你周末是喜欢看韩剧还是美剧。\n正确示例：读书(一年阅读超过 N 本书)、运动（每天5km）。'
    }
  ];

  // 从store中获取other模块数据
  const { modules, updateModuleItem, activeIndex, setActiveIndex } = useModuleStore();
  const otherModule = modules['other'];

  // 使用ref来跟踪是否是本地状态更新导致的变化
  const isLocalUpdate = React.useRef(false);
  // 使用ref来跟踪是否是首次渲染
  const isFirstRender = React.useRef(true);

  // 转换store数据格式为本地格式
  const convertStoreToLocal = (storeItems: StoreOtherItem[]): OtherItem[] => {
    if (!Array.isArray(storeItems)) return [];

    return storeItems.map(item => ({
      id: item.id,
      name: typeof item.name === 'object' ? item.name.value : item.name,
      desc: typeof item.desc === 'object' ? item.desc.value : item.desc,
      index: item.index
    }));
  };

  // 转换本地数据格式为store格式
  const convertLocalToStore = (localItems: OtherItem[]): StoreOtherItem[] => {
    return localItems.map(item => ({
      id: item.id,
      name: { label: "项目名称", value: item.name },
      desc: { label: "项目描述", value: item.desc },
      index: item.index
    }));
  };

  // 使用store中的数据，如果没有则使用示例数据
  const [otherItems, setOtherItems] = useState<OtherItem[]>(
    convertStoreToLocal(otherModule?.item as StoreOtherItem[])
  );

  // 初始化：如果store中没有数据，则创建默认的空项目
  useEffect(() => {
    if (otherModule && (!otherModule.item || !Array.isArray(otherModule.item) || otherModule.item.length === 0)) {
      // 创建默认的空其他项目
      const defaultOtherItems: OtherItem[] = [
        {
          id: 'other-1',
          name: '技能',
          desc: '',
          index: 0
        }
      ];

      // 标记为本地更新
      isLocalUpdate.current = true;
      // 将默认数据转换为store格式并更新
      const storeData = convertLocalToStore(defaultOtherItems);
      updateModuleItem('other', storeData as unknown as Record<string, unknown>);
      // 重置标记
      setTimeout(() => {
        isLocalUpdate.current = false;
      }, 0);

      // 确保本地状态也使用默认数据
      setOtherItems(defaultOtherItems);
    }
  }, [otherModule, updateModuleItem]);

  // 当store中的数据变化时，更新本地状态，但仅当不是由本地更新触发的
  useEffect(() => {
    if (!isLocalUpdate.current && otherModule?.item) {
      if (Array.isArray(otherModule.item) && otherModule.item.length > 0) {
        // 转换store数据为本地格式
        const localData = convertStoreToLocal(otherModule.item as StoreOtherItem[]);
        setOtherItems(localData);
      } else {
        // 如果store中的数据不是数组或为空，则使用空数组
        setOtherItems([]);
      }
    }
  }, [otherModule?.item]);

  // 当本地状态变化时，更新store，但需要防止无限循环
  useEffect(() => {
    // 如果是首次渲染，标记为非首次渲染并返回
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // 仅当不是从store更新本地状态时才更新store
    if (otherModule && !isLocalUpdate.current) {
      // 标记为本地更新
      isLocalUpdate.current = true;
      // 将本地数据转换为store格式并更新
      const storeData = convertLocalToStore(otherItems);
      updateModuleItem('other', storeData as unknown as Record<string, unknown>);
      // 重置标记
      setTimeout(() => {
        isLocalUpdate.current = false;
      }, 0);
    }
  }, [otherItems, otherModule, updateModuleItem]);

  // 展开/折叠状态 - 使用对象来跟踪每个项目的展开状态
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(() => {
    // 根据 activeIndex 数组确定默认展开的项目
    if (activeIndex.length > 0 && otherItems.length > 0) {
      const initialExpanded: Record<string, boolean> = {};
      activeIndex.forEach(index => {
        if (otherItems[index]) {
          initialExpanded[otherItems[index].id] = true;
        }
      });
      return initialExpanded;
    }
    // 如果没有 activeIndex 或对应项目不存在，默认展开第一个项目
    return otherItems.length > 0 ? { [otherItems[0].id]: true } : {};
  });

  // 监听 activeIndex 变化，更新展开状态
  useEffect(() => {
    if (activeIndex.length > 0 && otherItems.length > 0) {
      // 根据 activeIndex 数组展开对应的项目
      const newExpandedItems: Record<string, boolean> = {};
      activeIndex.forEach(index => {
        if (otherItems[index]) {
          newExpandedItems[otherItems[index].id] = true;
        }
      });
      setExpandedItems(newExpandedItems);
    }
  }, [activeIndex, otherItems]);

  // 拖拽状态
  const [activeId, setActiveId] = useState<string | null>(null);

  // 获取当前拖拽的项目
  const activeItem = activeId ? otherItems.find(item => item.id === activeId) : null;

  // 切换特定项目的展开/折叠状态
  const toggleExpanded = (itemId: string) => {
    const itemIndex = otherItems.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    // 先更新展开状态
    setExpandedItems(prev => {
      const newExpanded = {
        ...prev,
        [itemId]: !prev[itemId]
      };

      // 在下一个事件循环中更新 activeIndex，避免在渲染过程中调用 setState
      setTimeout(() => {
        const currentActiveIndex = [...activeIndex];
        if (newExpanded[itemId]) {
          // 展开：添加到 activeIndex
          if (!currentActiveIndex.includes(itemIndex)) {
            currentActiveIndex.push(itemIndex);
            setActiveIndex(currentActiveIndex);
          }
        } else {
          // 折叠：从 activeIndex 移除
          const indexToRemove = currentActiveIndex.indexOf(itemIndex);
          if (indexToRemove > -1) {
            currentActiveIndex.splice(indexToRemove, 1);
            setActiveIndex(currentActiveIndex);
          }
        }
      }, 0);

      return newExpanded;
    });
  };

  // 添加新的项目
  const addOtherItem = () => {
    // 确保 otherItems 是数组
    const safeItems = Array.isArray(otherItems) ? otherItems : [];

    // 生成唯一ID
    const newId = `other-${Date.now()}`;

    // 创建新的项目
    const newOtherItem: OtherItem = {
      id: newId,
      name: '自定义',
      desc: '',
      index: safeItems.length // 设置索引为当前列表长度
    };

    // 添加到列表中
    setOtherItems([...safeItems, newOtherItem]);

    // 先关闭所有已展开的项目，然后只展开新添加的项目
    setExpandedItems({
      [newId]: true
    });
  };

  // 删除项目
  const deleteOtherItem = (itemId: string) => {
    // 确保 otherItems 是数组
    if (!Array.isArray(otherItems)) {
      return;
    }

    // 如果只剩下一个项目，不允许删除
    if (otherItems.length <= 1) {
      return;
    }

    // 从列表中移除项目
    const updatedItems = otherItems.filter(item => item.id !== itemId);

    // 更新状态
    setOtherItems(updatedItems);

    // 从展开状态中移除该项目
    const newExpandedItems = { ...expandedItems };
    delete newExpandedItems[itemId];
    setExpandedItems(newExpandedItems);
  };

  // 确保 otherItems 是一个数组
  const safeOtherItems = Array.isArray(otherItems) ? otherItems : [];



  return (
    <div className="h-full">
      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="其他怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                填写简历的核心思路就是——扬长避短，突出自己和岗位的匹配度。面试或者简历上有缺点没关系，反而更真实，但不要暴露硬伤。
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 使用 ScrollableContent 组件 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              id="other"
              icon={<Image src="/image/other.svg" alt="其他" width={24} height={24} />}
              title="其他"
              description="你可以在这里罗列你所掌握的技能、语言、证书，个人的兴趣爱好，以凸显你的能力💪"
            />

            {/* 表单内容区域 */}
            <div className="p-6 space-y-6 bg-[#f7f8fa] rounded-md">
              {/* 其他信息列表 */}
              <div className="flex-1">
                {/* 使用DndContext包装其他信息列表 */}
                <DndContext
                  sensors={useSensors(
                    useSensor(PointerSensor, {
                      activationConstraint: {
                        distance: 2, // 只需要拖动2px就会触发拖拽
                        tolerance: 3, // 增加容差，使拖拽更容易触发
                        delay: 0, // 无延迟
                      },
                    }),
                    useSensor(KeyboardSensor, {
                      coordinateGetter: sortableKeyboardCoordinates,
                    })
                  )}
                  collisionDetection={closestCenter}
                  onDragStart={(event) => {
                    const { active } = event;
                    setActiveId(active.id as string);
                  }}
                  onDragEnd={(event) => {
                    const { active, over } = event;

                    if (over && active.id !== over.id && Array.isArray(otherItems)) {
                      // 获取排序后的数组
                      const oldIndex = otherItems.findIndex(item => item.id === active.id);
                      const newIndex = otherItems.findIndex(item => item.id === over.id);

                      // 确保索引有效
                      if (oldIndex !== -1 && newIndex !== -1) {
                        // 更新项目的顺序
                        const updatedItems = [...otherItems];
                        const [movedItem] = updatedItems.splice(oldIndex, 1);
                        updatedItems.splice(newIndex, 0, movedItem);

                        // 更新所有项目的索引
                        const reindexedItems = updatedItems.map((item, index) => ({
                          ...item,
                          index
                        }));

                        // 更新状态
                        setOtherItems(reindexedItems);
                      }
                    }

                    // 重置拖拽状态
                    setActiveId(null);
                  }}
                  measuring={{
                    droppable: {
                      strategy: MeasuringStrategy.Always,
                    },
                  }}
                >
                  <SortableContext
                    items={safeOtherItems.map(item => item.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    {safeOtherItems.map((item) => (
                      <SortableOtherItem
                        key={item.id}
                        item={item}
                        expandedItems={expandedItems}
                        toggleExpanded={toggleExpanded}
                        deleteOtherItem={deleteOtherItem}
                        otherItems={safeOtherItems}
                        setOtherItems={setOtherItems}
                      />
                    ))}
                  </SortableContext>

                  {/* 拖拽预览 */}
                  <DragOverlay>
                    {activeItem ? (
                      <div className="flex items-center justify-between w-full text-[14px] text-[#2E2F66] bg-white p-4 rounded-md shadow-lg border-2 border-purple-200">
                        <div className="flex items-center gap-3">
                          <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={activeItem.name}>
                            {activeItem.name || '未填写名称'}
                          </div>
                        </div>
                        <div className="flex gap-5 items-center">
                          <div className={cn(
                            "cursor-grab p-1 rounded-md bg-gray-100 text-primary"
                          )}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
                              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
                            </svg>
                          </div>
                          {/* 编辑图标 */}
                          <div className="p-1">
                            <Image src="/assets/svg/edit-icon.svg" alt="编辑" width={20} height={20} />
                          </div>
                        </div>
                      </div>
                    ) : null}
                  </DragOverlay>
                </DndContext>
              </div>
            </div>

            {/* 添加按钮和跳转器 */}
            <div className="mt-6 flex justify-between items-center">
              <button
                type="button"
                onClick={addOtherItem}
                className="flex items-center justify-center gap-2 py-2 px-5 rounded-md transition-all bg-white text-[#333] border border-gray-200 hover:bg-gray-50 active:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 cursor-pointer shadow-sm whitespace-nowrap"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 4V12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M4 8H12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
                <span className="text-base font-medium">添加其他</span>
              </button>

              <ModuleNavigator currentModuleId="other" />
            </div>
          </ScrollableContent>
        </div>
      </div>
    </div>
  );
}
