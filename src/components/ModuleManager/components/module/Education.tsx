'use client';

import React, { useState, useEffect } from 'react';
import Tip from '../Tip';
import ModuleNavigator from '@/components/ModuleNavigator';
import ScrollableContent from '../ScrollableContent';
import ModuleHeader from '@/components/ModuleHeader';
import Image from 'next/image';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
// 不再需要 Checkbox 组件
import { cn } from '@/lib/utils'; // 用于条件类名
import MarkdownEditor from '@/components/MarkdownEditor';
import AIGenerateButtons from '@/components/AIGenerateButtons';
import YearMonthPicker from '@/components/YearMonthPicker';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  MeasuringStrategy,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { useModuleStore, type EducationItem as StoreEducationItem } from '@/store/useModuleStore';
import ItemDeleteConfirmDialog from '../ItemDeleteConfirmDialog';

// 本地教育经历项目类型（用于表单状态）
interface EducationItem {
  id: string;
  school_name: string;
  college_name: string;
  major: string;
  degree: string;
  city: string;
  start_date: string;
  end_date: string;
  description: string;
  school_tags: string[];
  index: number;
}



// 可排序的教育经历项组件
const SortableEducationItem = ({
  item,
  expandedItems,
  toggleExpanded,
  deleteEducationItem,
  educationItems,
  setEducationItems,
  customTagInputs,
  handleCustomTagInput,
  showCustomTagInput,
  setCustomTagInputs
}: {
  item: EducationItem;
  expandedItems: Record<string, boolean>;
  toggleExpanded: (itemId: string) => void;
  deleteEducationItem: (itemId: string) => void;
  educationItems: EducationItem[];
  setEducationItems: React.Dispatch<React.SetStateAction<EducationItem[]>>;
  customTagInputs: Record<string, {isEditing: boolean, value: string}>;
  handleCustomTagInput: (itemId: string, value: string) => void;
  showCustomTagInput: (itemId: string) => void;
  setCustomTagInputs: React.Dispatch<React.SetStateAction<Record<string, {isEditing: boolean, value: string}>>>;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 'auto',
    position: 'relative' as const,
    touchAction: 'none',
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-4 bg-w">
      <div
        className="flex items-center justify-between w-full text-[14px] text-[#2E2F66] bg-white p-4 rounded-md"
        {...attributes}
      >
        <div className="flex items-center gap-3">
          <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={item.school_name}>
            {item.school_name}
          </div>
        </div>
        <div>
          {item.start_date} <span>- {item.end_date}</span>
        </div>
        <div className="flex gap-5 items-center">
          <div
            className={cn(
              "cursor-grab active:cursor-grabbing p-1 rounded-md transition-colors",
              "hover:bg-gray-100 hover:text-primary"
            )}
            {...listeners}
            title="拖拽排序"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
            </svg>
          </div>
          <div
            className={`cursor-pointer ${educationItems.length <= 1 ? 'opacity-50' : 'hover:text-red-500'}`}
            onClick={(e) => {
              e.stopPropagation();
              if (educationItems.length > 1) {
                deleteEducationItem(item.id);
              }
            }}
            title={educationItems.length <= 1 ? "至少保留一个教育经历" : "删除此教育经历"}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M6.7251 8.77979C7.00124 8.77979 7.2251 9.00364 7.2251 9.27979V14.7341H12.7746V9.27979C12.7746 9.00364 12.9985 8.77979 13.2746 8.77979C13.5508 8.77979 13.7746 9.00364 13.7746 9.27979V15.2341C13.7746 15.5103 13.5508 15.7341 13.2746 15.7341H6.7251C6.44896 15.7341 6.2251 15.5103 6.2251 15.2341V9.27979C6.2251 9.00364 6.44896 8.77979 6.7251 8.77979Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M4.76807 6.78955C4.76807 6.51341 4.99192 6.28955 5.26807 6.28955L14.732 6.28955C15.0082 6.28955 15.232 6.51341 15.232 6.78955C15.232 7.06569 15.0082 7.28955 14.732 7.28955L5.26807 7.28955C4.99192 7.28955 4.76807 7.06569 4.76807 6.78955Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M7.76147 4.76611C7.76147 4.48997 7.98533 4.26611 8.26147 4.26611H11.7384C12.0145 4.26611 12.2384 4.48997 12.2384 4.76611C12.2384 5.04226 12.0145 5.26611 11.7384 5.26611H8.26147C7.98533 5.26611 7.76147 5.04226 7.76147 4.76611Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M8.91357 8.95068C9.18972 8.95068 9.41357 9.17454 9.41357 9.45068V13.1917C9.41357 13.4679 9.18972 13.6917 8.91357 13.6917C8.63743 13.6917 8.41357 13.4679 8.41357 13.1917V9.45068C8.41357 9.17454 8.63743 8.95068 8.91357 8.95068Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M11.0864 8.95068C11.3626 8.95068 11.5864 9.17454 11.5864 9.45068V13.1917C11.5864 13.4679 11.3626 13.6917 11.0864 13.6917C10.8103 13.6917 10.5864 13.4679 10.5864 13.1917V9.45068C10.5864 9.17454 10.8103 8.95068 11.0864 8.95068Z" fill="currentColor"></path>
            </svg>
          </div>
          <div
            className="cursor-pointer"
            onClick={(e) => {
              e.stopPropagation();
              toggleExpanded(item.id);
            }}
            style={{ transform: expandedItems[item.id] ? 'rotate(180deg)' : 'rotate(0deg)' }}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M4.99805 7.85151L5.825 7L9.99805 11.297L14.1711 7L14.998 7.85151L9.99805 13L4.99805 7.85151Z" fill="#707191"></path>
            </svg>
          </div>
        </div>
      </div>

      {expandedItems[item.id] && (
        <div className="pt-[22px]" style={{ transition: 'max-height 0.3s' }}>
          <form className="grid grid-cols-2 gap-6" onSubmit={(e) => e.preventDefault()}>
            {/* 学校 */}
            <div className="col-span-2">
              <div className="space-y-2">
                <Label htmlFor={`school_name_${item.id}`}>学校</Label>
                <Input
                  id={`school_name_${item.id}`}
                  type="text"
                  placeholder="输入校名全称，如：北京大学"
                  value={item.school_name}
                  onChange={(e) => {
                    const updatedItems = educationItems.map(edu =>
                      edu.id === item.id ? {...edu, school_name: e.target.value} : edu
                    );
                    setEducationItems(updatedItems);
                  }}
                  className="bg-white"
                />
                <div className="flex flex-wrap gap-2 mt-2">
                  {/* 渲染所有标签，包括预设标签和自定义标签 */}
                  {item.school_tags.map((tag) => (
                    <button
                      key={tag}
                      type="button"
                      onClick={() => {
                        // 移除标签
                        const updatedTags = item.school_tags.filter(t => t !== tag);
                        const updatedItems = educationItems.map(edu =>
                          edu.id === item.id ? {...edu, school_tags: updatedTags} : edu
                        );
                        setEducationItems(updatedItems);
                      }}
                      className="px-3 py-1 text-sm rounded-md border border-primary bg-primary text-white transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 cursor-pointer"
                    >
                      {tag}
                    </button>
                  ))}

                  {/* 渲染未选中的预设标签 */}
                  {['985', '211', '双一流', '海外QS前100'].filter(tag => !item.school_tags.includes(tag)).map((tag) => (
                    <button
                      key={tag}
                      type="button"
                      onClick={() => {
                        // 添加标签
                        const updatedTags = [...item.school_tags, tag];
                        const updatedItems = educationItems.map(edu =>
                          edu.id === item.id ? {...edu, school_tags: updatedTags} : edu
                        );
                        setEducationItems(updatedItems);
                      }}
                      className="px-3 py-1 text-sm rounded-md border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-purple-200 cursor-pointer"
                    >
                      {tag}
                    </button>
                  ))}
                  {customTagInputs[item.id]?.isEditing ? (
                    <div className="relative flex">
                      <Input
                        type="text"
                        placeholder="自定义"
                        value={customTagInputs[item.id]?.value || ''}
                        onChange={(e) => {
                          // 限制输入长度为20个字符
                          if (e.target.value.length <= 20) {
                            handleCustomTagInput(item.id, e.target.value);
                          }
                        }}
                        className="pl-3 pr-3 py-1 text-sm rounded-l-md border border-primary bg-white focus:ring-2 focus:ring-purple-200 focus:outline-none w-[100px] h-[26px] min-h-[26px] leading-tight"
                        onKeyDown={(e) => {
                          if (e.key === 'Enter') {
                            e.preventDefault();
                            const customTag = customTagInputs[item.id]?.value.trim();

                            if (customTag && customTag.length > 0) {
                              // 检查标签是否已存在
                              const tagExists = item.school_tags.includes(customTag);

                              if (!tagExists) {
                                // 直接更新教育项目的标签
                                const updatedItems = [...educationItems];
                                const itemIndex = updatedItems.findIndex(edu => edu.id === item.id);

                                if (itemIndex !== -1) {

                                  const newTags = [...updatedItems[itemIndex].school_tags, customTag];

                                  updatedItems[itemIndex] = {
                                    ...updatedItems[itemIndex],
                                    school_tags: newTags
                                  };


                                  // 强制更新状态
                                  setEducationItems([...updatedItems]);
                                }
                              }

                              // 重置输入状态
                              setCustomTagInputs((prev) => ({
                                ...prev,
                                [item.id]: {
                                  isEditing: false,
                                  value: ''
                                }
                              }));
                            }
                          } else if (e.key === 'Escape') {
                            setCustomTagInputs((prev) => ({
                              ...prev,
                              [item.id]: {
                                isEditing: false,
                                value: ''
                              }
                            }));
                          }
                        }}
                        autoFocus
                      />

                      <button
                        type="button"
                        className="px-2 py-1 text-sm rounded-r-md border border-l-0 border-primary bg-primary text-white hover:bg-purple-600 transition-colors h-[26px] min-h-[26px] leading-tight cursor-pointer"
                        onClick={(e) => {
                          e.preventDefault(); // 防止表单提交
                          e.stopPropagation(); // 防止事件冒泡

                          const customTag = customTagInputs[item.id]?.value.trim();

                          if (customTag && customTag.length > 0) {
                            // 检查标签是否已存在
                            const tagExists = item.school_tags.includes(customTag);

                            if (!tagExists) {
                              // 直接更新教育项目的标签
                              const updatedItems = [...educationItems];
                              const itemIndex = updatedItems.findIndex(edu => edu.id === item.id);

                              if (itemIndex !== -1) {

                                const newTags = [...updatedItems[itemIndex].school_tags, customTag];

                                updatedItems[itemIndex] = {
                                  ...updatedItems[itemIndex],
                                  school_tags: newTags
                                };


                                // 强制更新状态
                                setEducationItems([...updatedItems]);
                              }
                            }

                            // 重置输入状态
                            setCustomTagInputs((prev) => ({
                              ...prev,
                              [item.id]: {
                                isEditing: false,
                                value: ''
                              }
                            }));
                          }
                        }}
                      >
                        确定
                      </button>
                    </div>
                  ) : (
                    <button
                      type="button"
                      className="flex items-center gap-2 px-3 py-1 text-sm rounded-md border border-gray-200 bg-white text-gray-700 hover:bg-gray-50 transition-colors cursor-pointer"
                      onClick={() => showCustomTagInput(item.id)}
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="10" height="10" viewBox="0 0 32 32" fill="none">
                        <path fillRule="evenodd" clipRule="evenodd" d="M14.3999 17.6V32H17.5999V17.6H32V14.4H17.5999V0H14.3999V14.4H0V17.6H14.3999Z" fill="#A1A3C4"></path>
                      </svg>
                      <span>自定义</span>
                    </button>
                  )}
                </div>
              </div>
            </div>

            {/* 专业 */}
            <div className="space-y-2">
              <Label htmlFor={`major_${item.id}`}>专业</Label>
              <Input
                id={`major_${item.id}`}
                type="text"
                placeholder="写全称哦，如：软件工程"
                value={item.major}
                onChange={(e) => {
                  const updatedItems = educationItems.map(edu =>
                    edu.id === item.id ? {...edu, major: e.target.value} : edu
                  );
                  setEducationItems(updatedItems);
                }}
                className="bg-white"
              />
            </div>

            {/* 学历 */}
            <div className="space-y-2">
              <Label htmlFor={`degree_${item.id}`}>学历</Label>
              <Select
                value={item.degree}
                onValueChange={(value) => {
                  const updatedItems = educationItems.map(edu =>
                    edu.id === item.id ? {...edu, degree: value} : edu
                  );
                  setEducationItems(updatedItems);
                }}
              >
                <SelectTrigger className="w-full bg-white">
                  <SelectValue placeholder="请选择学历类型" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="本科">本科</SelectItem>
                  <SelectItem value="硕士">硕士</SelectItem>
                  <SelectItem value="博士">博士</SelectItem>
                  <SelectItem value="专科">专科</SelectItem>
                  <SelectItem value="高中">高中</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 学院名称 */}
            <div className="space-y-2">
              <Label htmlFor={`college_name_${item.id}`}>学院名称</Label>
              <Input
                id={`college_name_${item.id}`}
                type="text"
                placeholder="写全称哦，如：经济管理学院"
                value={item.college_name}
                onChange={(e) => {
                  const updatedItems = educationItems.map(edu =>
                    edu.id === item.id ? {...edu, college_name: e.target.value} : edu
                  );
                  setEducationItems(updatedItems);
                }}
                className="bg-white"
              />
            </div>

            {/* 所在城市 */}
            <div className="space-y-2">
              <Label htmlFor={`city_${item.id}`}>所在城市</Label>
              <Input
                id={`city_${item.id}`}
                type="text"
                placeholder="请输入您所在城市"
                value={item.city}
                onChange={(e) => {
                  const updatedItems = educationItems.map(edu =>
                    edu.id === item.id ? {...edu, city: e.target.value} : edu
                  );
                  setEducationItems(updatedItems);
                }}
                className="bg-white"
              />
            </div>

            {/* 开始时间 - 使用年月选择器 */}
            <YearMonthPicker
              id={`start_date_${item.id}`}
              label="开始时间"
              placeholder="如：2021-09"
              value={item.start_date}
              onChange={(value) => {
                const updatedItems = educationItems.map(edu =>
                  edu.id === item.id ? {...edu, start_date: value} : edu
                );
                setEducationItems(updatedItems);
              }}
            />

            {/* 结束时间 - 使用年月选择器 */}
            <YearMonthPicker
              id={`end_date_${item.id}`}
              label="结束时间"
              placeholder={`如：${new Date().getFullYear()}-06，或选择'至今'`}
              value={item.end_date}
              onChange={(value) => {
                const updatedItems = educationItems.map(edu =>
                  edu.id === item.id ? {...edu, end_date: value} : edu
                );
                setEducationItems(updatedItems);
              }}
            />

            {/* 教育经历 */}
            <div className="col-span-2">
              <div className="space-y-2">
                <Label htmlFor={`description_${item.id}`}>教育经历</Label>
                <MarkdownEditor
                  value={item.description}
                  onChange={(value) => {
                    const updatedItems = educationItems.map(edu =>
                      edu.id === item.id ? {...edu, description: value} : edu
                    );
                    setEducationItems(updatedItems);
                  }}
                  height={200}
                  placeholder="应届生/经验5年以内，教育经历建议放在简历核心区域，工作经验丰富且匹配职位时可以放简历末尾。

填写示例:

GPA:3.8/4.0(专业前10%)[通过对比突出含金量，如: 专业前3，排名: 7/65]

课程: 财务管理 (96/100)经济法(88/100) ...[课程名称+分数，分数不突出时可以只写名称]"
                />
              </div>
              <AIGenerateButtons
                className="w-full"
                markdownContent={item.description}
                educationInfo={{
                  school_name: item.school_name,
                  major: item.major,
                  degree: item.degree
                }}
                onContentGenerated={(content) => {
                  const updatedItems = educationItems.map(edu =>
                    edu.id === item.id ? {...edu, description: content} : edu
                  );
                  setEducationItems(updatedItems);
                }}
              />
            </div>
          </form>
        </div>
      )}
    </div>
  );
};

export default function Education() {
  // 从store中获取education模块数据
  const { modules, updateModuleItem, activeIndex, setActiveIndex } = useModuleStore();
  const educationModule = modules['education'];

  // 使用ref来跟踪是否是本地状态更新导致的变化
  const isLocalUpdate = React.useRef(false);
  // 使用ref来跟踪是否是首次渲染
  const isFirstRender = React.useRef(true);

  // 从store数据转换为本地表单数据
  const convertStoreToLocal = (storeData: StoreEducationItem[]): EducationItem[] => {
    return storeData.map(item => ({
      id: item.id,
      school_name: item.school_name?.value || '',
      college_name: item.college_name?.value || '',
      major: item.major?.value || '',
      degree: item.degree?.value || '',
      city: item.city?.value || '',
      start_date: item.start_date?.value || '',
      end_date: item.end_date?.value || '',
      description: item.description?.value || '',
      school_tags: item.school_tags?.value || [],
      index: item.index
    }));
  };

  // 从本地表单数据转换为store数据
  const convertLocalToStore = (localData: EducationItem[]): StoreEducationItem[] => {
    return localData.map(item => ({
      id: item.id,
      school_name: { label: "学校名称", value: item.school_name },
      college_name: { label: "学院名称", value: item.college_name },
      major: { label: "专业", value: item.major },
      degree: { label: "学历", value: item.degree },
      city: { label: "所在城市", value: item.city },
      start_date: { label: "开始日期", value: item.start_date },
      end_date: { label: "结束日期", value: item.end_date },
      description: { label: "描述", value: item.description },
      school_tags: { label: "学校标签", value: item.school_tags },
      index: item.index
    }));
  };

  // 使用store中的数据，如果没有则使用空数组
  const [educationItems, setEducationItems] = useState<EducationItem[]>(() => {
    if (educationModule?.item && Array.isArray(educationModule.item) && educationModule.item.length > 0) {
      return convertStoreToLocal(educationModule.item as StoreEducationItem[]);
    }
    return [];
  });

  // 初始化：如果store中没有数据，则创建一个默认的空项目
  useEffect(() => {
    if (educationModule && (!educationModule.item || (Array.isArray(educationModule.item) && educationModule.item.length === 0))) {
      // 创建一个默认的空教育经历项目
      const defaultEducationItem: EducationItem = {
        id: 'edu-1',
        school_name: '',
        college_name: '',
        major: '',
        degree: '本科',
        city: '',
        start_date: '',
        end_date: '',
        description: '',
        school_tags: [],
        index: 0
      };

      // 标记为本地更新
      isLocalUpdate.current = true;
      // 更新本地状态
      setEducationItems([defaultEducationItem]);
      // 更新store中的数据
      updateModuleItem('education', convertLocalToStore([defaultEducationItem]));
      // 重置标记
      setTimeout(() => {
        isLocalUpdate.current = false;
      }, 0);
    }
  }, [educationModule, updateModuleItem]);

  // 当store中的数据变化时，更新本地状态，但仅当不是由本地更新触发的
  useEffect(() => {
    if (!isLocalUpdate.current && educationModule?.item && Array.isArray(educationModule.item) && educationModule.item.length > 0) {
      setEducationItems(convertStoreToLocal(educationModule.item as StoreEducationItem[]));
    }
  }, [educationModule?.item]);

  // 当本地状态变化时，更新store，但需要防止无限循环
  useEffect(() => {
    // 如果是首次渲染，标记为非首次渲染并返回
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    // 仅当不是从store更新本地状态时才更新store
    if (educationModule && !isLocalUpdate.current) {
      // 标记为本地更新
      isLocalUpdate.current = true;
      // 将本地数据转换为store格式并更新
      updateModuleItem('education', convertLocalToStore(educationItems));
      // 重置标记
      setTimeout(() => {
        isLocalUpdate.current = false;
      }, 0);
    }
  }, [educationItems, educationModule, updateModuleItem]);

  // 展开/折叠状态 - 使用对象来跟踪每个教育经历项目的展开状态
  const [expandedItems, setExpandedItems] = useState<Record<string, boolean>>(() => {
    // 根据 activeIndex 数组确定默认展开的项目
    if (activeIndex.length > 0 && educationItems.length > 0) {
      const initialExpanded: Record<string, boolean> = {};
      activeIndex.forEach(index => {
        if (educationItems[index]) {
          initialExpanded[educationItems[index].id] = true;
        }
      });
      return initialExpanded;
    }
    // 如果没有 activeIndex 或对应项目不存在，默认展开第一个项目
    return educationItems.length > 0 ? { [educationItems[0].id]: true } : {};
  });

  // 监听 activeIndex 变化，更新展开状态
  useEffect(() => {
    if (activeIndex.length > 0 && educationItems.length > 0) {
      // 根据 activeIndex 数组展开对应的项目
      const newExpandedItems: Record<string, boolean> = {};
      activeIndex.forEach(index => {
        if (educationItems[index]) {
          newExpandedItems[educationItems[index].id] = true;
        }
      });
      setExpandedItems(newExpandedItems);
    }
  }, [activeIndex, educationItems]);

  // 拖拽状态
  const [activeId, setActiveId] = useState<string | null>(null);

  // 获取当前拖拽的项目
  const activeItem = activeId ? educationItems.find(item => item.id === activeId) : null;

  // 删除确认对话框状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{id: string, name: string} | null>(null);

  // 自定义标签状态
  const [customTagInputs, setCustomTagInputs] = useState<Record<string, {isEditing: boolean, value: string}>>({});

  // 处理自定义标签输入
  const handleCustomTagInput = (itemId: string, value: string) => {
    setCustomTagInputs(prev => ({
      ...prev,
      [itemId]: {
        ...prev[itemId],
        value
      }
    }));
  };

  // 显示自定义标签输入框
  const showCustomTagInput = (itemId: string) => {
    setCustomTagInputs(prev => ({
      ...prev,
      [itemId]: {
        isEditing: true,
        value: prev[itemId]?.value || ''
      }
    }));
  };

  // 不再需要单独的 addCustomTag 函数，因为我们已经在组件内部直接实现了添加标签的逻辑

  // 已经定义了 customTagInputs 状态，不需要重复定义

  // 切换特定项目的展开/折叠状态
  const toggleExpanded = (itemId: string) => {
    const itemIndex = educationItems.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    // 先更新展开状态
    setExpandedItems(prev => {
      const newExpanded = {
        ...prev,
        [itemId]: !prev[itemId]
      };

      // 在下一个事件循环中更新 activeIndex，避免在渲染过程中调用 setState
      setTimeout(() => {
        const currentActiveIndex = [...activeIndex];
        if (newExpanded[itemId]) {
          // 展开：添加到 activeIndex
          if (!currentActiveIndex.includes(itemIndex)) {
            currentActiveIndex.push(itemIndex);
            setActiveIndex(currentActiveIndex);
          }
        } else {
          // 折叠：从 activeIndex 移除
          const indexToRemove = currentActiveIndex.indexOf(itemIndex);
          if (indexToRemove > -1) {
            currentActiveIndex.splice(indexToRemove, 1);
            setActiveIndex(currentActiveIndex);
          }
        }
      }, 0);

      return newExpanded;
    });
  };

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // 获取排序后的教育经历数组
      const oldIndex = educationItems.findIndex(item => item.id === active.id);
      const newIndex = educationItems.findIndex(item => item.id === over.id);

      // 更新教育经历项目的顺序
      const updatedItems = [...educationItems];
      const [movedItem] = updatedItems.splice(oldIndex, 1);
      updatedItems.splice(newIndex, 0, movedItem);

      // 更新所有项目的索引
      const reindexedItems = updatedItems.map((item, index) => ({
        ...item,
        index
      }));

      // 更新状态
      setEducationItems(reindexedItems);
    }

    // 重置拖拽状态
    setActiveId(null);
  };

  // 添加新的教育经历
  const addEducationItem = () => {
    // 生成唯一ID
    const newId = `edu-${Date.now()}`;

    // 创建新的教育经历项目
    const newEducationItem: EducationItem = {
      id: newId,
      school_name: '',
      college_name: '',
      major: '',
      degree: '本科',
      city: '',
      start_date: '',
      end_date: '',
      description: '',
      school_tags: [],
      index: educationItems.length // 设置索引为当前列表长度
    };

    // 添加到列表中
    setEducationItems([...educationItems, newEducationItem]);

    // 自动展开新添加的项目
    setExpandedItems(prev => ({
      ...prev,
      [newId]: true
    }));
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (itemId: string) => {
    // 如果只剩下一个项目，不允许删除
    if (educationItems.length <= 1) {
      return;
    }

    const item = educationItems.find(item => item.id === itemId);
    if (item) {
      setItemToDelete({
        id: itemId,
        name: item.school_name || '教育经历'
      });
      setDeleteConfirmOpen(true);
    }
  };

  // 确认删除教育经历项目
  const confirmDeleteEducationItem = () => {
    if (!itemToDelete) return;

    // 从列表中移除项目
    const updatedItems = educationItems.filter(item => item.id !== itemToDelete.id);

    // 更新状态
    setEducationItems(updatedItems);

    // 从展开状态中移除该项目
    const newExpandedItems = { ...expandedItems };
    delete newExpandedItems[itemToDelete.id];
    setExpandedItems(newExpandedItems);

    // 重置删除状态
    setItemToDelete(null);
  };

  // 保留原有的删除函数以兼容现有代码
  const deleteEducationItem = (itemId: string) => {
    showDeleteConfirm(itemId);
  };

  // 定义提示项
  const tipItems = [
    {
      question: '工作几年了还要写教育经历吗？',
      answer: '如果工作经验丰富，教育经历建议简写，只写学校名称、学历、专业、时间即可。'
    },
    {
      question: 'GPA怎么写？',
      answer: '写简历的核心思想——扬长避短。如果GPA高你就写（通过对比凸显自己的优秀）\n如：\nGPA：3.5/4.0（专业前15%）\nGPA：3.8/4.0（专业前3）。'
    },
    {
      question: '写课程上去是不是太凑数了？',
      answer: '写简历的核心思想——扬长避短。\n如果你课程和求职意向相关就写，如果你课程分数高就写。'
    },
    {
      question: '高中教育经历怎么写？',
      answer: '建议不要写，如果你其他经验太少，而且高中特别出名，可以写。'
    },
    {
      question: '获奖、社团、比赛、培训这些经历要写吗',
      answer: '如果能给你加分那就写上，但是不建议写在这里，可写在其他对应版块。'
    },
    {
      question: '国外的成绩怎么写',
      answer: '百分比的成绩建议改成 GPA 的格式，可以参考 College Board 的网站 How to Convert Your GPA to a 4.0 Scale，了解美国最权威的机构 College Board(美国大学理事会)是如何换算 GPA 的。\n如果是英国成绩的话，直接写 AVG 或者 平均成绩，有 Merit 或者 Distinction 也可以不写成绩，直接写 优等学位 Merit 或一等成绩 Distinction。'
    },
  ];

  return (
    <div className="h-full">
      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="教育经历怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                填写简历的核心思路就是——扬长避短，突出自己和岗位的匹配度。面试或者简历上有缺点没关系，反而更真实，但不要暴露硬伤。
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 使用 ScrollableContent 组件 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              id="education"
              icon={<Image src="/image/education.svg" alt="教育经历" width={24} height={24} />}
              title="教育经历"
              description="教育经历可以突出你学习和成长潜力，尤其是应届生和工作5年以内的候选人🎓"
            />

            {/* 表单内容区域 */}
            <div className="p-6 space-y-6 bg-[#f7f8fa] rounded-md">
              {/* 教育经历列表 */}
              <div className="flex-1">
                {/* 使用DndContext包装教育经历列表 */}
                <DndContext
                  sensors={useSensors(
                    useSensor(PointerSensor, {
                      activationConstraint: {
                        distance: 2, // 只需要拖动2px就会触发拖拽
                        tolerance: 3, // 增加容差，使拖拽更容易触发
                        delay: 0, // 无延迟
                      },
                    }),
                    useSensor(KeyboardSensor, {
                      coordinateGetter: sortableKeyboardCoordinates,
                    })
                  )}
                  collisionDetection={closestCenter}
                  onDragStart={handleDragStart}
                  onDragEnd={handleDragEnd}
                  measuring={{
                    droppable: {
                      strategy: MeasuringStrategy.Always,
                    },
                  }}
                >
                  <SortableContext
                    items={educationItems.map(item => item.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    {educationItems.map((item) => (
                      <SortableEducationItem
                        key={item.id}
                        item={item}
                        expandedItems={expandedItems}
                        toggleExpanded={toggleExpanded}
                        deleteEducationItem={deleteEducationItem}
                        educationItems={educationItems}
                        setEducationItems={setEducationItems}
                        customTagInputs={customTagInputs}
                        handleCustomTagInput={handleCustomTagInput}
                        showCustomTagInput={showCustomTagInput}

                        setCustomTagInputs={setCustomTagInputs}
                      />
                    ))}
                  </SortableContext>

                  {/* 拖拽预览 */}
                  <DragOverlay>
                    {activeItem ? (
                      <div className="flex items-center justify-between w-full text-[14px] text-[#2E2F66] bg-white p-4 rounded-md shadow-lg border-2 border-purple-200">
                        <div className="flex items-center gap-3">
                          <div className="w-[137px] text-ellipsis overflow-hidden whitespace-nowrap" title={activeItem.school_name}>
                            {activeItem.school_name || '未填写学校名称'}
                          </div>
                        </div>
                        <div>
                          {activeItem.start_date || '未填写'} <span>- {activeItem.end_date || '未填写'}</span>
                        </div>
                        <div className="flex gap-5 items-center">
                          <div className={cn(
                            "cursor-grab p-1 rounded-md bg-gray-100 text-primary"
                          )}>
                            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                              <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
                              <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
                            </svg>
                          </div>
                        </div>
                      </div>
                    ) : null}
                  </DragOverlay>
                </DndContext>
              </div>

            </div>

            {/* 添加教育经历按钮和跳转器 */}
            <div className="mt-6 flex justify-between items-center">
              <button
                type="button"
                onClick={addEducationItem}
                className="flex items-center justify-center gap-2 py-2 px-5 rounded-md transition-all bg-white text-[#333] border border-gray-200 hover:bg-gray-50 active:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 cursor-pointer shadow-sm whitespace-nowrap"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 4V12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M4 8H12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
                <span className="text-base font-medium">教育经历</span>
              </button>

              <ModuleNavigator currentModuleId="education" />
            </div>
          </ScrollableContent>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <ItemDeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        itemName={itemToDelete?.name || ''}
        itemType="教育经历"
        onConfirm={confirmDeleteEducationItem}
        disabled={educationItems.length <= 1}
        disabledReason="至少需要保留一个教育经历"
      />
    </div>
  );
}
