'use client';

import React, { useState, useEffect } from 'react';
import Tip from '../Tip';
import ModuleNavigator from '@/components/ModuleNavigator';
import ScrollableContent from '../ScrollableContent';
import ModuleHeader from '@/components/ModuleHeader';
import Image from 'next/image';
import { useModuleStore } from '@/store/useModuleStore';
import { useResumeStyleStore } from '@/store/useResumeStyleStore';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { cn } from '@/lib/utils';
import SkillBar from '@/components/SkillBar';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  MeasuringStrategy,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// 导入store中的类型
import { SkillsItem, SkillValueItem } from '@/store/useModuleStore';
import ItemDeleteConfirmDialog from '../ItemDeleteConfirmDialog';

// 可排序的技能项组件
const SortableSkillItem = ({
  item,
  deleteSkillItem,
  skillItems,
  setSkillItems
}: {
  item: SkillValueItem;
  deleteSkillItem: (index: number) => void;
  skillItems: SkillValueItem[];
  setSkillItems: React.Dispatch<React.SetStateAction<SkillValueItem[]>>;
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editName, setEditName] = useState(item.name.value);
  const [editScore, setEditScore] = useState(item.score.value);

  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: item.index.toString() });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 'auto',
    position: 'relative' as const,
    touchAction: 'none',
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEditName(e.target.value);
  };

  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleConfirmEdit();
    } else if (e.key === 'Escape') {
      cancelEditing();
    }
  };

  const handleScoreChange = (value: string) => {
    const score = parseInt(value);
    if (!isNaN(score) && score >= 0 && score <= 10) {
      setEditScore(score);
    }
  };

  const cancelEditing = () => {
    setIsEditing(false);
    setEditName(item.name.value);
    setEditScore(item.score.value);
  };

  const handleConfirmEdit = () => {
    if (editName.trim()) {
      const updatedItems = skillItems.map(skill =>
        skill.index === item.index ? {
          ...skill,
          name: { ...skill.name, value: editName.trim() },
          score: { ...skill.score, value: editScore }
        } : skill
      );
      setSkillItems(updatedItems);
    }
    setIsEditing(false);
  };

  return (
    <div ref={setNodeRef} style={style} className="mb-3">
      <div className="bg-white p-4 rounded-md">
        {isEditing ? (
          <div className="flex justify-between items-center">
            <div className="flex-1 flex items-center gap-[7.5rem]">
              <div>
                <input
                  type="text"
                  value={editName}
                  onChange={handleInputChange}
                  onKeyDown={handleInputKeyDown}
                  className="w-40 px-2 py-1 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-200"
                  autoFocus
                  onClick={(e) => e.stopPropagation()}
                />
              </div>
              <div>
                <Select
                  value={editScore.toString()}
                  onValueChange={handleScoreChange}
                >
                  <SelectTrigger className="w-32 h-8 text-sm bg-white cursor-pointer">
                    <SelectValue placeholder="选择熟练度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0" className="cursor-pointer text-sm">0</SelectItem>
                    <SelectItem value="1" className="cursor-pointer text-sm">1 初学</SelectItem>
                    <SelectItem value="2" className="cursor-pointer text-sm">2</SelectItem>
                    <SelectItem value="3" className="cursor-pointer text-sm">3</SelectItem>
                    <SelectItem value="4" className="cursor-pointer text-sm">4 入门</SelectItem>
                    <SelectItem value="5" className="cursor-pointer text-sm">5</SelectItem>
                    <SelectItem value="6" className="cursor-pointer text-sm">6 掌握</SelectItem>
                    <SelectItem value="7" className="cursor-pointer text-sm">7</SelectItem>
                    <SelectItem value="8" className="cursor-pointer text-sm">8 熟练</SelectItem>
                    <SelectItem value="9" className="cursor-pointer text-sm">9</SelectItem>
                    <SelectItem value="10" className="cursor-pointer text-sm">10 精通</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <div className="flex items-center gap-2">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  cancelEditing();
                }}
                className="p-1 text-red-500 hover:text-red-600 cursor-pointer"
                title="取消"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleConfirmEdit();
                }}
                className="p-1 text-green-500 hover:text-green-600 cursor-pointer"
                title="确认"
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </button>
            </div>
          </div>
        ) : (
          <div className="flex justify-between items-center">
            <div className="flex-1 flex items-center gap-[7.5rem]">
              <div>
                {item.name.value}
              </div>
              <span className="whitespace-nowrap">
                熟练度：{item.score.value}/10
              </span>
            </div>
            <div className="flex items-center gap-3">
              <div
                className={cn(
                  "cursor-grab active:cursor-grabbing p-1 rounded-md transition-colors",
                  "hover:bg-gray-100 hover:text-primary"
                )}
                {...listeners}
                {...attributes}
                title="拖拽排序"
                onClick={(e) => e.stopPropagation()}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path fillRule="evenodd" clipRule="evenodd" d="M7.19958 15.0532C7.47572 15.0532 7.69958 14.8294 7.69958 14.5532L7.69957 5.44624C7.69957 5.23032 7.56097 5.03879 7.35587 4.9713C7.15077 4.9038 6.92551 4.97559 6.79728 5.14932L4.55673 8.18498C4.39274 8.40716 4.43992 8.72021 4.66209 8.88419C4.88427 9.04818 5.19732 9.001 5.36131 8.77882L6.69958 6.96565L6.69958 14.5532C6.69958 14.8294 6.92343 15.0532 7.19958 15.0532Z" fill="currentColor"></path>
                  <path fillRule="evenodd" clipRule="evenodd" d="M12.8004 4.94678C12.5243 4.94678 12.3004 5.17063 12.3004 5.44678L12.3004 14.5538C12.3004 14.7697 12.439 14.9612 12.6441 15.0287C12.8492 15.0962 13.0745 15.0244 13.2027 14.8507L15.4433 11.815C15.6073 11.5928 15.5601 11.2798 15.3379 11.1158C15.1157 10.9518 14.8027 10.999 14.6387 11.2212L13.3004 13.0344L13.3004 5.44678C13.3004 5.17063 13.0766 4.94678 12.8004 4.94678Z" fill="currentColor"></path>
                </svg>
              </div>
              <div
                className="cursor-pointer hover:text-primary p-1"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsEditing(true);
                }}
                title="编辑技能"
              >
                <Image src="/assets/svg/edit-icon.svg" alt="编辑" width={20} height={20} />
              </div>
              <div
                className={`cursor-pointer p-1 ${skillItems.length <= 1 ? 'opacity-50' : 'hover:text-red-500'}`}
                onClick={(e) => {
                  e.stopPropagation();
                  if (skillItems.length > 1) {
                    deleteSkillItem(item.index);
                  }
                }}
                title={skillItems.length <= 1 ? "至少保留一个技能项" : "删除此技能"}
              >
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path fillRule="evenodd" clipRule="evenodd" d="M6.7251 8.77979C7.00124 8.77979 7.2251 9.00364 7.2251 9.27979V14.7341H12.7746V9.27979C12.7746 9.00364 12.9985 8.77979 13.2746 8.77979C13.5508 8.77979 13.7746 9.00364 13.7746 9.27979V15.2341C13.7746 15.5103 13.5508 15.7341 13.2746 15.7341H6.7251C6.44896 15.7341 6.2251 15.5103 6.2251 15.2341V9.27979C6.2251 9.00364 6.44896 8.77979 6.7251 8.77979Z" fill="currentColor"></path>
                  <path fillRule="evenodd" clipRule="evenodd" d="M4.76807 6.78955C4.76807 6.51341 4.99192 6.28955 5.26807 6.28955L14.732 6.28955C15.0082 6.28955 15.232 6.51341 15.232 6.78955C15.232 7.06569 15.0082 7.28955 14.732 7.28955L5.26807 7.28955C4.99192 7.28955 4.76807 7.06569 4.76807 6.78955Z" fill="currentColor"></path>
                  <path fillRule="evenodd" clipRule="evenodd" d="M7.76147 4.76611C7.76147 4.48997 7.98533 4.26611 8.26147 4.26611H11.7384C12.0145 4.26611 12.2384 4.48997 12.2384 4.76611C12.2384 5.04226 12.0145 5.26611 11.7384 5.26611H8.26147C7.98533 5.26611 7.76147 5.04226 7.76147 4.76611Z" fill="currentColor"></path>
                  <path fillRule="evenodd" clipRule="evenodd" d="M8.91357 8.95068C9.18972 8.95068 9.41357 9.17454 9.41357 9.45068V13.1917C9.41357 13.4679 9.18972 13.6917 8.91357 13.6917C8.63743 13.6917 8.41357 13.4679 8.41357 13.1917V9.45068C8.41357 9.17454 8.63743 8.95068 8.91357 8.95068Z" fill="currentColor"></path>
                  <path fillRule="evenodd" clipRule="evenodd" d="M11.0864 8.95068C11.3626 8.95068 11.5864 9.17454 11.5864 9.45068V13.1917C11.5864 13.4679 11.3626 13.6917 11.0864 13.6917C10.8103 13.6917 10.5864 13.4679 10.5864 13.1917V9.45068C10.5864 9.17454 10.8103 8.95068 11.0864 8.95068Z" fill="currentColor"></path>
                </svg>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default function Skills() {
  // 定义提示项
  const tipItems = [
    {
      question: '技能太水了，要写上去吗？',
      answer: '如果与申请岗位相关，一定一定一定要写，而且起码要写熟练或精通。\n\n技能都是在实战用精进的，你目前最重要的是得到实战机会。'
    },
    {
      question: '优先写哪些技能？',
      answer: '最好跟所申请岗位的相关，比如金融行业会偏向量化的技能，如量化分析（精通Python/R 等）、数据库（Bloomberg/万德等）。'
    },
    {
      question: '技能条和文字描述技能，哪种方式好？',
      answer: '如果你的技能非常牛，建议用文字描述。比如：英语（雅思7.5）、钢琴（专业8级），可以加备注来证明你的专业程度。\n\n如果比较一般，建议用技能条，更加美观，程度比较灵活。'
    },
    {
      question: '实在没有技能怎么办？',
      answer: '实在没有什么好写，那就写Word、Excel、PPT、驾驶证、普通话这些通用技能。'
    }
  ];

  // 从store中获取skills模块数据
  const { modules, updateModuleItem } = useModuleStore();
  const skillsModule = modules['skills'];

  // 从样式store中获取功能开关
  const { can_change_skills_three_columns } = useResumeStyleStore();

  // 使用ref来跟踪是否是本地状态更新导致的变化
  const isLocalUpdate = React.useRef(false);
  // 使用ref来跟踪是否是首次渲染
  const isFirstRender = React.useRef(true);

  // 默认技能条数据
  const defaultSkillData: SkillsItem = {
    skillLayout: { label: "技能布局", value: '1' },
    skillStyle: { label: "技能样式", value: '1' },
    values: { label: "技能列表", value: [
      {
        name: { label: "技能名称", value: 'Word' },
        index: 0,
        score: { label: "技能评分", value: 7 },
        desc: { label: "技能描述", value: '' },
      },
      {
        name: { label: "技能名称", value: 'Excel' },
        index: 1,
        score: { label: "技能评分", value: 7 },
        desc: { label: "技能描述", value: '' },
      }
    ] }
  };

  // 使用store中的数据，如果没有则使用默认数据
  const [skillData, setSkillData] = useState<SkillsItem>(() => {
    if (skillsModule?.item &&
        typeof skillsModule.item === 'object' &&
        'skillLayout' in skillsModule.item &&
        'skillStyle' in skillsModule.item &&
        'values' in skillsModule.item) {
      return skillsModule.item as SkillsItem;
    }
    return defaultSkillData;
  });

  // 提取技能项目列表
  const [skillItems, setSkillItems] = useState<SkillValueItem[]>(
    skillData.values.value || []
  );

  // 布局和样式状态
  const [layout, setLayout] = useState(skillData.skillLayout.value || '1');
  const [style, setStyle] = useState(skillData.skillStyle.value || '1');
  // 下拉菜单状态
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // 拖拽状态
  const [activeId, setActiveId] = useState<string | null>(null);

  // 获取当前拖拽的项目
  const activeItem = activeId ? skillItems.find(item => item.index.toString() === activeId) : null;

  // 删除确认对话框状态
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<{id: number, name: string} | null>(null);

  // 设置传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 2, // 只需要拖动2px就会触发拖拽
        tolerance: 3, // 增加容差，使拖拽更容易触发
        delay: 0, // 无延迟
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      setSkillItems(items => {
        const oldIndex = items.findIndex(item => item.index.toString() === active.id);
        const newIndex = items.findIndex(item => item.index.toString() === over.id);

        // 创建新数组并重新排序
        const newItems = [...items];
        const [movedItem] = newItems.splice(oldIndex, 1);
        newItems.splice(newIndex, 0, movedItem);

        // 更新索引
        return newItems.map((item, index) => ({
          ...item,
          index
        }));
      });
    }

    setActiveId(null);
  };

  // 显示删除确认对话框
  const showDeleteConfirm = (index: number) => {
    if (skillItems.length <= 1) return; // 至少保留一个技能项

    const item = skillItems.find(item => item.index === index);
    if (item) {
      setItemToDelete({
        id: index,
        name: item.name.value || '技能'
      });
      setDeleteConfirmOpen(true);
    }
  };

  // 确认删除技能项目
  const confirmDeleteSkillItem = () => {
    if (itemToDelete === null) return;

    const newItems = skillItems.filter(item => item.index !== itemToDelete.id)
      .map((item, idx) => ({ ...item, index: idx }));

    setSkillItems(newItems);

    // 重置删除状态
    setItemToDelete(null);
  };

  // 保留原有的删除函数以兼容现有代码
  const deleteSkillItem = (index: number) => {
    showDeleteConfirm(index);
  };

  // 添加新的技能项目
  const addSkillItem = () => {
    const newItem: SkillValueItem = {
      name: { label: "技能名称", value: '新技能' },
      index: skillItems.length,
      score: { label: "技能评分", value: 7 },
      desc: { label: "技能描述", value: '' },
    };

    setSkillItems([...skillItems, newItem]);
  };

  // 当本地状态变化时，更新store
  useEffect(() => {
    if (isFirstRender.current) {
      isFirstRender.current = false;
      return;
    }

    isLocalUpdate.current = true;

    // 更新store中的数据
    updateModuleItem('skills', {
      skillLayout: { label: "技能布局", value: layout },
      skillStyle: { label: "技能样式", value: style },
      values: { label: "技能列表", value: skillItems }
    });
  }, [skillItems, layout, style, updateModuleItem]);

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // 当store中的数据变化时，更新本地状态
  useEffect(() => {
    if (isLocalUpdate.current) {
      isLocalUpdate.current = false;
      return;
    }

    if (skillsModule?.item &&
        typeof skillsModule.item === 'object' &&
        'skillLayout' in skillsModule.item &&
        'skillStyle' in skillsModule.item &&
        'values' in skillsModule.item) {
      const data = skillsModule.item as SkillsItem;
      setSkillData(data);
      setSkillItems(data.values.value || []);
      setLayout(data.skillLayout.value || '1');
      setStyle(data.skillStyle.value || '1');
    }
  }, [skillsModule]);

  return (
    <div className="h-full">
      <div className="flex gap-4 h-full">
        <div className="w-48">
          <Tip
            title="技能条怎么写？"
            description="还有疑惑? 立即反馈"
            tipContent={
              <>
                合适的布局和样式可以让简历看起来这更整齐美观。
              </>
            }
            items={tipItems}
          />
        </div>

        <div className="flex-1 rounded-md flex flex-col bg-white">
          {/* 使用 ScrollableContent 组件 */}
          <ScrollableContent>
            {/* 模块头部 */}
            <ModuleHeader
              id="skills"
              icon={<Image src="/image/skills.svg" alt="技能条" width={24} height={24} />}
              title="技能条"
              description="使用图形样式表示掌握技能的熟练程度，可以增加简历的设计感。📊"
            />

            {/* 表单内容区域 */}
            <div className="p-6 space-y-6 bg-[#f7f8fa] rounded-md">
              {/* 技能条设置 */}
              <div className="space-y-4">
                <h3 className="text-base font-medium text-gray-700">技能条设置</h3>

                {/* 技能条布局 */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <Label className="w-24 flex-shrink-0">技能条布局</Label>
                  <div className="flex flex-wrap gap-3 flex-1">
                    <button
                      type="button"
                      className={cn(
                        "flex items-center justify-center h-9 px-8 text-sm rounded-md transition-all border cursor-pointer",
                        layout === '1'
                          ? "bg-primary text-white border-primary"
                          : "bg-white text-gray-700 border-gray-200 hover:border-primary"
                      )}
                      onClick={() => setLayout('1')}
                    >
                      单栏
                    </button>
                    <button
                      type="button"
                      className={cn(
                        "flex items-center justify-center h-9 px-8 text-sm rounded-md transition-all border cursor-pointer",
                        layout === '2'
                          ? "bg-primary text-white border-primary"
                          : "bg-white text-gray-700 border-gray-200 hover:border-primary"
                      )}
                      onClick={() => setLayout('2')}
                    >
                      双栏
                    </button>
                    <button
                      type="button"
                      className={cn(
                        "flex items-center justify-center h-9 px-8 text-sm rounded-md transition-all border",
                        !can_change_skills_three_columns
                          ? "bg-gray-100 text-gray-400 border-gray-200 cursor-not-allowed"
                          : layout === '3'
                          ? "bg-primary text-white border-primary cursor-pointer"
                          : "bg-white text-gray-700 border-gray-200 hover:border-primary cursor-pointer"
                      )}
                      onClick={() => can_change_skills_three_columns && setLayout('3')}
                      disabled={!can_change_skills_three_columns}
                      title={!can_change_skills_three_columns ? "当前模板不支持三栏布局" : ""}
                    >
                      三栏
                    </button>
                  </div>
                </div>

                {/* 技能条样式 */}
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <Label className="w-24 flex-shrink-0">技能条样式</Label>
                  <div className="flex-1">
                    <div className="relative" ref={dropdownRef}>
                      {/* 当前选中的样式展示 */}
                      <div
                        className="w-full bg-white border border-gray-200 rounded-md cursor-pointer p-2 flex items-center justify-between"
                        onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                      >
                        <div>
                          {style === '1' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style1"
                              color="var(--primary)"
                            />
                          )}
                          {style === '2' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style2"
                              color="var(--primary)"
                            />
                          )}
                          {style === '3' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style3"
                              color="var(--primary)"
                            />
                          )}
                          {style === '4' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style4"
                              color="var(--primary)"
                            />
                          )}
                          {style === '5' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style5"
                              color="var(--primary)"
                            />
                          )}
                          {style === '6' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style6"
                              color="var(--primary)"
                            />
                          )}
                          {style === '7' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style7"
                              color="var(--primary)"
                            />
                          )}
                          {style === '8' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style8"
                              color="var(--primary)"
                            />
                          )}
                          {style === '9' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style9"
                              color="var(--primary)"
                            />
                          )}
                          {style === '10' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style10"
                              color="var(--primary)"
                            />
                          )}
                          {style === '11' && (
                            <SkillBar
                              item={{ name: "技能", score: 7, index: 0 }}
                              style="style11"
                              color="var(--primary)"
                            />
                          )}
                          {style === '12' && (
                            <div className="flex justify-start w-full">
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style12"
                                color="var(--primary)"
                              />
                            </div>
                          )}
                          {style === '13' && (
                            <div className="flex justify-start w-full">
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style13"
                                color="var(--primary)"
                              />
                            </div>
                          )}
                          {style === '14' && (
                            <div className="flex justify-start w-full">
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style14"
                                color="var(--primary)"
                              />
                            </div>
                          )}
                          {style === '15' && (
                            <div className="flex justify-start w-full">
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style15"
                                color="var(--primary)"
                              />
                            </div>
                          )}
                        </div>
                        <svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg" className="ml-2 h-4 w-4 opacity-50">
                          <path d="M4.93179 5.43179C4.75605 5.60753 4.75605 5.89245 4.93179 6.06819C5.10753 6.24392 5.39245 6.24392 5.56819 6.06819L7.49999 4.13638L9.43179 6.06819C9.60753 6.24392 9.89245 6.24392 10.0682 6.06819C10.2439 5.89245 10.2439 5.60753 10.0682 5.43179L7.81819 3.18179C7.73379 3.0974 7.61933 3.04999 7.49999 3.04999C7.38064 3.04999 7.26618 3.0974 7.18179 3.18179L4.93179 5.43179ZM10.0682 9.56819C10.2439 9.39245 10.2439 9.10753 10.0682 8.93179C9.89245 8.75606 9.60753 8.75606 9.43179 8.93179L7.49999 10.8636L5.56819 8.93179C5.39245 8.75606 5.10753 8.75606 4.93179 8.93179C4.75605 9.10753 4.75605 9.39245 4.93179 9.56819L7.18179 11.8182C7.26618 11.9026 7.38064 11.95 7.49999 11.95C7.61933 11.95 7.73379 11.9026 7.81819 11.8182L10.0682 9.56819Z" fill="currentColor"></path>
                        </svg>
                      </div>

                      {/* 下拉选项 */}
                      {isDropdownOpen && (
                        <div className="absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-auto">
                          <div className="p-1">
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('1'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style1"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('2'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style2"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('3'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style3"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('4'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style4"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('5'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style5"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('6'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style6"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('7'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style7"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('8'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style8"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('9'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style9"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('10'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style10"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('11'); setIsDropdownOpen(false); }}
                            >
                              <SkillBar
                                item={{ name: "技能", score: 7, index: 0 }}
                                style="style11"
                                color="var(--primary)"
                              />
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('12'); setIsDropdownOpen(false); }}
                            >
                              <div className="flex justify-start">
                                <SkillBar
                                  item={{ name: "技能", score: 7, index: 0 }}
                                  style="style12"
                                  color="var(--primary)"
                                />
                              </div>
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('13'); setIsDropdownOpen(false); }}
                            >
                              <div className="flex justify-start">
                                <SkillBar
                                  item={{ name: "技能", score: 7, index: 0 }}
                                  style="style13"
                                  color="var(--primary)"
                                />
                              </div>
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('14'); setIsDropdownOpen(false); }}
                            >
                              <div className="flex justify-start">
                                <SkillBar
                                  item={{ name: "技能", score: 7, index: 0 }}
                                  style="style14"
                                  color="var(--primary)"
                                />
                              </div>
                            </div>
                            <div
                              className="p-2 hover:bg-gray-100 rounded-md cursor-pointer"
                              onClick={() => { setStyle('15'); setIsDropdownOpen(false); }}
                            >
                              <div className="flex justify-start">
                                <SkillBar
                                  item={{ name: "技能", score: 7, index: 0 }}
                                  style="style15"
                                  color="var(--primary)"
                                />
                              </div>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* 添加技能 */}
              <div className="space-y-4">
                <h3 className="text-base font-medium text-gray-700">添加技能</h3>

                {/* 技能项目列表 */}
                <div className="space-y-2">
                  <DndContext
                    sensors={sensors}
                    collisionDetection={closestCenter}
                    onDragStart={handleDragStart}
                    onDragEnd={handleDragEnd}
                    measuring={{
                      droppable: {
                        strategy: MeasuringStrategy.Always,
                      },
                    }}
                  >
                    <SortableContext
                      items={skillItems.map(item => item.index.toString())}
                      strategy={verticalListSortingStrategy}
                    >
                      {skillItems.map((item) => (
                        <SortableSkillItem
                          key={item.index}
                          item={item}
                          deleteSkillItem={deleteSkillItem}
                          skillItems={skillItems}
                          setSkillItems={setSkillItems}
                        />
                      ))}
                    </SortableContext>

                    {/* 拖拽预览 */}
                    <DragOverlay>
                      {activeItem ? (
                        <div className="bg-white p-4 rounded-md shadow-md">
                          <div className="flex justify-between items-center">
                            <div className="flex-1 flex items-center gap-[7.5rem]">
                              <div className="cursor-pointer">{activeItem.name.value}</div>
                              <span className="whitespace-nowrap">熟练度：{activeItem.score.value}/10</span>
                            </div>
                          </div>
                        </div>
                      ) : null}
                    </DragOverlay>
                  </DndContext>
                </div>
              </div>
            </div>

            {/* 添加技能按钮和跳转器 */}
            <div className="mt-6 flex justify-between items-center">
              <button
                type="button"
                onClick={addSkillItem}
                className="flex items-center justify-center gap-2 py-2 px-5 rounded-md transition-all bg-white text-[#333] border border-gray-200 hover:bg-gray-50 active:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-200 cursor-pointer shadow-sm whitespace-nowrap"
              >
                <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 4V12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                  <path d="M4 8H12" stroke="#333" strokeWidth="1.5" strokeLinecap="round"/>
                </svg>
                <span className="text-base font-medium">添加技能</span>
              </button>

              <ModuleNavigator currentModuleId="skills" />
            </div>
          </ScrollableContent>
        </div>
      </div>

      {/* 删除确认对话框 */}
      <ItemDeleteConfirmDialog
        open={deleteConfirmOpen}
        onOpenChange={setDeleteConfirmOpen}
        itemName={itemToDelete?.name || ''}
        itemType="技能"
        onConfirm={confirmDeleteSkillItem}
        disabled={skillItems.length <= 1}
        disabledReason="至少需要保留一个技能"
      />
    </div>
  );
}
