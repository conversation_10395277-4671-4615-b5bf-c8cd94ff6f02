'use client';

import React from 'react';
import { DialogOverlay } from '@/components/ui/dialog';
import { cn } from '@/lib/utils';

interface CustomDialogOverlayProps extends React.ComponentProps<typeof DialogOverlay> {
  onMouseDown?: (e: React.MouseEvent) => void;
}

export default function CustomDialogOverlay({
  className,
  onMouseDown,
  ...props
}: CustomDialogOverlayProps) {
  return (
    <DialogOverlay
      className={cn(className)}
      onMouseDown={(e) => {
        // 阻止事件冒泡，避免触发管理模块弹窗的关闭
        e.stopPropagation();
        onMouseDown?.(e);
      }}
      onClick={(e) => {
        // 阻止事件冒泡，避免触发管理模块弹窗的关闭
        e.stopPropagation();
      }}
      {...props}
    />
  );
}
