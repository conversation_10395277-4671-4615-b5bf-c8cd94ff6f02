'use client';

import React, { useState, useEffect } from 'react';
import { useModuleStore, Module } from '@/store/useModuleStore';
import { cn } from '@/lib/utils';
import { PencilIcon, TrashIcon } from 'lucide-react';
// 移除未使用的图标导入
import Image from 'next/image';
import {
  DndContext,
  closestCenter,
  KeyboardSensor,
  PointerSensor,
  useSensor,
  useSensors,
  DragEndEvent,
  DragStartEvent,
  DragOverlay,
  MeasuringStrategy,
} from '@dnd-kit/core';
import {
  SortableContext,
  sortableKeyboardCoordinates,
  useSortable,
  verticalListSortingStrategy,
} from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

// 可排序的模块项组件
const SortableModuleItem = ({
  module,
  onEditClick,
  onDeleteClick,
  onModuleClick,
}: {
  module: Module;
  onEditClick: (module: Module, e: React.MouseEvent) => void;
  onDeleteClick: (id: string, e: React.MouseEvent) => void;
  onModuleClick: (id: string) => void;
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
    isDragging,
  } = useSortable({ id: module.id });

  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
    zIndex: isDragging ? 999 : 'auto',
    position: 'relative' as const,
    // 确保拖拽后能够正确重置状态
    touchAction: 'none',
  };

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className={cn(
        "flex items-center justify-between p-2 rounded-md bg-gray-50 hover:bg-gray-100 mb-1.5",
        module.id !== 'basic_info' && "cursor-pointer",
        isDragging && "shadow-lg bg-white border-2 border-purple-200"
      )}
      onClick={(e) => {
        // 阻止事件冒泡，避免触发拖拽
        e.stopPropagation();
        // 切换到对应模块
        onModuleClick(module.id);
      }}
    >
      <div
        className="flex items-center gap-2 cursor-pointer flex-1"
      >
        {/* 拖拽手柄 - 只在非基本信息模块上显示 */}
        {module.id !== 'basic_info' && (
          <div
            className="w-5 h-5 flex items-center justify-center cursor-grab active:cursor-grabbing"
            {...listeners}
            onClick={(e) => e.stopPropagation()} // 阻止点击事件冒泡，避免触发模块切换
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M5.76 12.8C5.98091 12.8 6.16 12.6209 6.16 12.4L6.16 3.6C6.16 3.42426 6.02878 3.27103 5.8547 3.21704C5.68062 3.16304 5.49041 3.22047 5.38782 3.35946L3.64538 5.54798C3.51419 5.72573 3.55194 5.97617 3.72968 6.10735C3.90742 6.23854 4.15786 6.2008 4.28904 6.02306L5.36 4.57252L5.36 12.4C5.36 12.6209 5.53909 12.8 5.76 12.8Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M10.24 3.2C10.0191 3.2 9.84 3.37909 9.84 3.6L9.84 12.4C9.84 12.5757 9.97122 12.729 10.1453 12.783C10.3194 12.837 10.5096 12.7795 10.6122 12.6405L12.3546 10.452C12.4858 10.2743 12.4481 10.0238 12.2703 9.89265C12.0926 9.76146 11.8421 9.7992 11.711 9.97694L10.64 11.4275L10.64 3.6C10.64 3.37909 10.4609 3.2 10.24 3.2Z" fill="currentColor"></path>
            </svg>
          </div>
        )}

        <div className="relative">
          <span className="text-sm">{module.name}</span>
          {module.support_ai && (
            <Image
              src="/image/ai-icon.svg"
              alt="AI支持"
              width={16}
              height={12}
              className="absolute top-[-12px] right-[-16px] w-[16px] h-[12px]"
            />
          )}
        </div>
      </div>

      <div className="flex items-center gap-1">
        {/* 所有模块都可以编辑，但只有非必需模块可以删除 */}
        <button
          onClick={(e) => {
            e.stopPropagation(); // 阻止事件冒泡
            onEditClick(module, e);
          }}
          className="p-0.5 text-gray-500 hover:text-primary rounded-md cursor-pointer transition-colors"
        >
          <PencilIcon size={14} />
        </button>
        {!module.is_required && (
          <button
            onClick={(e) => {
              e.stopPropagation(); // 阻止事件冒泡
              onDeleteClick(module.id, e);
            }}
            className="p-0.5 text-gray-500 hover:text-red-500 rounded-md cursor-pointer transition-colors"
          >
            <TrashIcon size={14} />
          </button>
        )}
      </div>
    </div>
  );
};

// 模块项组件（用于拖拽预览）
const ModuleItem = ({
  module,
}: {
  module: Module;
}) => {
  return (
    <div
      className={cn(
        "flex items-center justify-between p-2 rounded-md bg-white shadow-lg border-2 border-purple-200",
        "w-[280px]" // 设置固定宽度，与原始元素相同
      )}
    >
      <div className="flex items-center gap-2">
        {/* 拖拽手柄 - 只在非基本信息模块上显示 */}
        {module.id !== 'basic_info' && (
          <div className="w-5 h-5 flex items-center justify-center cursor-grab">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
              <path fillRule="evenodd" clipRule="evenodd" d="M5.76 12.8C5.98091 12.8 6.16 12.6209 6.16 12.4L6.16 3.6C6.16 3.42426 6.02878 3.27103 5.8547 3.21704C5.68062 3.16304 5.49041 3.22047 5.38782 3.35946L3.64538 5.54798C3.51419 5.72573 3.55194 5.97617 3.72968 6.10735C3.90742 6.23854 4.15786 6.2008 4.28904 6.02306L5.36 4.57252L5.36 12.4C5.36 12.6209 5.53909 12.8 5.76 12.8Z" fill="currentColor"></path>
              <path fillRule="evenodd" clipRule="evenodd" d="M10.24 3.2C10.0191 3.2 9.84 3.37909 9.84 3.6L9.84 12.4C9.84 12.5757 9.97122 12.729 10.1453 12.783C10.3194 12.837 10.5096 12.7795 10.6122 12.6405L12.3546 10.452C12.4858 10.2743 12.4481 10.0238 12.2703 9.89265C12.0926 9.76146 11.8421 9.7992 11.711 9.97694L10.64 11.4275L10.64 3.6C10.64 3.37909 10.4609 3.2 10.24 3.2Z" fill="currentColor"></path>
            </svg>
          </div>
        )}

        <div className="relative">
          <span className="text-sm">{module.name}</span>
          {module.support_ai && (
            <Image
              src="/image/ai-icon.svg"
              alt="AI支持"
              width={16}
              height={12}
              className="absolute top-[-12px] right-[-16px] w-[16px] h-[12px]"
            />
          )}
        </div>
      </div>
    </div>
  );
};

// 可排序的模块列表组件
export default function SortableModuleList({
  onEditClick,
  onDeleteClick,
  onModuleClick,
}: {
  onEditClick: (module: Module, e: React.MouseEvent) => void;
  onDeleteClick: (id: string, e: React.MouseEvent) => void;
  onModuleClick: (id: string) => void;
}) {
  const { modules, customModules, reorderModules } = useModuleStore();
  const [activeId, setActiveId] = useState<string | null>(null);
  // 添加一个状态用于强制重新渲染
  const [renderKey, setRenderKey] = useState(0);

  // 监听 modules 和 customModules 的变化，强制重新渲染
  useEffect(() => {
    setRenderKey(prev => prev + 1);
  }, [modules, customModules]);

  // 合并 modules 和 customModules，过滤出可见的模块和有值的自定义模块，并按照 index 属性排序
  const visibleModules = Object.values(modules).filter(module => module.is_visible);
  const validCustomModules = customModules.filter(customModule => customModule.items && customModule.items.length > 0);

  const allModules: Module[] = [
    // 添加传统的 modules（只显示 is_visible: true 的）
    ...visibleModules,
    // 添加新的 customModules，转换为 Module 格式（只显示有值的自定义模块）
    ...validCustomModules.map(customModule => ({
        id: customModule.id,
        name: customModule.name,
        type: 'custom',
        is_visible: true, // customModules 中的模块都是可见的
        is_custom: true,
        is_required: false, // 自定义模块都不是必需的，可以删除
        support_ai: false,
        index: customModule.index,
        item: customModule.items
      } as Module))
  ];

  const sortedModules = allModules.sort((a, b) => a.index - b.index);

  // 获取当前拖拽的模块（从 sortedModules 中查找）
  const activeModule = activeId ? sortedModules.find(m => m.id === activeId) || null : null;

  // 设置传感器
  const sensors = useSensors(
    useSensor(PointerSensor, {
      // 降低拖拽激活阈值，使拖拽更容易触发
      activationConstraint: {
        distance: 4, // 只需要拖动4px就会触发拖拽
        tolerance: 5, // 增加容差，使拖拽更容易触发
        delay: 0, // 无延迟
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  // 处理拖拽开始
  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);
  };

  // 处理拖拽结束
  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (over && active.id !== over.id) {
      // 找到源模块和目标模块的索引
      const oldIndex = sortedModules.findIndex(m => m.id === active.id);
      const newIndex = sortedModules.findIndex(m => m.id === over.id);

      // 检查索引是否有效
      if (oldIndex === -1 || newIndex === -1) {
        setActiveId(null);
        return;
      }

      // 如果源模块或目标模块是基本信息模块，则不进行排序
      if (sortedModules[oldIndex].id === 'basic_info' || sortedModules[newIndex].id === 'basic_info') {
        setActiveId(null);
        return;
      }

      // 调用 reorderModules 方法重新排序模块
      reorderModules(oldIndex, newIndex);
    }

    // 重置拖拽状态
    setActiveId(null);

    // 强制重新渲染组件
    setRenderKey(prev => prev + 1);
  };

  return (
    <DndContext
      key={renderKey} // 使用 renderKey 作为 key，强制重新渲染
      sensors={sensors}
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      // 添加更多配置，确保拖拽正常工作
      autoScroll={true}
      measuring={{
        droppable: {
          strategy: MeasuringStrategy.Always,
        },
      }}
    >
      <SortableContext
        items={sortedModules.map(m => m.id)}
        strategy={verticalListSortingStrategy}
      >
        <div className="space-y-1.5">

          {sortedModules.map((module) => (
            <SortableModuleItem
              key={module.id}
              module={module}
              onEditClick={onEditClick}
              onDeleteClick={onDeleteClick}
              onModuleClick={onModuleClick}
            />
          ))}
        </div>
      </SortableContext>

      {/* 拖拽预览 */}
      <DragOverlay>
        {activeModule ? <ModuleItem module={activeModule} /> : null}
      </DragOverlay>
    </DndContext>
  );
}
