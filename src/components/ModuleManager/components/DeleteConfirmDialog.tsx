'use client';

import React from 'react';
import {
  Dialog,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import CustomDialogContent from './CustomDialogContent';
import { AlertCircle } from 'lucide-react';

interface DeleteConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  moduleName: string;
  onConfirm: () => void;
}

export default function DeleteConfirmDialog({
  open,
  onOpenChange,
  moduleName,
  onConfirm
}: DeleteConfirmDialogProps) {
  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
    >
      <CustomDialogContent
        className="sm:max-w-[425px]"
      >
        <DialogHeader>
          <div className="flex items-center justify-center gap-2 text-red-500">
            <AlertCircle className="w-6 h-6" />
            <DialogTitle className="text-center text-xl font-medium">温馨提示</DialogTitle>
          </div>
        </DialogHeader>

        <div className="py-6 text-center">
          <p className="text-lg">即将删除{moduleName}模块</p>
        </div>

        <DialogFooter className="flex justify-center gap-4">
          <DialogClose asChild>
            <button
              onClick={(e) => {
                e.stopPropagation();
              }}
              className="px-8 py-2 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors cursor-pointer"
            >
              取消
            </button>
          </DialogClose>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onConfirm();
              onOpenChange(false);
            }}
            className="px-8 py-2 text-sm bg-indigo-500 text-white rounded-md hover:bg-indigo-600 transition-colors cursor-pointer"
          >
            确认
          </button>
        </DialogFooter>
      </CustomDialogContent>
    </Dialog>
  );
}
