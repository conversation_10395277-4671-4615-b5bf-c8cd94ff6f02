'use client';

import React, { useState, useEffect, useMemo } from 'react';
import { Sparkles, FileText, Brain, Zap } from 'lucide-react';

interface AIGeneratingLoadingProps {
  isVisible: boolean;
  onComplete?: () => void;
  duration?: number; // 总时长，默认70秒
}

/**
 * AI生成简历的全屏Loading组件
 * 包含进度条和动态提示文字
 */
export default function AIGeneratingLoading({
  isVisible,
  onComplete,
  duration = 50000 // 50秒
}: AIGeneratingLoadingProps) {
  const [progress, setProgress] = useState(0);
  const [currentTip, setCurrentTip] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);

  // 动态提示文字
  const tips = useMemo(() => [
    { icon: Brain, text: 'AI正在深度分析您的个人信息...', time: 0 },
    { icon: FileText, text: '智能梳理教育背景与学术成就...', time: 4 },
    { icon: Sparkles, text: '精心包装项目经历与技术亮点...', time: 9 },
    { icon: Zap, text: '深度挖掘工作经历与职业成长...', time: 15 },
    { icon: Brain, text: '智能提炼技能专长与核心竞争力...', time: 20 },
    { icon: FileText, text: '精心构建荣誉墙与获奖经历...', time: 25 },
    { icon: Sparkles, text: '量身定制个人总结与职业规划...', time: 30 },
    { icon: Zap, text: '智能匹配最佳简历模板风格...', time: 35 },
    { icon: Brain, text: '优化关键词密度，提升ATS通过率...', time: 40 },
    { icon: FileText, text: '精雕细琢，完善排版与视觉效果...', time: 44 },
    { icon: Sparkles, text: '最终质检中，确保简历完美呈现...', time: 47 },
    { icon: Zap, text: '简历生成完成！即将为您呈现专属作品...', time: 48 }
  ], []);

  useEffect(() => {
    if (!isVisible) {
      setProgress(0);
      setCurrentTip(0);
      setElapsedTime(0);
      return;
    }

    const startTime = Date.now();
    const interval = setInterval(() => {
      const elapsed = Date.now() - startTime;
      setElapsedTime(elapsed);

      // 计算进度百分比
      const progressPercent = Math.min((elapsed / duration) * 100, 100);
      setProgress(progressPercent);

      // 更新提示文字
      const currentSeconds = elapsed / 1000;
      const tipIndex = tips.findIndex((tip, index) => {
        const nextTip = tips[index + 1];
        return currentSeconds >= tip.time && (!nextTip || currentSeconds < nextTip.time);
      });

      if (tipIndex !== -1) {
        setCurrentTip(tipIndex);
      }

      // 调试信息
      if (elapsed % 5000 < 100) { // 每5秒打印一次
      }

      // 完成时调用回调
      if (elapsed >= duration) {
        clearInterval(interval);
        onComplete?.();
      }
    }, 100);

    return () => clearInterval(interval);
  }, [isVisible, duration, onComplete, tips]);

  if (!isVisible) return null;

  const CurrentIcon = tips[currentTip]?.icon || Brain;
  const currentText = tips[currentTip]?.text || 'AI正在生成简历...';

  return (
    <div className="fixed inset-0 z-[9999] bg-gradient-to-br from-purple-900 to-blue-900 backdrop-blur-sm flex items-center justify-center">
      <div className="bg-white rounded-2xl p-8 shadow-2xl max-w-md mx-4 w-full">
        {/* 标题 */}
        <div className="text-center mb-8">
          <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
            <Sparkles className="w-8 h-8 text-white animate-pulse" />
          </div>
          <h2 className="text-2xl font-bold text-gray-900 mb-2">AI智能生成中</h2>
          <p className="text-gray-600">正在为您打造专属简历</p>
        </div>

        {/* 进度条 */}
        <div className="mb-8">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">生成进度</span>
            <span className="text-sm font-medium text-purple-600">{Math.round(progress)}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3 overflow-hidden">
            <div
              className="h-full bg-gradient-to-r from-purple-500 to-blue-500 rounded-full transition-all duration-300 ease-out relative"
              style={{ width: `${progress}%` }}
            >
              {/* 进度条光效 */}
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white to-transparent animate-pulse"></div>
            </div>
          </div>
        </div>

        {/* 当前状态 */}
        <div className="flex items-center justify-center space-x-3 mb-6">
          <div className="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
            <CurrentIcon className="w-5 h-5 text-purple-600 animate-pulse" />
          </div>
          <p className="text-gray-700 font-medium">{currentText}</p>
        </div>

        {/* 时间显示 */}
        <div className="text-center text-sm text-gray-500">
          <p>预计还需 {Math.max(0, Math.ceil((duration - elapsedTime) / 1000))} 秒</p>
        </div>

        {/* 底部装饰动画 */}
        <div className="flex justify-center space-x-2 mt-6">
          {[0, 1, 2].map((i) => (
            <div
              key={i}
              className="w-2 h-2 bg-purple-400 rounded-full animate-bounce"
              style={{ animationDelay: `${i * 0.2}s` }}
            ></div>
          ))}
        </div>
      </div>

      {/* 背景装饰 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* 浮动的装饰元素 */}
        {[...Array(6)].map((_, i) => (
          <div
            key={i}
            className="absolute w-20 h-20 bg-white bg-opacity-5 rounded-full animate-float"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 3}s`,
              animationDuration: `${3 + Math.random() * 2}s`
            }}
          ></div>
        ))}
      </div>

      <style jsx>{`
        @keyframes float {
          0%, 100% { transform: translateY(0px) rotate(0deg); }
          50% { transform: translateY(-20px) rotate(180deg); }
        }
        .animate-float {
          animation: float 4s ease-in-out infinite;
        }
      `}</style>
    </div>
  );
}
