'use client';

import React from 'react';

interface FullScreenLoadingProps {
  isVisible: boolean;
  message?: string;
}

/**
 * 全屏Loading组件
 * 用于智能一页功能的加载状态显示
 */
export default function FullScreenLoading({ 
  isVisible, 
  message = '智能调整中...' 
}: FullScreenLoadingProps) {
  if (!isVisible) return null;

  return (
    <div className="fixed inset-0 z-[9999] bg-black bg-opacity-50 backdrop-blur-sm flex items-center justify-center">
      <div className="bg-white rounded-lg p-8 shadow-2xl flex flex-col items-center space-y-4 max-w-sm mx-4">
        {/* 加载动画 */}
        <div className="relative">
          <div className="w-12 h-12 border-4 border-gray-200 rounded-full animate-spin border-t-purple-600"></div>
          <div className="absolute inset-0 w-12 h-12 border-4 border-transparent rounded-full animate-ping border-t-purple-300"></div>
        </div>
        
        {/* 加载文字 */}
        <div className="text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-1">
            {message}
          </h3>
          <p className="text-sm text-gray-500">
            正在为您优化简历布局，请稍候...
          </p>
        </div>
      </div>
    </div>
  );
}
