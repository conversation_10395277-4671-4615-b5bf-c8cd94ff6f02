'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import { useResumeStyleStore } from '@/store/useResumeStyleStore';

interface DiffMarkdownViewerProps {
  content: string;
  className?: string;
  style?: React.CSSProperties;
  removePadding?: boolean;
  inheritLineHeight?: boolean;
}

const DiffMarkdownViewer: React.FC<DiffMarkdownViewerProps> = ({
  content,
  className = '',
  style = {},
  removePadding = true,
  inheritLineHeight = true
}) => {
  const { font_family, font_gray, line_spacing } = useResumeStyleStore();

  // 如果没有内容，不渲染
  if (!content) {
    return null;
  }

  // 构建样式对象
  const viewerStyle: React.CSSProperties = {
    ...(removePadding && { padding: 0 }),
    ...(inheritLineHeight && { lineHeight: line_spacing }),
    fontFamily: font_family,
    color: font_gray,
    backgroundColor: '#f1f1fb', // 使用UI组件背景色
    ...style
  };

  return (
    <div className="rounded-xl p-6 border border-purple-100 relative" style={{ backgroundColor: '#f1f1fb' }}>
      {/* 左上角标签 */}
      <div className="absolute -top-0 left-0 text-white px-4 py-1.5 rounded-tr-lg rounded-tl-lg text-base font-medium bg-primary">
        修改前后对比
      </div>

      <div
        className={`rounded-lg p-4 shadow-sm mt-4 ${className}`}
        style={viewerStyle}
      >
        <ReactMarkdown
          components={{
            // 自定义组件样式 - 所有文字大小都小一号
            p: ({ children }) => <p className="mb-2 last:mb-0 text-sm">{children}</p>,
            ul: ({ children }) => <ul className="list-disc list-inside mb-2 text-sm">{children}</ul>,
            ol: ({ children }) => <ol className="list-decimal list-inside mb-2 text-sm">{children}</ol>,
            li: ({ children }) => <li className="mb-1 text-sm">{children}</li>,
            strong: ({ children }) => <strong className="font-semibold text-sm">{children}</strong>,
            em: ({ children }) => <em className="italic text-sm">{children}</em>,
            h1: ({ children }) => <h1 className="text-base font-bold mb-2">{children}</h1>,
            h2: ({ children }) => <h2 className="text-sm font-bold mb-2">{children}</h2>,
            h3: ({ children }) => <h3 className="text-xs font-bold mb-1">{children}</h3>,
          }}
        >
          {content}
        </ReactMarkdown>
      </div>
    </div>
  );
};

export default DiffMarkdownViewer;
