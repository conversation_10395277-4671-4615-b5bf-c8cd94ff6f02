'use client';

import { useEffect } from 'react';
import { getFingerprint, saveFingerprint, getFingerprintFromCookie, getFingerprintFromLocalStorage } from '@/lib/fingerprint';

export const FingerPrintInitializer = () => {
  useEffect(() => {
    const initializeFingerprint = async () => {
      try {
        // 检查Cookie和localStorage中是否已存在指纹
        const cookieFingerprint = getFingerprintFromCookie();
        const localStorageFingerprint = getFingerprintFromLocalStorage();

        // 如果已经存在指纹，则不再初始化
        if (cookieFingerprint && localStorageFingerprint) {
          return;
        }

        // 获取浏览器指纹
        const visitorId = await getFingerprint();

        // 保存指纹到Cookie和localStorage
        if (visitorId) {
          saveFingerprint(visitorId);
        }
      } catch {
        // 指纹初始化失败，静默处理
      }
    };

    if (typeof window !== 'undefined') {
      initializeFingerprint();
    }
  }, []);

  // 这个组件不渲染任何可见内容
  return null;
};

export default FingerPrintInitializer;
