'use client';

import React, { useState } from 'react';
import EditIcon from '@/assets/svg/edit-icon.svg';
import { TrashIcon } from 'lucide-react';
import { useModuleStore } from '@/store/useModuleStore';
import {
  Dialog,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import CustomDialogContent from '@/components/ModuleManager/components/CustomDialogContent';
import DeleteConfirmDialog from '@/components/ModuleManager/components/DeleteConfirmDialog';
import { toast } from 'sonner';

interface ModuleHeaderProps {
  /**
   * 模块标题
   */
  title: string;

  /**
   * 模块ID
   */
  id: string;

  /**
   * 模块描述信息
   */
  description: string;

  /**
   * 模块图标
   */
  icon: React.ReactElement<Record<string, unknown>>;

  /**
   * 点击编辑按钮的回调函数
   */
  onEdit?: () => void;

  /**
   * 点击删除按钮的回调函数
   */
  onDelete?: () => void;
}

/**
 * 模块头部组件，显示模块标题、描述和图标
 */
const ModuleHeader: React.FC<ModuleHeaderProps> = ({
  title,
  id,
  description,
  icon,
  onEdit,
  onDelete
}) => {
  const { modules, editModule, deleteModule, customModules } = useModuleStore();
  const [dialogOpen, setDialogOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // 从 store 中获取模块名称
  const moduleFromStore = modules[id];
  const moduleName = moduleFromStore?.name || title;

  const [editingModuleName, setEditingModuleName] = useState(moduleName);

  // 处理编辑模块名称
  const handleEditClick = () => {
    setEditingModuleName(moduleName);
    setDialogOpen(true);
    if (onEdit) {
      onEdit();
    }
  };

  // 保存编辑的模块名称
  const handleSaveEdit = () => {
    if (editingModuleName.trim() && editingModuleName !== moduleName) {
      editModule(id, editingModuleName.trim());
    }
    setDialogOpen(false);
  };

  // 取消编辑
  const handleCancelEdit = () => {
    setDialogOpen(false);
    setEditingModuleName(moduleName);
  };

  // 处理删除按钮点击
  const handleDeleteClick = () => {
    // 检查是否是必需模块，必需模块不能删除
    const moduleFromStore = modules[id] || customModules.find(cm => cm.id === id);
    if (moduleFromStore && moduleFromStore.is_required) {
      toast.error('必需模块不能删除');
      return;
    }

    setDeleteDialogOpen(true);
    if (onDelete) {
      onDelete();
    }
  };

  // 确认删除模块
  const handleConfirmDelete = () => {
    deleteModule(id);
    setDeleteDialogOpen(false);
    toast.success(`${moduleName}已删除`);
  };

  return (
    <>
      <div className="mb-2">
        <div className="flex gap-4 items-center">
          <div className="flex-shrink-0">
            {/* 图标放大 */}
            <div className="w-10 h-10 flex items-center justify-center">
              <div style={{ width: '34px', height: '34px' }}>{icon}</div>
            </div>
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-1 mb-1">
              <h2 className="text-lg font-semibold text-gray-800">{moduleName}</h2>
              <button
                onClick={handleEditClick}
                className="p-1 text-gray-500 hover:text-gray-700 rounded-md cursor-pointer transition-colors"
                aria-label="编辑"
              >
                <EditIcon width={14} height={14} />
              </button>
              {/* 删除按钮 - 只有非必需模块才显示 */}
              {(() => {
                const moduleFromStore = modules[id] || customModules.find(cm => cm.id === id);
                return !moduleFromStore?.is_required && (
                  <button
                    onClick={handleDeleteClick}
                    className="p-1 text-gray-500 hover:text-red-500 rounded-md cursor-pointer transition-colors"
                    aria-label="删除"
                  >
                    <TrashIcon size={14} />
                  </button>
                );
              })()}
            </div>
            <p className="text-gray-500 text-xs">{description}</p>
          </div>
        </div>
      </div>

      {/* 编辑模块对话框 */}
      <Dialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
      >
        <CustomDialogContent
          className="sm:max-w-[425px]"
        >
          <DialogHeader>
            <DialogTitle className="text-center text-xl font-medium">修改模块标题</DialogTitle>
          </DialogHeader>

          <div className="py-4 px-4" onClick={(e) => e.stopPropagation()}>
            <input
              type="text"
              value={editingModuleName}
              onChange={(e) => setEditingModuleName(e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  handleSaveEdit();
                } else if (e.key === 'Escape') {
                  handleCancelEdit();
                }
              }}
              autoFocus
              className="w-full px-3 py-2 border rounded-md text-sm focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary cursor-text"
              placeholder="请输入模块名称"
            />
          </div>

          <DialogFooter className="flex justify-end gap-2">
            <DialogClose asChild>
              <button
                onClick={handleCancelEdit}
                className="px-4 py-2 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors cursor-pointer"
              >
                取消
              </button>
            </DialogClose>
            <button
              onClick={handleSaveEdit}
              className="px-4 py-2 text-sm bg-primary text-white rounded-md hover:bg-purple-600 transition-colors cursor-pointer"
            >
              确定
            </button>
          </DialogFooter>
        </CustomDialogContent>
      </Dialog>

      {/* 删除确认对话框 */}
      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        moduleName={moduleName}
        onConfirm={handleConfirmDelete}
      />
    </>
  );
};

export default ModuleHeader;
