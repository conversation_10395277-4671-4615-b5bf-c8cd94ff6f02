'use client';

import React, { useState, useRef, useCallback, useEffect, useMemo } from 'react';
import NextImage from 'next/image';
import { Dialog, DialogContent, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AlertCircle, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { uploadApi } from '@/api/client';

// 滤镜类型定义
type FilterType = 'original' | 'summer' | 'vintage' | 'warm';

// 组件属性定义
interface ImageUploaderProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (imageUrl: string, filter: FilterType) => void;
  title?: string;
  maxSize?: number; // 单位：MB
}

// 裁剪区域尺寸
const CROP_WIDTH = 213;
const CROP_HEIGHT = 298;

export default function ImageUploader({
  open,
  onClose,
  onConfirm,
  title = '裁剪美化',
  maxSize = 7
}: ImageUploaderProps) {
  // 状态管理
  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [selectedFilter, setSelectedFilter] = useState<FilterType>('original');
  const [isDragging, setIsDragging] = useState(false);
  const [scale, setScale] = useState(1); // 默认值为1，使滑块位于中间
  const [position, setPosition] = useState({ x: 0, y: 0 });
  const [isDraggingImage, setIsDraggingImage] = useState(false);
  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });
  const [isUploading, setIsUploading] = useState(false); // 上传状态
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageRef = useRef<HTMLImageElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const originalFileRef = useRef<File | null>(null); // 保存原始文件引用

  // 滤镜配置
  const filters = useMemo(() => [
    { id: 'original', name: '原图', className: '' },
    { id: 'summer', name: '夏日', className: 'brightness-110 contrast-105' },
    { id: 'vintage', name: '复古', className: 'grayscale-[30%] sepia-[20%]' },
    { id: 'warm', name: '烤面包', className: 'sepia-[30%] saturate-150 brightness-105' }
  ], []);

  // 计算图片的初始缩放和位置
  const calculateInitialTransform = useCallback((img: HTMLImageElement) => {
    const imgWidth = img.naturalWidth;
    const imgHeight = img.naturalHeight;

    // 计算适合裁剪区域的缩放比例
    const scaleX = CROP_WIDTH / imgWidth;
    const scaleY = CROP_HEIGHT / imgHeight;

    // 使用较大的缩放比例，确保图片能够覆盖整个裁剪区域
    const initialScale = Math.max(scaleX, scaleY);

    // 计算居中位置
    const scaledWidth = imgWidth * initialScale;
    const scaledHeight = imgHeight * initialScale;
    const centerX = (CROP_WIDTH - scaledWidth) / 2;
    const centerY = (CROP_HEIGHT - scaledHeight) / 2;

    return {
      scale: initialScale,
      position: { x: centerX, y: centerY }
    };
  }, []);

  // 验证并处理文件
  const validateAndProcessFile = useCallback((file: File) => {
    // 检查文件类型
    const validTypes = ['image/jpeg', 'image/png', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      alert('请上传 JPG、PNG 或 WEBP 格式的图片');
      return;
    }

    // 检查文件大小
    if (file.size > maxSize * 1024 * 1024) {
      alert(`图片大小不能超过 ${maxSize}MB`);
      return;
    }

    // 创建文件预览
    const reader = new FileReader();
    reader.onload = (e) => {
      setSelectedImage(e.target?.result as string);
      setSelectedFilter('original');
      // 初始缩放和位置将在图片加载完成后设置
      setScale(1);
      setPosition({ x: 0, y: 0 });
    };
    reader.readAsDataURL(file);
  }, [maxSize, setSelectedImage, setSelectedFilter, setScale, setPosition]);

  // 处理文件选择
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // 保存原始文件引用，用于后续上传
      originalFileRef.current = file;
      validateAndProcessFile(file);
    }
  };

  // 处理拖放
  const handleDrop = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      // 保存原始文件引用，用于后续上传
      originalFileRef.current = file;
      validateAndProcessFile(file);
    }
  }, [validateAndProcessFile]);

  // 处理拖拽事件
  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  // 触发文件选择
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };



  // 处理图片拖动开始
  const handleImageDragStart = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!selectedImage) return;
    setIsDraggingImage(true);
    setDragStart({ x: e.clientX - position.x, y: e.clientY - position.y });
  }, [position, selectedImage]);

  // 处理图片拖动
  const handleImageDrag = useCallback((e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDraggingImage) return;

    // 计算新位置
    const newX = e.clientX - dragStart.x;
    const newY = e.clientY - dragStart.y;

    // 设置新位置
    setPosition({ x: newX, y: newY });
  }, [isDraggingImage, dragStart]);

  // 处理图片拖动结束
  const handleImageDragEnd = useCallback(() => {
    setIsDraggingImage(false);
  }, []);

  // 处理缩放变化
  const handleScaleChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newScale = parseFloat(e.target.value);
    setScale(newScale);
  }, []);

  // 绘制canvas预览
  const drawCanvas = useCallback((showDimensions = true) => {
    if (!selectedImage || !imageRef.current || !canvasRef.current) return;

    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 获取设备像素比，提高Canvas分辨率
    const dpr = window.devicePixelRatio || 1;
    const displayWidth = CROP_WIDTH;
    const displayHeight = CROP_HEIGHT;

    // 设置canvas实际尺寸（高分辨率）
    canvas.width = displayWidth * dpr;
    canvas.height = displayHeight * dpr;

    // 设置canvas显示尺寸
    canvas.style.width = displayWidth + 'px';
    canvas.style.height = displayHeight + 'px';

    // 缩放绘图上下文以匹配设备像素比
    ctx.scale(dpr, dpr);

    // 设置图像平滑算法为高质量
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // 先填充白色背景
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, displayWidth, displayHeight);

    // 绘制背景网格
    ctx.save();
    const gridSize = 20;
    ctx.fillStyle = '#F0F0F0';
    for (let x = 0; x < displayWidth; x += gridSize) {
      for (let y = 0; y < displayHeight; y += gridSize) {
        if ((x / gridSize + y / gridSize) % 2 === 0) {
          ctx.fillRect(x, y, gridSize, gridSize);
        }
      }
    }
    ctx.restore();

    // 绘制图片
    const img = imageRef.current;

    // 应用滤镜效果
    const filter = filters.find(f => f.id === selectedFilter);
    if (filter) {
      if (filter.id === 'summer') {
        ctx.filter = 'brightness(1.1) contrast(1.05)';
      } else if (filter.id === 'vintage') {
        ctx.filter = 'grayscale(30%) sepia(20%)';
      } else if (filter.id === 'warm') {
        ctx.filter = 'sepia(30%) saturate(1.5) brightness(1.05)';
      } else {
        ctx.filter = 'none';
      }
    }

    // 绘制图片，应用缩放和位置
    ctx.save();
    // 先移动到指定位置，再进行缩放
    ctx.translate(position.x, position.y);
    ctx.scale(scale, scale);
    // 绘制图片，从(0,0)开始绘制
    ctx.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight);
    ctx.restore();

    // 只在预览时绘制尺寸标记，导出时不绘制
    if (showDimensions) {
      ctx.save();
      ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
      ctx.fillRect(0, 0, 60, 18);
      ctx.fillStyle = '#FFFFFF';
      ctx.font = '10px Arial';
      ctx.fillText(`${displayWidth} × ${displayHeight}`, 5, 12);
      ctx.restore();
    }

  }, [selectedImage, position.x, position.y, scale, selectedFilter, filters]);

  // 当相关状态变化时更新canvas
  useEffect(() => {
    if (selectedImage && imageRef.current) {
      // 图片加载完成后绘制canvas
      const img = imageRef.current;
      if (img.complete) {
        // 如果是新加载的图片且位置为初始值，则计算初始变换
        if (position.x === 0 && position.y === 0 && scale === 1) {
          const transform = calculateInitialTransform(img);
          setScale(transform.scale);
          setPosition(transform.position);
        } else {
          drawCanvas(true);
        }
      } else {
        img.onload = () => {
          // 图片加载完成后，计算初始变换
          const transform = calculateInitialTransform(img);
          setScale(transform.scale);
          setPosition(transform.position);
        };
      }
    }
  }, [selectedImage, position.x, position.y, scale, selectedFilter, drawCanvas, calculateInitialTransform]);

  // 创建裁剪后的图片
  const createCroppedImage = useCallback(() => {
    if (!selectedImage || !imageRef.current) return null;

    // 创建一个临时的canvas元素用于导出，使用更高的分辨率
    const exportCanvas = document.createElement('canvas');
    const exportScale = 2; // 导出时使用2倍分辨率提高质量
    exportCanvas.width = CROP_WIDTH * exportScale;
    exportCanvas.height = CROP_HEIGHT * exportScale;

    // 在临时canvas上绘制图片，但不显示尺寸标记
    const ctx = exportCanvas.getContext('2d');
    if (!ctx) return null;

    // 设置高质量图像平滑
    ctx.imageSmoothingEnabled = true;
    ctx.imageSmoothingQuality = 'high';

    // 缩放绘图上下文
    ctx.scale(exportScale, exportScale);

    // 先填充白色背景
    ctx.fillStyle = '#FFFFFF';
    ctx.fillRect(0, 0, CROP_WIDTH, CROP_HEIGHT);

    // 绘制图片
    const img = imageRef.current;

    // 应用滤镜效果
    const filter = filters.find(f => f.id === selectedFilter);
    if (filter) {
      if (filter.id === 'summer') {
        ctx.filter = 'brightness(1.1) contrast(1.05)';
      } else if (filter.id === 'vintage') {
        ctx.filter = 'grayscale(30%) sepia(20%)';
      } else if (filter.id === 'warm') {
        ctx.filter = 'sepia(30%) saturate(1.5) brightness(1.05)';
      } else {
        ctx.filter = 'none';
      }
    }

    // 绘制图片，应用缩放和位置
    ctx.save();
    // 先移动到指定位置，再进行缩放
    ctx.translate(position.x, position.y);
    ctx.scale(scale, scale);
    // 绘制图片，从(0,0)开始绘制
    ctx.drawImage(img, 0, 0, img.naturalWidth, img.naturalHeight);
    ctx.restore();

    // 返回裁剪后的图片URL，使用更高的质量
    return exportCanvas.toDataURL('image/jpeg', 0.95);
  }, [selectedImage, position.x, position.y, scale, selectedFilter, filters]);

  // 确认选择
  const handleConfirm = async () => {
    if (selectedImage) {
      try {
        // 设置上传状态
        setIsUploading(true);

        // 获取裁剪后的图片
        const croppedImage = createCroppedImage();

        if (!croppedImage) {
          throw new Error('裁剪图片失败');
        }

        // 将Base64图片转换为File对象
        const base64Data = croppedImage.split(',')[1];
        const byteCharacters = atob(base64Data);
        const byteArrays = [];

        for (let i = 0; i < byteCharacters.length; i++) {
          byteArrays.push(byteCharacters.charCodeAt(i));
        }

        const byteArray = new Uint8Array(byteArrays);
        const blob = new Blob([byteArray], { type: 'image/jpeg' });

        // 使用原始文件名，但确保扩展名是jpg
        const originalFileName = originalFileRef.current?.name || 'avatar.jpg';
        const fileName = originalFileName.replace(/\.[^/.]+$/, '') + '.jpg';

        // 创建File对象，确保类型为image/jpeg
        const file = new File([blob], fileName, {
          type: 'image/jpeg',
          lastModified: new Date().getTime()
        });

        // 调用上传API
        const result = await uploadApi.uploadAvatar(file);

        // 上传成功，调用onConfirm回调
        onConfirm(result.url, selectedFilter);

        // 显示成功提示
        toast.success('头像上传成功', {
          position: 'top-center'
        });

        // 关闭对话框
        onClose();
      } catch (error) {
        // 显示错误提示
        toast.error('头像上传失败: ' + (error instanceof Error ? error.message : '未知错误'), {
          position: 'top-center'
        });
      } finally {
        // 重置上传状态
        setIsUploading(false);
      }
    }
  };

  // 计算动态缩放范围
  const getScaleRange = useCallback(() => {
    if (!imageRef.current) return { min: 0.1, max: 3 };

    const img = imageRef.current;
    const imgWidth = img.naturalWidth;
    const imgHeight = img.naturalHeight;

    // 计算刚好覆盖裁剪区域的缩放比例
    const minScaleX = CROP_WIDTH / imgWidth;
    const minScaleY = CROP_HEIGHT / imgHeight;
    const coverScale = Math.max(minScaleX, minScaleY);

    // 最小缩放比例设为覆盖缩放的70%，给用户留出调整余地
    const minScale = coverScale * 0.7;

    // 最大缩放比例设为覆盖缩放的3倍，但不超过3
    const maxScale = Math.min(coverScale * 3, 3);

    return { min: minScale, max: maxScale };
  }, []);

  // 重新上传
  const handleReupload = () => {
    // 重置状态
    setSelectedImage(null);
    setSelectedFilter('original');
    setScale(1); // 保持默认值为1，使滑块位于中间
    setPosition({ x: 0, y: 0 });
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] p-0 gap-0 overflow-hidden">
        <DialogTitle className="flex items-center p-5">
          <div className="flex items-center gap-2">
            <NextImage src="/star-icon.svg" alt="星形图标" width={32} height={32} />
            <span className="text-xl font-medium">{title}</span>
          </div>
        </DialogTitle>

        <div className="flex flex-col md:flex-row p-4 gap-4">
          {/* 左侧上传区域 */}
          <div className="w-full md:w-1/3">
            {!selectedImage ? (
              <div
                className={`border-2 border-dashed p-4 flex flex-col items-center justify-center cursor-pointer transition-colors ${
                  isDragging ? 'bg-opacity-10' : 'border-gray-300'
                }`}
                style={{
                  width: `${CROP_WIDTH}px`,
                  height: `${CROP_HEIGHT}px`,
                  borderColor: isDragging ? 'var(--primary, #3b82f6)' : '',
                  backgroundColor: isDragging ? 'var(--primary, #3b82f6)' : ''
                }}
                onClick={triggerFileInput}
                onDrop={handleDrop}
                onDragOver={handleDragOver}
                onDragLeave={handleDragLeave}
              >
                <div className="flex items-center justify-center mb-4">
                  <NextImage src="/upload-icon.svg" alt="上传图标" width={60} height={55} />
                </div>
                <p className="text-center text-gray-600 text-xs mb-1">点击上传或将图片拖拽到此区域</p>
                <p className="text-center text-gray-500 text-xs">
                  支持格式：jpg、png、webp
                </p>
                <input
                  type="file"
                  ref={fileInputRef}
                  onChange={handleFileChange}
                  accept="image/jpeg,image/png,image/webp"
                  className="hidden"
                />
              </div>
            ) : (
              <div className="flex flex-col items-center">
                {/* 裁剪预览区域 - Canvas实现 */}
                <div
                  className="border overflow-hidden relative mb-4"
                  style={{
                    width: `${CROP_WIDTH}px`,
                    height: `${CROP_HEIGHT}px`
                  }}
                  onMouseDown={handleImageDragStart}
                  onMouseMove={handleImageDrag}
                  onMouseUp={handleImageDragEnd}
                  onMouseLeave={handleImageDragEnd}
                >
                  <canvas
                    ref={canvasRef}
                    width={CROP_WIDTH}
                    height={CROP_HEIGHT}
                    className="cursor-grab"
                    style={{
                      cursor: isDraggingImage ? 'grabbing' : 'grab'
                    }}
                  />

                  {/* 隐藏的图片元素用于加载图片 */}
                  {selectedImage && (
                    <div className="hidden">
                      <img
                        ref={imageRef}
                        src={selectedImage}
                        alt="预览图"
                        onLoad={() => drawCanvas(true)}
                        draggable={false}
                      />
                    </div>
                  )}
                </div>

                {/* 缩放控制 */}
                <div className="flex items-center gap-2 w-full mb-4">
                  {(() => {
                    const scaleRange = getScaleRange();
                    return (
                      <>
                        <button
                          className="w-6 h-6 flex items-center justify-center text-gray-600 rounded-md border border-gray-200 hover:bg-gray-100 cursor-pointer transition-colors"
                          onClick={() => setScale(Math.max(scaleRange.min, scale - 0.1))}
                        >
                          <span className="text-sm font-medium">−</span>
                        </button>

                        <input
                          type="range"
                          min={scaleRange.min}
                          max={scaleRange.max}
                          step="0.05"
                          value={scale}
                          onChange={handleScaleChange}
                          className="flex-1"
                        />

                        <button
                          className="w-6 h-6 flex items-center justify-center text-gray-600 rounded-md border border-gray-200 hover:bg-gray-100 cursor-pointer transition-colors"
                          onClick={() => setScale(Math.min(scaleRange.max, scale + 0.1))}
                        >
                          <span className="text-sm font-medium">+</span>
                        </button>
                      </>
                    );
                  })()}
                </div>


              </div>
            )}
          </div>

          {/* 右侧滤镜和建议 */}
          <div className="w-full md:w-2/3 flex flex-col">


            {/* 滤镜预览 */}
            <div className="grid grid-cols-4 gap-2 mb-2">
              {filters.map((filter) => (
                <div
                  key={filter.id}
                  className="relative cursor-pointer flex flex-col"
                  onClick={() => {
                    if (selectedImage) {
                      setSelectedFilter(filter.id as FilterType);
                    } else {
                      // 显示提示
                      toast('请选择一张心仪照片', {
                        icon: <AlertCircle className="h-5 w-5 text-[#F8B254]" />,
                        position: 'top-center',
                        className: 'bg-[#FFF9F0] border border-[#FFECD1] text-[#F8B254] font-medium',
                        duration: 3000,
                      });
                    }
                  }}
                >
                  <div
                    className={`relative h-36 overflow-hidden ${
                      selectedFilter === filter.id ? 'ring-2' : ''
                    } ${!selectedImage ? 'opacity-80' : ''}`}
                    style={{
                      ...(selectedFilter === filter.id ? { '--tw-ring-color': 'var(--primary, #3b82f6)' } : {})
                    } as React.CSSProperties}
                  >
                    <NextImage
                      src={selectedImage || '/preview-img.png'}
                      alt={filter.name}
                      fill
                      className={`object-cover ${filter.className}`}
                    />
                    {selectedFilter === filter.id && selectedImage && (
                      <div
                        className="absolute top-2 right-2 rounded-full p-1 text-white"
                        style={{ backgroundColor: 'var(--primary, #3b82f6)' }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 text-white"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                      </div>
                    )}
                  </div>
                  <div className="text-center py-1 text-xs">{filter.name}</div>
                </div>
              ))}
            </div>

            {/* 裁剪建议 */}
            <div className="mt-2">
              <h3 className="flex items-center text-base font-medium mb-2">
                <span className="mr-1.5" style={{ color: 'var(--primary, #3b82f6)' }}>❤</span>
                裁剪建议
              </h3>
              <ol className="list-decimal pl-5 space-y-1 text-xs text-gray-700">
                <li>请将突出人物/主体放在画面中线条交叉位置（黄金分割点）；</li>
                <li>人物距离顶部和左右一定距离；</li>
                <li>为保证印刷质量，尽量使用原图上传。</li>
                <li>图片最大支持{maxSize}M大小</li>
                <li>此外，请勿上传违禁图</li>
              </ol>
            </div>
          </div>
        </div>

        <DialogFooter className="p-4">
          <Button
            variant="outline"
            onClick={handleReupload}
            disabled={!selectedImage || isUploading}
            className="mr-2 cursor-pointer hover:text-white hover:bg-gray-500 transition-colors"
          >
            重新上传
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!selectedImage || isUploading}
            className="text-white cursor-pointer hover:opacity-90 transition-opacity"
            style={{
              backgroundColor: 'var(--primary, #3b82f6)',
            }}
          >
            {isUploading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                上传中...
              </>
            ) : (
              '确定'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
