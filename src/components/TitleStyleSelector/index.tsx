'use client';

import React, { useState, useRef, useEffect } from 'react';
import { useResumeStyleStore } from '@/store/useResumeStyleStore';
import TitleStyle1 from '@/components/Title/Style1';
import TitleStyle2 from '@/components/Title/Style2';
import TitleStyle3 from '@/components/Title/Style3';
import TitleStyle4 from '@/components/Title/Style4';
import TitleStyle5 from '@/components/Title/Style5';
import TitleStyle6 from '@/components/Title/Style6';
import TitleStyle7 from '@/components/Title/Style7';
import TitleStyle8 from '@/components/Title/Style8';
import TitleStyle9 from '@/components/Title/Style9';
import ProjectIcon from '@/assets/svg/project-icon.svg';
import ChevronDownIcon from '@/assets/svg/chevron-down.svg';
import { cn } from '@/lib/utils';

interface TitleStyleSelectorProps {
  disabled?: boolean;
}

export default function TitleStyleSelector({ disabled = false }: TitleStyleSelectorProps) {
  const title_style = useResumeStyleStore(state => state.title_style);
  const updateStyle = useResumeStyleStore(state => state.updateStyle);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // 标题样式选项配置
  const titleStyleOptions = [
    { value: 'style1', component: <TitleStyle1 title="标题" /> },
    { value: 'style2', component: <TitleStyle2 title="标题" /> },
    { value: 'style3', component: <TitleStyle3 title="标题" /> },
    { value: 'style4', component: <TitleStyle4 title="标题" /> },
    { value: 'style5', component: <TitleStyle5 title="标题" icon={ProjectIcon} /> },
    { value: 'style6', component: <TitleStyle6 title="标题" icon={ProjectIcon} /> },
    { value: 'style7', component: <TitleStyle7 title="标题" icon={ProjectIcon} /> },
    { value: 'style8', component: <TitleStyle8 title="标题" /> },
    { value: 'style9', component: <TitleStyle9 title="标题" icon={ProjectIcon} /> },
  ];

  // 获取当前选中的样式组件
  const getCurrentStyleComponent = () => {
    const currentOption = titleStyleOptions.find(option => option.value === title_style);
    return currentOption?.component || titleStyleOptions[0].component;
  };

  const handleOptionClick = (value: string) => {
    if (!disabled) {
      updateStyle('title_style', value);
      setIsOpen(false);
    }
  };

  const handleTriggerClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
    }
  };

  // 点击外部关闭下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative w-full" ref={dropdownRef}>
      {/* 触发按钮 */}
      <div
        className={cn(
          "w-full h-auto py-2 px-3 border rounded-md flex items-center justify-between cursor-pointer transition-colors",
          disabled ? "bg-gray-50 cursor-not-allowed opacity-50" : "bg-white hover:border-primary",
          isOpen && "border-primary"
        )}
        onClick={handleTriggerClick}
      >
        <div className="flex-1">
          {getCurrentStyleComponent()}
        </div>
        <ChevronDownIcon
          className={cn(
            "ml-2 h-4 w-4 shrink-0 opacity-50 transition-transform",
            isOpen && "rotate-180"
          )}
        />
      </div>

      {/* 下拉菜单 */}
      {isOpen && !disabled && (
        <div className="absolute top-full left-0 right-0 mt-1 bg-white border rounded-md shadow-lg z-[9999] w-[300px] max-h-40 overflow-y-auto">
          {titleStyleOptions.map((option) => (
            <div
              key={option.value}
              className={cn(
                "py-3 px-4 cursor-pointer hover:bg-gray-50 border-b border-gray-100 last:border-b-0 min-h-[50px] flex items-center transition-colors",
                title_style === option.value && "bg-blue-50"
              )}
              onClick={() => handleOptionClick(option.value)}
            >
              {option.component}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
