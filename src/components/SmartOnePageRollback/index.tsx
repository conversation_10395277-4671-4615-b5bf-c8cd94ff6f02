'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useSmartOnePageStore } from '@/store/useSmartOnePageStore';
import { useResumeStyleStore } from '@/store/useResumeStyleStore';
import { toast } from 'sonner';

/**
 * 智能一页回滚按钮组件
 * 在智能一页调整完成后显示，提供5秒倒计时的回滚功能
 */
export default function SmartOnePageRollback() {
  const { 
    showRollbackButton, 
    savedParams, 
    setShowRollbackButton, 
    setSavedParams 
  } = useSmartOnePageStore();
  
  const { updateStyle } = useResumeStyleStore();
  
  const [countdown, setCountdown] = useState(10);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // 清理定时器的函数
  const clearTimer = () => {
    if (timerRef.current) {
      clearInterval(timerRef.current);
      timerRef.current = null;
    }
  };

  // 当按钮显示状态改变时重置倒计时
  useEffect(() => {
    if (showRollbackButton) {
      setCountdown(10);
    }
  }, [showRollbackButton]);

  // 倒计时逻辑
  useEffect(() => {
    if (!showRollbackButton) {
      clearTimer();
      return;
    }

    clearTimer(); // 清理之前的定时器

    timerRef.current = setInterval(() => {
      setCountdown(prev => {
        if (prev <= 1) {
          // 倒计时结束，使用 setTimeout 异步隐藏按钮
          setTimeout(() => {
            setShowRollbackButton(false);
            setSavedParams(null);
          }, 0);
          return 10;
        }
        return prev - 1;
      });
    }, 1000);

    // 清理函数
    return () => {
      clearTimer();
    };
  }, [showRollbackButton, setShowRollbackButton, setSavedParams]);

  // 处理回滚
  const handleRollback = () => {
    if (!savedParams) return;

    // 恢复保存的参数
    updateStyle('module_spacing', savedParams.module_spacing);
    updateStyle('line_spacing', savedParams.line_spacing);
    updateStyle('font_size', savedParams.font_size);

    // 清理定时器
    clearTimer();

    // 隐藏按钮并清除保存的参数
    setShowRollbackButton(false);
    setSavedParams(null);

    toast.success('已回滚到智能一页前的状态', {
      position: 'top-center'
    });
  };

  if (!showRollbackButton || !savedParams) {
    return null;
  }

  return (
    <div className="fixed bottom-8 left-1/2 transform -translate-x-1/2 z-50">
      <div className="relative">
        {/* 外层灰色边框 */}
        <div className="bg-gray-400 rounded-full p-1 shadow-lg">
          {/* 内层紫色按钮 */}
          <button
            onClick={handleRollback}
            className="text-white px-8 py-3 rounded-full transition-all duration-200 hover:opacity-90 active:scale-95 cursor-pointer"
            style={{
              background: 'linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%)',
              fontSize: '16px',
              fontWeight: '600',
              minWidth: '200px',
              boxShadow: '0 4px 12px rgba(99, 102, 241, 0.3)'
            }}
          >
            取消智能一页 ({countdown}s)
          </button>
        </div>
      </div>
    </div>
  );
}
