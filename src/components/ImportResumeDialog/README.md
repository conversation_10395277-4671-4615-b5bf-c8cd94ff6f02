# ImportResumeDialog 组件

导入简历弹窗组件，支持多种文件格式的简历导入功能。

## 功能特性

- 支持拖拽上传和点击选择文件
- 支持多种文件格式：pdf、txt、csv、docx、doc、xlsx、xls、pptx、ppt、md、mobi、epub
- 文件大小限制：最大 10MB
- 实时上传进度显示
- 文件验证和错误提示
- 使用 AI API 解析文件内容

## 使用方法

```tsx
import ImportResumeDialog from '@/components/ImportResumeDialog';

function MyComponent() {
  const [isOpen, setIsOpen] = useState(false);

  const handleImportSuccess = (content: string, filename: string) => {
    console.log('导入成功:', { content, filename });
    // 处理导入的内容
  };

  return (
    <>
      <button onClick={() => setIsOpen(true)}>
        导入简历
      </button>
      
      <ImportResumeDialog
        open={isOpen}
        onClose={() => setIsOpen(false)}
        onImportSuccess={handleImportSuccess}
      />
    </>
  );
}
```

## Props

| 属性 | 类型 | 必填 | 默认值 | 说明 |
|------|------|------|--------|------|
| open | boolean | 是 | - | 控制弹窗显示/隐藏 |
| onClose | () => void | 是 | - | 关闭弹窗的回调函数 |
| onImportSuccess | (content: string, filename: string) => void | 否 | - | 导入成功的回调函数 |

## 设计特点

- 使用项目统一的设计风格和颜色主题
- 响应式设计，适配不同屏幕尺寸
- 友好的用户交互体验
- 完整的错误处理和用户反馈
