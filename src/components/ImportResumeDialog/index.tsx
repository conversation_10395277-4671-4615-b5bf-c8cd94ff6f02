'use client';

import React, { useState, useRef, useCallback, useMemo } from 'react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Upload, FileText, X, CheckCircle, AlertCircle, Loader2, FileUp, Type } from 'lucide-react';
import { aiApi } from '@/api/client/ai';
import { PrivilegeType } from '@/api/client/types/ai';
import { usePrivilegeValidation } from '@/components/PrivilegeValidation';
import { toast } from 'sonner';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';
import AIGeneratingLoading from '@/components/AIGeneratingLoading';

interface ImportResumeDialogProps {
  open: boolean;
  onClose: () => void;
  templateId?: number; // 可选的模板ID
}

type TabType = 'file' | 'text';

interface UploadedFile {
  file: File;
  status: 'uploading' | 'success' | 'error';
  content?: string;
  error?: string;
  progress?: number;
}

/**
 * 导入简历弹窗组件
 * 支持拖拽上传和点击上传，支持多种文件格式
 */
export default function ImportResumeDialog({
  open,
  onClose,
  templateId
}: ImportResumeDialogProps) {
  const router = useRouter();

  // 权限校验 Hook
  const { validatePrivileges, PrivilegeValidationDialog } = usePrivilegeValidation();

  const [activeTab, setActiveTab] = useState<TabType>('file');
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [textContent, setTextContent] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [parseResult, setParseResult] = useState<{ content: string; filename: string } | null>(null);
  const [isParsingFile, setIsParsingFile] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // 支持的文件格式
  const supportedFormats = useMemo(() => [
    'pdf', 'txt', 'csv', 'docx', 'doc',
    'xlsx', 'xls', 'pptx', 'ppt', 'md', 'mobi', 'epub'
  ], []);

  // 最大文件大小 (10MB)
  const maxFileSize = 10 * 1024 * 1024;

  // 验证文件
  const validateFile = useCallback((file: File): string | null => {
    // 检查文件大小
    if (file.size > maxFileSize) {
      return `文件大小不能超过 10MB，当前文件大小：${(file.size / 1024 / 1024).toFixed(2)}MB`;
    }

    // 检查文件格式
    const fileExtension = file.name.split('.').pop()?.toLowerCase();
    if (!fileExtension || !supportedFormats.includes(fileExtension)) {
      return `不支持的文件格式，支持的格式：${supportedFormats.join(', ')}`;
    }

    return null;
  }, [maxFileSize, supportedFormats]);



  // 处理文件选择并立即解析
  const handleFiles = useCallback(async (files: FileList | File[]) => {
    const fileArray = Array.from(files);

    for (const file of fileArray) {
      const error = validateFile(file);
      if (error) {
        toast.error(error, { position: 'top-center' });
        continue;
      }

      // 设置选中的文件并开始解析
      setSelectedFile(file);
      setParseResult(null);
      setIsParsingFile(true);

      try {
        // 调用 AI 文件解析 API
        const result = await aiApi.parseFile(file);

        // 保存解析结果
        setParseResult({
          content: result.content,
          filename: result.filename
        });

        // 文件解析成功，不调用 onImportSuccess（避免关闭弹窗）
        // onImportSuccess?.(result.content, result.filename);

        toast.success(`文件 "${file.name}" 解析成功`, {
          position: 'top-center'
        });

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : '解析失败';
        toast.error(`文件 "${file.name}" 解析失败：${errorMessage}`, {
          position: 'top-center'
        });
        // 解析失败时清空选中的文件
        setSelectedFile(null);
      } finally {
        setIsParsingFile(false);
      }

      break; // 只处理第一个文件
    }
  }, [validateFile]);

  // 处理文件输入变化
  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
    // 重置文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [handleFiles]);

  // 处理拖拽事件
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = e.dataTransfer.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
  }, [handleFiles]);

  // 点击上传区域
  const handleUploadAreaClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);



  // 处理开始导入（调用 generateResume）
  const handleStartImport = useCallback(async () => {
    const content = parseResult?.content || textContent.trim();

    if (!content) {
      toast.error('没有可导入的内容', { position: 'top-center' });
      return;
    }

    setIsGenerating(true);

    try {
      // 权限校验 - 校验AI生成和简历创建权限
      const hasPrivilege = await validatePrivileges([
        PrivilegeType.PrivilegeAIGenerate,
        PrivilegeType.PrivilegeResumeCreate
      ]);

      // 如果权限校验失败，直接返回
      if (!hasPrivilege) {
        return;
      }

      // 构建 AI 提示词
      const prompt = `请根据以下简历内容，生成一份完整的简历数据：

${content}

请按照标准简历格式整理以上信息，包括基本信息、教育经历、工作经历、项目经历、技能特长、荣誉奖项等模块。`;

      // 调用AI生成简历接口，传入模板ID
      const generateRequest: { prompt: string; template_id?: number } = { prompt };
      if (templateId) {
        generateRequest.template_id = templateId;
      }

      const result = await aiApi.generateResume(generateRequest);

      // 显示成功提示
      toast.success('简历生成成功！正在跳转到预览页面...', {
        position: 'top-center'
      });

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        router.push(`/make/${result.resume_id}/`);
      }, 1000);

    } catch (error) {
      console.error('生成简历失败:', error);
      toast.error('生成简历失败，请稍后重试', {
        position: 'top-center'
      });
    } finally {
      setIsGenerating(false);
    }
  }, [parseResult, textContent, router, templateId, validatePrivileges]);

  // 移除文件
  const removeFile = useCallback((fileToRemove: File) => {
    setUploadedFiles(prev => prev.filter(f => f.file !== fileToRemove));
  }, []);

  // 关闭弹窗时清空状态
  const handleClose = useCallback(() => {
    if (!isParsingFile && !isGenerating) {
      setUploadedFiles([]);
      setIsDragOver(false);
      setTextContent('');
      setActiveTab('file');
      setSelectedFile(null);
      setParseResult(null);
      setIsParsingFile(false);
      setIsGenerating(false);
      onClose();
    }
  }, [isParsingFile, isGenerating, onClose]);

  return (
    <>
      {/* AI生成简历的全屏Loading */}
      <AIGeneratingLoading
        isVisible={isGenerating}
        onComplete={() => {
          // Loading完成后的处理在API调用的then中进行
        }}
        duration={50000} // 50秒
      />

      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent className="!w-[600px] !max-w-[600px] !p-0 !gap-0 overflow-hidden">
          <DialogTitle className="sr-only">导入简历</DialogTitle>
        
        <DialogHeader className="px-6 py-4 border-b border-gray-100">
          <div className="flex items-center justify-center">
            <h2 className="text-xl font-medium text-gray-900">简历导入</h2>
          </div>
        </DialogHeader>

        {/* 标签页 */}
        <div className="flex border-b border-gray-100">
          <button
            onClick={() => setActiveTab('file')}
            className={cn(
              "flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-colors",
              activeTab === 'file'
                ? "text-[#824dfc] border-b-2 border-[#824dfc] bg-purple-50"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            )}
            disabled={isParsingFile || isGenerating}
          >
            <FileUp size={16} />
            文件上传
          </button>
          <button
            onClick={() => setActiveTab('text')}
            className={cn(
              "flex-1 flex items-center justify-center gap-2 py-3 px-4 text-sm font-medium transition-colors",
              activeTab === 'text'
                ? "text-[#824dfc] border-b-2 border-[#824dfc] bg-purple-50"
                : "text-gray-500 hover:text-gray-700 hover:bg-gray-50"
            )}
            disabled={isParsingFile || isGenerating}
          >
            <Type size={16} />
            文本导入
          </button>
        </div>

        <div className="p-6">
          {/* 内容区域 - 固定高度避免切换抖动 */}
          <div className="min-h-[400px]">
            {/* 文件上传标签页内容 */}
            {activeTab === 'file' && (
              <>
                {/* 上传区域 */}
                <div
                  className={cn(
                    "relative border-2 border-dashed rounded-lg p-8 text-center transition-all h-[300px] flex items-center justify-center",
                    isDragOver
                      ? "border-[#824dfc] bg-purple-50"
                      : "border-gray-300",
                    (isParsingFile || parseResult) ? "border-gray-300" : "cursor-pointer hover:border-[#824dfc] hover:bg-gray-50",
                    (isParsingFile || parseResult) && "pointer-events-none"
                  )}
                  onDragOver={!isParsingFile && !parseResult ? handleDragOver : undefined}
                  onDragLeave={!isParsingFile && !parseResult ? handleDragLeave : undefined}
                  onDrop={!isParsingFile && !parseResult ? handleDrop : undefined}
                  onClick={!isParsingFile && !parseResult ? handleUploadAreaClick : undefined}
                >
                  <input
                    ref={fileInputRef}
                    type="file"
                    className="hidden"
                    accept={supportedFormats.map(format => `.${format}`).join(',')}
                    onChange={handleFileInputChange}
                    disabled={isParsingFile || !!parseResult}
                  />

                  {/* 解析中状态 */}
                  {isParsingFile && (
                    <div className="flex flex-col items-center space-y-4">
                      <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                        <Loader2 size={32} className="text-[#824dfc] animate-spin" />
                      </div>
                      <div>
                        <p className="text-lg font-medium text-gray-900 mb-2">
                          正在解析文件...
                        </p>
                        <p className="text-sm text-gray-500">
                          {selectedFile?.name}
                        </p>
                      </div>
                    </div>
                  )}

                  {/* 解析完成状态 */}
                  {parseResult && !isParsingFile && (
                    <div className="flex flex-col items-center space-y-4">
                      <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                        <CheckCircle size={32} className="text-green-500" />
                      </div>
                      <div>
                        <p className="text-lg font-medium text-gray-900 mb-2">
                          文件解析完成
                        </p>
                        <p className="text-sm text-gray-500 mb-1">
                          {parseResult.filename}
                        </p>
                        <p className="text-xs text-gray-400">
                          已成功解析 {parseResult.content.length} 个字符
                        </p>
                      </div>
                    </div>
                  )}

                  {/* 初始状态 */}
                  {!selectedFile && !isParsingFile && !parseResult && (
                    <div className="flex flex-col items-center space-y-4">
                      <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center">
                        <Upload size={32} className="text-[#824dfc]" />
                      </div>

                      <div>
                        <p className="text-lg font-medium text-gray-900 mb-2">
                          拖拽文件到此处，或点击选择文件
                        </p>
                        <p className="text-sm text-gray-500">
                          支持 {supportedFormats.join(', ')} 等多种格式
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          建议上传文字内容丰富，字数过少会影响生成效果。
                        </p>
                      </div>
                    </div>
                  )}


                </div>

                {/* 开始导入按钮 */}
                <div className="mt-4 flex justify-center">
                  {!selectedFile && !parseResult && !isParsingFile && (
                    <Button
                      onClick={() => fileInputRef.current?.click()}
                      className="px-8 py-2 bg-[#824dfc] hover:bg-[#7c3aed] text-white rounded-lg transition-colors"
                    >
                      选择文件
                    </Button>
                  )}

                  {isParsingFile && (
                    <Button
                      disabled
                      className="px-8 py-2 bg-[#824dfc] text-white rounded-lg opacity-50 cursor-not-allowed"
                    >
                      <Loader2 size={16} className="mr-2 animate-spin" />
                      解析中...
                    </Button>
                  )}

                  {parseResult && !isParsingFile && (
                    <Button
                      onClick={handleStartImport}
                      disabled={isGenerating}
                      className="px-8 py-2 bg-[#824dfc] hover:bg-[#7c3aed] text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 size={16} className="mr-2 animate-spin" />
                          生成中...
                        </>
                      ) : (
                        '开始导入'
                      )}
                    </Button>
                  )}
                </div>
              </>
            )}

            {/* 文本导入标签页内容 */}
            {activeTab === 'text' && (
              <div className="space-y-4 h-[350px] flex flex-col">
                <div className="flex-1 flex flex-col">
                  <textarea
                    value={textContent}
                    onChange={(e) => setTextContent(e.target.value)}
                    placeholder="请输入您的简历内容，支持纯文本格式..."
                    className="flex-1 w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-[#824dfc] focus:ring-2 focus:ring-purple-200 resize-none disabled:opacity-50 disabled:cursor-not-allowed"
                    disabled={isGenerating}
                  />
                </div>
                <p className="text-xs text-gray-500">
                  建议上传文字内容丰富，字数过少会影响生成效果。都会影响生成效果。
                </p>

                {/* 开始导入按钮 */}
                <div className="flex justify-center">
                  <Button
                    onClick={handleStartImport}
                    disabled={isGenerating || !textContent.trim()}
                    className="px-8 py-2 bg-[#824dfc] hover:bg-[#7c3aed] text-white rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isGenerating ? (
                      <>
                        <Loader2 size={16} className="mr-2 animate-spin" />
                        生成中...
                      </>
                    ) : (
                      '开始导入'
                    )}
                  </Button>
                </div>
              </div>
            )}
          </div>

          {/* 上传文件列表 - 只在文件上传标签页显示 */}
          {activeTab === 'file' && uploadedFiles.length > 0 && (
            <div className="mt-6 space-y-3">
              <h3 className="text-sm font-medium text-gray-900">上传文件</h3>
              {uploadedFiles.map((uploadedFile, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                >
                  <div className="flex items-center space-x-3">
                    <FileText size={20} className="text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {uploadedFile.file.name}
                      </p>
                      <p className="text-xs text-gray-500">
                        {(uploadedFile.file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    {uploadedFile.status === 'uploading' && (
                      <Loader2 size={16} className="text-[#824dfc] animate-spin" />
                    )}
                    {uploadedFile.status === 'success' && (
                      <CheckCircle size={16} className="text-green-500" />
                    )}
                    {uploadedFile.status === 'error' && (
                      <AlertCircle size={16} className="text-red-500" />
                    )}

                    {uploadedFile.status !== 'uploading' && (
                      <button
                        onClick={() => removeFile(uploadedFile.file)}
                        className="p-1 hover:bg-gray-200 rounded transition-colors"
                      >
                        <X size={14} className="text-gray-400" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>


      </DialogContent>
    </Dialog>

    {/* 权限校验弹窗 */}
    <PrivilegeValidationDialog />
    </>
  );
}
