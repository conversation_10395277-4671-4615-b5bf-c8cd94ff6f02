"use client"

import * as React from "react"
import * as ResizablePrimitive from "react-resizable-panels"
import { cn } from "@/lib/utils"

function CustomResizableHandle({
  className,
  ...props
}: React.ComponentProps<typeof ResizablePrimitive.PanelResizeHandle>) {
  return (
    <ResizablePrimitive.PanelResizeHandle
      data-slot="resizable-handle"
      className={cn(
        "bg-border focus-visible:ring-ring relative flex w-px items-center justify-center after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:ring-1 focus-visible:ring-offset-1 focus-visible:outline-hidden data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90 group",
        className
      )}
      {...props}
    >
      {/* 整个区域 hover 时显示垂直排列的文字 */}
      <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
        <div className="hidden group-hover:flex flex-col items-center justify-center text-white text-xs font-medium leading-tight">
          <span>按</span>
          <span>住</span>
          <span>左</span>
          <span>右</span>
          <span>拖</span>
          <span>动</span>
          <span className="mt-1">调</span>
          <span>整</span>
          <span>预</span>
          <span>览</span>
          <span>尺</span>
          <span>寸</span>
        </div>
      </div>
    </ResizablePrimitive.PanelResizeHandle>
  )
}

export { CustomResizableHandle }
