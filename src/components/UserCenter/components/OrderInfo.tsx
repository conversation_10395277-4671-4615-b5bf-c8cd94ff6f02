'use client';

import React, { useState } from 'react';
import { UserResponse } from '@/api/client/types/user';
import { PaymentStatus } from '@/api/client/types/order';
import OrderList from './OrderList';

interface OrderInfoProps {
  userInfo: UserResponse | null;
}

/**
 * 订单信息组件
 */
export default function OrderInfo({ userInfo }: OrderInfoProps) {
  const [activeSubTab, setActiveSubTab] = useState<'all' | 'pending' | 'success' | 'failed'>('success');

  const subTabs = [
    { key: 'success', label: '支付成功' },
    { key: 'pending', label: '待支付' },
    { key: 'failed', label: '支付失败' },
    { key: 'all', label: '全部订单' },
  ];

  const renderSubContent = () => {
    // 如果是"所有"，不传payment_status参数
    if (activeSubTab === 'all') {
      return <OrderList userInfo={userInfo} />;
    }

    // 根据选择的状态组合传递对应的支付状态数组
    let paymentStatusArray: PaymentStatus[] = [];

    switch (activeSubTab) {
      case 'pending':
        // 待支付 = 待支付 + 支付处理中
        paymentStatusArray = [PaymentStatus.Pending, PaymentStatus.Processing];
        break;
      case 'success':
        // 支付成功
        paymentStatusArray = [PaymentStatus.Success];
        break;
      case 'failed':
        // 支付失败 = 支付失败 + 支付超时
        paymentStatusArray = [PaymentStatus.Failed, PaymentStatus.Timeout];
        break;
    }

    return <OrderList userInfo={userInfo} paymentStatusArray={paymentStatusArray} />;
  };

  return (
    <div className="space-y-6">
      {/* 子标签导航 */}
      <div className="flex space-x-8 border-b border-gray-200">
        {subTabs.map((tab) => (
          <button
            key={tab.key}
            onClick={() => setActiveSubTab(tab.key as 'all' | 'pending' | 'success' | 'failed')}
            className={`pb-3 text-base font-medium transition-colors relative ${
              activeSubTab === tab.key
                ? 'text-purple-600'
                : 'text-gray-600 hover:text-gray-800'
            }`}
          >
            {tab.label}
            {activeSubTab === tab.key && (
              <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-purple-600"></div>
            )}
          </button>
        ))}
      </div>

      {/* 子内容区域 */}
      <div>
        {renderSubContent()}
      </div>
    </div>
  );
}
