'use client';

import React, { useState, useRef } from 'react';
import { UserResponse, UserType } from '@/api/client/types/user';
import { userApi } from '@/api/client/user';
import { authApi } from '@/api/client/auth';
import { clearToken } from '@/lib/auth';
import { useUserStore } from '@/store/useUserStore';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import Image from 'next/image';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import WechatBindDialog from './WechatBindDialog';

// 手机号绑定表单的 zod schema
const phoneBindSchema = z.object({
  phone: z
    .string()
    .min(1, '请输入手机号')
    .regex(/^1[3-9]\d{9}$/, '请输入正确的手机号格式'),
  verificationCode: z
    .string()
    .min(1, '请输入验证码')
    .min(4, '验证码至少4位')
    .max(6, '验证码最多6位'),
});

// 邮箱绑定表单的 zod schema
const emailBindSchema = z.object({
  email: z
    .string()
    .min(1, '请输入邮箱')
    .email('请输入正确的邮箱格式'),
  verificationCode: z
    .string()
    .min(1, '请输入验证码')
    .min(4, '验证码至少4位')
    .max(6, '验证码最多6位'),
});

type PhoneBindFormData = z.infer<typeof phoneBindSchema>;
type EmailBindFormData = z.infer<typeof emailBindSchema>;

interface BasicInfoProps {
  userInfo: UserResponse | null;
}

/**
 * 基本信息组件
 */
export default function BasicInfo({ userInfo: initialUserInfo }: BasicInfoProps) {
  const { resetUserInfo, getGuestUserInfo, username, avatar, phone, email, user_type, open_id } = useUserStore();
  const router = useRouter();

  // 使用 store 中的数据，如果 store 中没有数据则使用 props 中的数据
  const userInfo = username ? {
    username,
    avatar,
    phone,
    email,
    user_type,
    open_id
  } : initialUserInfo;

  // 编辑用户名状态
  const [isEditingUsername, setIsEditingUsername] = useState(false);
  const [editingUsername, setEditingUsername] = useState('');

  // 手机号绑定弹窗状态
  const [showPhoneModal, setShowPhoneModal] = useState(false);
  const [countdown, setCountdown] = useState(0);

  // 邮箱绑定弹窗状态
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [emailCountdown, setEmailCountdown] = useState(0);

  // 微信绑定弹窗状态
  const [showWechatModal, setShowWechatModal] = useState(false);

  // 手机号绑定表单设置
  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { errors },
  } = useForm<PhoneBindFormData>({
    resolver: zodResolver(phoneBindSchema),
    defaultValues: {
      phone: '',
      verificationCode: '',
    },
  });

  // 邮箱绑定表单设置
  const {
    register: emailRegister,
    handleSubmit: emailHandleSubmit,
    watch: emailWatch,
    reset: emailReset,
    formState: { errors: emailErrors },
  } = useForm<EmailBindFormData>({
    resolver: zodResolver(emailBindSchema),
    defaultValues: {
      email: '',
      verificationCode: '',
    },
  });

  // 监听手机号表单字段变化
  const phoneNumber = watch('phone');
  const verificationCode = watch('verificationCode');

  // 监听邮箱表单字段变化
  const emailAddress = emailWatch('email');
  const emailVerificationCode = emailWatch('verificationCode');

  // 检查手机号格式是否正确
  const isPhoneValid = phoneBindSchema.shape.phone.safeParse(phoneNumber).success;

  // 检查验证码格式是否正确
  const isCodeValid = phoneBindSchema.shape.verificationCode.safeParse(verificationCode).success;

  // 发送按钮是否可用：手机号格式正确且不在倒计时中
  const canSendCode = isPhoneValid && countdown === 0;

  // 绑定按钮是否可用：手机号和验证码都格式正确
  const canSubmit = isPhoneValid && isCodeValid;

  // 检查邮箱格式是否正确
  const isEmailValid = emailBindSchema.shape.email.safeParse(emailAddress).success;

  // 检查邮箱验证码格式是否正确
  const isEmailCodeValid = emailBindSchema.shape.verificationCode.safeParse(emailVerificationCode).success;

  // 邮箱发送按钮是否可用：邮箱格式正确且不在倒计时中
  const canSendEmailCode = isEmailValid && emailCountdown === 0;

  // 邮箱绑定按钮是否可用：邮箱和验证码都格式正确
  const canSubmitEmail = isEmailValid && isEmailCodeValid;

  // 文件选择ref
  const fileInputRef = useRef<HTMLInputElement>(null);

  const getUserTypeText = (userType: UserType) => {
    switch (userType) {
      case UserType.Guest:
        return '游客';
      case UserType.Regular:
        return '普通用户';
      case UserType.Member:
        return '会员';
      default:
        return '未知';
    }
  };

  // 处理点击头像
  const handleAvatarClick = () => {
    fileInputRef.current?.click();
  };

  // 处理手机号绑定
  const handlePhoneBind = () => {
    setShowPhoneModal(true);
  };

  // 处理邮箱绑定
  const handleEmailBind = () => {
    setShowEmailModal(true);
  };

  // 处理微信绑定
  const handleWechatBind = () => {
    setShowWechatModal(true);
  };

  // 处理复制功能
  const handleCopy = async (text: string, type: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success(`${type}已复制到剪贴板`, {
        position: 'top-center'
      });
    } catch (error) {
      toast.error('复制失败，请手动复制', {
        position: 'top-center'
      });
    }
  };

  // 处理发送验证码
  const handleSendCode = async () => {
    // 使用 zod 验证手机号
    const phoneValidation = phoneBindSchema.shape.phone.safeParse(phoneNumber);

    if (!phoneValidation.success) {
      toast.error(phoneValidation.error.errors[0].message, {
        position: 'top-center'
      });
      return;
    }

    // 调用发送短信验证码接口
    await authApi.sendSMSCode(phoneNumber);

    // 开始倒计时
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    toast.success('验证码已发送', {
      position: 'top-center'
    });
  };

  // 处理发送邮箱验证码
  const handleSendEmailCode = async () => {
    // 使用 zod 验证邮箱
    const emailValidation = emailBindSchema.shape.email.safeParse(emailAddress);

    if (!emailValidation.success) {
      toast.error(emailValidation.error.errors[0].message, {
        position: 'top-center'
      });
      return;
    }

    // 调用发送邮箱验证码接口
    await authApi.sendEmailCode(emailAddress);

    // 开始倒计时
    setEmailCountdown(60);
    const timer = setInterval(() => {
      setEmailCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);

    toast.success('验证码已发送', {
      position: 'top-center'
    });
  };

  // 处理绑定提交
  const onSubmit = async (data: PhoneBindFormData) => {
    // 调用绑定手机号API
    await userApi.bindPhone(data.phone, data.verificationCode);

    toast.success('手机号绑定成功', {
      position: 'top-center'
    });

    // 关闭弹窗并重置状态
    setShowPhoneModal(false);
    reset();
    setCountdown(0);

    // 刷新用户信息
    try {
      await getGuestUserInfo();
    } catch (error) {
    }
  };

  // 处理邮箱绑定提交
  const onEmailSubmit = async (data: EmailBindFormData) => {
    // 调用绑定邮箱API
    await userApi.bindEmail(data.email, data.verificationCode);

    toast.success('邮箱绑定成功', {
      position: 'top-center'
    });

    // 关闭弹窗并重置状态
    setShowEmailModal(false);
    emailReset();
    setEmailCountdown(0);

    // 刷新用户信息
    try {
      await getGuestUserInfo();
    } catch (error) {
    }
  };

  // 处理弹窗状态变化
  const handleDialogChange = (open: boolean) => {
    setShowPhoneModal(open);
    if (!open) {
      // 关闭时重置状态
      reset();
      setCountdown(0);
    }
  };

  // 处理邮箱弹窗状态变化
  const handleEmailDialogChange = (open: boolean) => {
    setShowEmailModal(open);
    if (!open) {
      // 关闭时重置状态
      emailReset();
      setEmailCountdown(0);
    }
  };



  // 处理文件选择
  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      toast.error('请选择jpg、png或webp格式的图片', {
        position: 'top-center'
      });
      return;
    }

    // 验证文件大小 (7MB = 7 * 1024 * 1024 bytes)
    const maxSize = 7 * 1024 * 1024;
    if (file.size > maxSize) {
      toast.error('图片大小不能超过7MB', {
        position: 'top-center'
      });
      return;
    }

    // 调用修改头像API
    await userApi.updateAvatar(file);

    // 刷新用户信息
    await getGuestUserInfo();

    // 显示成功提示
    toast.success('头像修改成功', {
      position: 'top-center'
    });

    // 清空文件选择，允许重复选择同一文件
    event.target.value = '';
  };

  // 处理开始编辑用户名
  const handleStartEditUsername = () => {
    setEditingUsername(userInfo?.username || '');
    setIsEditingUsername(true);
  };

  // 处理取消编辑用户名
  const handleCancelEditUsername = () => {
    setIsEditingUsername(false);
    setEditingUsername('');
  };

  // 处理保存用户名
  const handleSaveUsername = async () => {
    if (!editingUsername.trim()) {
      toast.error('用户名不能为空', {
        position: 'top-center'
      });
      return;
    }

    if (editingUsername.length < 2 || editingUsername.length > 20) {
      toast.error('用户名长度必须在2-20字符之间', {
        position: 'top-center'
      });
      return;
    }

    // 调用修改用户名API
    await userApi.updateUsername(editingUsername.trim());

    // 刷新用户信息
    await getGuestUserInfo();

    // 显示成功提示
    toast.success('用户名修改成功', {
      position: 'top-center'
    });

    // 退出编辑模式
    setIsEditingUsername(false);
    setEditingUsername('');
  };

  // 处理退出登录
  const handleLogout = async () => {
    // 调用退出登录API
    await userApi.logout();

    // 清除本地token和cookie
    clearToken();

    // 重置用户信息为默认状态
    resetUserInfo();

    // 显示成功提示
    toast.success('退出登录成功', {
      position: 'top-center'
    });

    // 使用router.push跳转到首页
    router.push('/');
  };



  if (!userInfo) {
    return (
      <div className="text-center py-8 text-gray-500">
        <p>暂无用户信息</p>
      </div>
    );
  }

  return (
    <div className="space-y-8 w-1/3">
      {/* 用户头像和基本信息 */}
      <div className="flex items-center p-6 bg-white rounded-xl shadow-[0_4px_20px_rgba(0,0,0,0.08)] hover:shadow-[0_8px_30px_rgba(0,0,0,0.12)] transition-shadow duration-300">
        <div className="flex items-center space-x-4">
          <div
            className="relative w-16 h-16 rounded-lg overflow-hidden bg-purple-100 flex items-center justify-center cursor-pointer group"
            onClick={handleAvatarClick}
          >
            <Image
              src={userInfo.avatar || 'https://cdn.shineresume.com/avatar/6.svg'}
              alt="用户头像"
              width={64}
              height={64}
              className="w-full h-full object-cover transition-opacity duration-300 group-hover:opacity-70"
            />
            {/* 悬停蒙版和相机图标 */}
            <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <svg
                className="w-6 h-6 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"
                />
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 13a3 3 0 11-6 0 3 3 0 016 0z"
                />
              </svg>
            </div>

            {/* 隐藏的文件输入框 */}
            <input
              ref={fileInputRef}
              type="file"
              accept="image/jpeg,image/jpg,image/png,image/webp"
              onChange={handleFileChange}
              className="hidden"
            />
          </div>
          <div>
            <div className="flex items-center space-x-3">
              {isEditingUsername ? (
                <div className="flex items-center space-x-2">
                  <input
                    type="text"
                    value={editingUsername}
                    onChange={(e) => setEditingUsername(e.target.value)}
                    className="text-base font-medium text-gray-900 bg-white border border-gray-300 rounded-lg px-2 py-1 w-32 focus:outline-none focus:ring-1 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200"
                    placeholder="请输入用户名"
                    maxLength={20}
                    autoFocus
                  />
                  <button
                    onClick={handleSaveUsername}
                    className="px-2 py-1 bg-green-500 text-white text-xs hover:bg-green-600 rounded-lg transition-colors cursor-pointer"
                  >
                    保存
                  </button>
                  <button
                    onClick={handleCancelEditUsername}
                    className="px-2 py-1 bg-gray-100 text-gray-700 text-xs hover:bg-gray-200 rounded-lg transition-colors cursor-pointer"
                  >
                    取消
                  </button>
                </div>
              ) : (
                <>
                  <h2 className="text-xl font-medium text-gray-900">{userInfo.username}</h2>
                  <button
                    onClick={handleStartEditUsername}
                    className="flex items-center px-2 py-1 text-gray-600 text-sm hover:bg-gray-50 rounded transition-colors cursor-pointer"
                  >
                    <img src="/assets/svg/edit-icon.svg" alt="编辑" className="w-4 h-4 mr-1" />
                    编辑
                  </button>
                </>
              )}
            </div>
            <div className="mt-1">
              <span className={`inline-flex items-center px-2 py-1 rounded text-sm font-medium ${
                userInfo.user_type === UserType.Member
                  ? 'bg-purple-100 text-purple-800'
                  : userInfo.user_type === UserType.Regular
                  ? 'bg-blue-100 text-blue-800'
                  : 'bg-gray-100 text-gray-800'
              }`}>
                {getUserTypeText(userInfo.user_type)}
              </span>
            </div>
          </div>
        </div>
      </div>



      {/* 账号设置 */}
      <div className="space-y-6 p-6 bg-white rounded-xl shadow-[0_4px_20px_rgba(0,0,0,0.08)] hover:shadow-[0_8px_30px_rgba(0,0,0,0.12)] transition-shadow duration-300">
        <h3 className="text-lg font-medium text-gray-900">账号设置</h3>

        <div className="space-y-4">
          {/* 手机号 */}
          <div className="flex items-center justify-between py-3">
            <span className="text-gray-700">手机号</span>
            <div className="flex items-center space-x-3">
              <span className="text-gray-900">{userInfo.phone || '未绑定'}</span>
              <button
                onClick={userInfo.phone ? () => handleCopy(userInfo.phone!, '手机号') : handlePhoneBind}
                className="text-purple-600 text-sm hover:text-purple-800 cursor-pointer"
              >
                {userInfo.phone ? '复制' : '绑定'}
              </button>
            </div>
          </div>

          {/* 微信 */}
          <div className="flex items-center justify-between py-3">
            <span className="text-gray-700">微信</span>
            <div className="flex items-center space-x-3">
              <span className="text-gray-900">{userInfo.open_id ? userInfo.open_id : '未绑定'}</span>
              <button
                onClick={userInfo.open_id ? () => handleCopy(userInfo.open_id!, '微信') : handleWechatBind}
                className="text-purple-600 text-sm hover:text-purple-800 cursor-pointer"
              >
                {userInfo.open_id ? '复制' : '绑定'}
              </button>
            </div>
          </div>

          {/* 邮箱 */}
          <div className="flex items-center justify-between py-3">
            <span className="text-gray-700">邮箱</span>
            <div className="flex items-center space-x-3">
              <span className="text-gray-900">{userInfo.email || '未绑定'}</span>
              <button
                onClick={userInfo.email ? () => handleCopy(userInfo.email!, '邮箱') : handleEmailBind}
                className="text-purple-600 text-sm hover:text-purple-800 cursor-pointer"
              >
                {userInfo.email ? '复制' : '绑定'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* 退出登录 */}
      <div className="pt-6 flex justify-center">
        <button
          onClick={handleLogout}
          className="bg-red-600 text-white text-base hover:bg-red-700 transition-colors px-6 py-2 rounded-lg cursor-pointer"
        >
          退出登录
        </button>
      </div>

      {/* 手机号绑定弹窗 */}
      <Dialog open={showPhoneModal} onOpenChange={handleDialogChange}>
        <DialogContent className="bg-gradient-to-br from-purple-100 to-blue-100 rounded-2xl p-8 w-96 max-w-md border-0">
          <DialogHeader className="text-center mb-6">
            <DialogTitle className="text-xl font-bold text-gray-800 mb-2">
              绑定手机号
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            {/* 手机号输入 */}
            <div className="mb-4">
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <img src="/user/phone.svg" alt="手机" className="w-5 h-5" />
                </div>
                <input
                  type="tel"
                  {...register('phone')}
                  placeholder="请输入手机号"
                  className="w-full pl-10 pr-4 py-3 border border-gray-400 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                  maxLength={11}
                />
              </div>
              {errors.phone && (
                <p className="text-red-500 text-sm mt-1">{errors.phone.message}</p>
              )}
            </div>

            {/* 验证码输入和发送按钮 */}
            <div className="mb-6">
              <div className="flex space-x-3 items-start">
                <div className="flex-1">
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <img src="/user/verification-code.svg" alt="验证码" className="w-5 h-5" />
                    </div>
                    <input
                      type="text"
                      {...register('verificationCode')}
                      placeholder="请输入验证码"
                      className="w-full pl-10 pr-4 py-3 border border-gray-400 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      maxLength={6}
                    />
                  </div>
                </div>
                <button
                  type="button"
                  onClick={handleSendCode}
                  disabled={!canSendCode}
                  className="px-10 py-3 bg-[#824dfc] text-white rounded-xl hover:bg-[#7041e6] disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium flex-shrink-0"
                >
                  {countdown > 0 ? `${countdown}s` : '发送'}
                </button>
              </div>
              {errors.verificationCode && (
                <p className="text-red-500 text-sm mt-1">{errors.verificationCode.message}</p>
              )}
            </div>

            {/* 绑定按钮 */}
            <button
              type="submit"
              disabled={!canSubmit}
              className="w-full py-3 bg-[#824dfc] text-white rounded-xl hover:bg-[#7041e6] disabled:bg-gray-300 disabled:cursor-not-allowed transition-all font-medium text-lg"
            >
              绑定
            </button>
          </form>
        </DialogContent>
      </Dialog>

      {/* 邮箱绑定弹窗 */}
      <Dialog open={showEmailModal} onOpenChange={handleEmailDialogChange}>
        <DialogContent className="bg-gradient-to-br from-purple-100 to-blue-100 rounded-2xl p-8 w-96 max-w-md border-0">
          <DialogHeader className="text-center mb-6">
            <DialogTitle className="text-xl font-bold text-gray-800 mb-2">
              绑定邮箱
            </DialogTitle>
          </DialogHeader>

          <form onSubmit={emailHandleSubmit(onEmailSubmit)} className="space-y-4">
            {/* 邮箱输入 */}
            <div className="mb-4">
              <div className="relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                  <img src="/user/verification-code.svg" alt="邮箱" className="w-5 h-5" />
                </div>
                <input
                  type="email"
                  {...emailRegister('email')}
                  placeholder="请输入邮箱"
                  className="w-full pl-10 pr-4 py-3 border border-gray-400 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                />
              </div>
              {emailErrors.email && (
                <p className="text-red-500 text-sm mt-1">{emailErrors.email.message}</p>
              )}
            </div>

            {/* 验证码输入和发送按钮 */}
            <div className="mb-6">
              <div className="flex space-x-3 items-start">
                <div className="flex-1">
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      <img src="/user/verification-code.svg" alt="验证码" className="w-5 h-5" />
                    </div>
                    <input
                      type="text"
                      {...emailRegister('verificationCode')}
                      placeholder="请输入验证码"
                      className="w-full pl-10 pr-4 py-3 border border-gray-400 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                      maxLength={6}
                    />
                  </div>
                </div>
                <button
                  type="button"
                  onClick={handleSendEmailCode}
                  disabled={!canSendEmailCode}
                  className="px-10 py-3 bg-[#824dfc] text-white rounded-xl hover:bg-[#7041e6] disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors font-medium flex-shrink-0"
                >
                  {emailCountdown > 0 ? `${emailCountdown}s` : '发送'}
                </button>
              </div>
              {emailErrors.verificationCode && (
                <p className="text-red-500 text-sm mt-1">{emailErrors.verificationCode.message}</p>
              )}
            </div>

            {/* 绑定按钮 */}
            <button
              type="submit"
              disabled={!canSubmitEmail}
              className="w-full py-3 bg-[#824dfc] text-white rounded-xl hover:bg-[#7041e6] disabled:bg-gray-300 disabled:cursor-not-allowed transition-all font-medium text-lg"
            >
              绑定
            </button>
          </form>
        </DialogContent>
      </Dialog>

      {/* 微信绑定弹窗 */}
      <WechatBindDialog
        isOpen={showWechatModal}
        onClose={() => setShowWechatModal(false)}
        onBindSuccess={async () => {
          // 刷新用户信息
          try {
            await getGuestUserInfo();
          } catch (error) {
          }
        }}
      />
    </div>
  );
}
