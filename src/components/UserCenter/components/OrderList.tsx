'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { UserResponse } from '@/api/client/types/user';
import { PaymentStatus, PaymentMethod, OrderListResponse } from '@/api/client/types/order';
import { orderApi } from '@/api/client';

interface OrderListProps {
  userInfo: UserResponse | null;
  paymentStatusArray?: PaymentStatus[];
}

/**
 * 订单列表组件
 */
export default function OrderList({ userInfo, paymentStatusArray }: OrderListProps) {
  const [currentPage, setCurrentPage] = useState(1);
  const [orderData, setOrderData] = useState<OrderListResponse | null>(null);
  const [loading, setLoading] = useState(false);
  const pageSize = 10;

  // 获取订单列表
  const fetchOrderList = useCallback(async (page: number = 1) => {
    if (!userInfo) return;

    setLoading(true);
    const response = await orderApi.getOrderList({
      page,
      page_size: pageSize,
      payment_status: paymentStatusArray,
    });
    setOrderData(response);
    setLoading(false);
  }, [userInfo, pageSize, paymentStatusArray]);

  // 页面变化时重新获取数据
  useEffect(() => {
    fetchOrderList(currentPage);
  }, [currentPage, fetchOrderList]);

  // 支付状态变化时重置到第一页
  useEffect(() => {
    setCurrentPage(1);
  }, [paymentStatusArray]);

  // 如果没有用户信息，显示空状态
  if (!userInfo) {
    return (
      <div className="text-center py-12 text-gray-500">
        <p>请先登录查看订单信息</p>
      </div>
    );
  }

  const totalItems = orderData?.total || 0;
  const totalPages = Math.ceil(totalItems / pageSize);
  const orders = orderData?.list || [];

  const getStatusColor = (paymentStatus: PaymentStatus) => {
    switch (paymentStatus) {
      case PaymentStatus.Success:
        return 'bg-green-100 text-green-800';
      case PaymentStatus.Pending:
        return 'bg-yellow-100 text-yellow-800';
      case PaymentStatus.Processing:
        return 'bg-blue-100 text-blue-800';
      case PaymentStatus.Failed:
        return 'bg-red-100 text-red-800';
      case PaymentStatus.Timeout:
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPaymentMethodText = (paymentMethod: PaymentMethod) => {
    switch (paymentMethod) {
      case PaymentMethod.WechatPay:
        return '微信支付';
      case PaymentMethod.AliPay:
        return '支付宝';
      default:
        return '未知';
    }
  };

  const getPaymentMethodColor = (paymentMethod: PaymentMethod) => {
    switch (paymentMethod) {
      case PaymentMethod.WechatPay:
        return 'bg-green-100 text-green-800';
      case PaymentMethod.AliPay:
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* 加载状态 */}
      {loading && (
        <div className="text-center py-12 text-gray-500">
          <p>正在加载订单数据...</p>
        </div>
      )}

      {/* 订单表格 */}
      {!loading && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead style={{ backgroundColor: '#f8f9fa' }}>
                <tr>
                  <th className="px-4 py-3 text-left text-base font-medium text-gray-700">
                    订单号
                  </th>
                  <th className="px-4 py-3 text-left text-base font-medium text-gray-700">
                    订单标题
                  </th>
                  <th className="px-4 py-3 text-left text-base font-medium text-gray-700">
                    套餐名称
                  </th>
                  <th className="px-4 py-3 text-left text-base font-medium text-gray-700">
                    支付方式
                  </th>
                  <th className="px-4 py-3 text-left text-base font-medium text-gray-700">
                    订单状态
                  </th>
                  <th className="px-4 py-3 text-left text-base font-medium text-gray-700">
                    支付金额
                  </th>
                  <th className="px-4 py-3 text-left text-base font-medium text-gray-700">
                    创建时间
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-100">
                {orders.map((order) => (
                  <tr key={order.id} className="hover:bg-gray-50 transition-colors">
                    <td className="px-4 py-4 text-base text-gray-900">
                      {order.order_no}
                    </td>
                    <td className="px-4 py-4 text-base text-gray-900">
                      {order.title}
                    </td>
                    <td className="px-4 py-4 text-base text-gray-900">
                      {order.membership_plan.name}
                    </td>
                    <td className="px-4 py-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded text-sm font-medium ${getPaymentMethodColor(order.payment_method)}`}>
                        {getPaymentMethodText(order.payment_method)}
                      </span>
                    </td>
                    <td className="px-4 py-4">
                      <span className={`inline-flex items-center px-2 py-1 rounded text-sm font-medium ${getStatusColor(order.payment_status)}`}>
                        {order.payment_status_str}
                      </span>
                    </td>
                    <td className="px-4 py-4 text-base text-gray-900">
                      ¥{order.amount.toFixed(2)}
                    </td>
                    <td className="px-4 py-4 text-base text-gray-900">
                      {new Date(order.created_at).toLocaleString('zh-CN')}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* 空状态 */}
          {orders.length === 0 && !loading && (
            <div className="text-center py-12 text-gray-500">
              <p>暂无订单记录</p>
            </div>
          )}
        </div>
      )}

      {/* 分页 */}
      {!loading && totalItems > 0 && (
        <div className="flex items-center justify-between text-base text-gray-600">
          <div>
            共 {totalItems} 条，共 {totalPages} 页
          </div>

          <div className="flex items-center space-x-2">
            <button
              onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="px-3 py-1 text-base text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>

            <span className="px-3 py-1 text-base text-gray-900">
              第 {currentPage} 页
            </span>

            <button
              onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
              disabled={currentPage === totalPages || totalPages === 0}
              className="px-3 py-1 text-base text-gray-600 hover:text-gray-800 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
