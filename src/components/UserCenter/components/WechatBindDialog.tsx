'use client';

import React, { useState, useEffect, useRef } from 'react';
import Image from 'next/image';
import { Loader2 } from 'lucide-react';
import { userApi } from '@/api/client';
import { QrCodeLoginResponse, QrCodeStatusResponse } from '@/api/client/types/auth';
import { toast } from 'sonner';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

interface WechatBindDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onBindSuccess?: () => void;
}

/**
 * 微信绑定弹窗组件
 */
export default function WechatBindDialog({ isOpen, onClose, onBindSuccess }: WechatBindDialogProps) {
  // 二维码信息
  const [qrCode, setQrCode] = useState<QrCodeLoginResponse | null>(null);
  // 二维码状态
  const [status, setStatus] = useState<QrCodeStatusResponse | null>(null);
  // 加载状态
  const [loading, setLoading] = useState<boolean>(true);
  // 错误信息
  const [error, setError] = useState<string | null>(null);

  // 使用 ref 来存储 intervalId，避免 ESLint 警告
  const intervalIdRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // 获取二维码
  useEffect(() => {
    if (!isOpen) return;

    const fetchQrCode = async () => {
      setLoading(true);
      setError(null);
      const response = await userApi.getBindWechatQrCode();
      setQrCode(response);
      setLoading(false);
    };

    fetchQrCode();
  }, [isOpen]);

  // 轮询检查二维码状态
  useEffect(() => {
    if (!qrCode?.scene_id || !isOpen) return;

    // 定义检查状态的函数
    const checkStatus = async () => {
      const response = await userApi.checkBindWechatQrCodeStatus(qrCode.scene_id);
      setStatus(response);

      // 如果已扫码，处理绑定成功
      if (response.status === 'SCANNED') {
        // 停止轮询
        if (intervalIdRef.current) {
          clearInterval(intervalIdRef.current);
        }

        // 显示成功提示
        toast.success('微信绑定成功！', {
          position: 'top-center'
        });

        // 调用绑定成功回调
        if (onBindSuccess) {
          onBindSuccess();
        }

        // 关闭弹窗
        onClose();
      }

      // 如果二维码已过期，停止轮询
      if (response.status === 'EXPIRED') {
        if (intervalIdRef.current) {
          clearInterval(intervalIdRef.current);
        }
        setError('二维码已过期，请刷新页面重新获取');
      }
    };

    // 立即检查一次状态
    checkStatus();

    // 开始轮询，每2秒检查一次状态
    intervalIdRef.current = setInterval(checkStatus, 2000);

    // 清理函数
    return () => {
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
      }
    };
  }, [qrCode, isOpen, onBindSuccess, onClose]);

  // 处理弹窗关闭
  const handleDialogChange = (open: boolean) => {
    if (!open) {
      // 清理轮询
      if (intervalIdRef.current) {
        clearInterval(intervalIdRef.current);
      }
      // 重置状态
      setQrCode(null);
      setStatus(null);
      setLoading(true);
      setError(null);
      // 调用关闭回调
      onClose();
    }
  };

  // 渲染加载状态
  if (loading) {
    return (
      <Dialog open={isOpen} onOpenChange={handleDialogChange}>
        <DialogContent className="bg-gradient-to-br from-purple-100 to-blue-100 rounded-2xl p-8 w-96 max-w-md border-0">
          <div className="relative mb-8 p-0 flex justify-center items-center w-full h-[200px]">
            <Loader2 className="w-8 h-8 text-primary animate-spin" />
            <span className="ml-2 text-gray-600">正在获取二维码...</span>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <Dialog open={isOpen} onOpenChange={handleDialogChange}>
        <DialogContent className="bg-gradient-to-br from-purple-100 to-blue-100 rounded-2xl p-8 w-96 max-w-md border-0">
          <div className="relative mb-8 p-0 flex justify-center items-center w-full h-[200px]">
            <p className="text-red-500 text-center">{error}</p>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogChange}>
      <DialogContent className="bg-gradient-to-br from-purple-100 to-blue-100 rounded-2xl p-8 w-96 max-w-md border-0">
        <DialogHeader className="text-center mb-6">
          <DialogTitle className="text-xl font-bold text-gray-800 mb-2">
            绑定微信
          </DialogTitle>
        </DialogHeader>

        {/* 二维码容器 */}
        <div className="relative mb-8 p-0 flex justify-center">
          <div className="relative bg-white p-4 rounded-2xl shadow-lg">
            {/* 二维码 */}
            <div className="bg-white p-2">
              {qrCode ? (
                <>
                  <Image
                    src={qrCode.qr_code_url}
                    alt="微信扫码绑定"
                    width={200}
                    height={200}
                    className="w-[200px] h-[200px]"
                  />
                  {/* 二维码状态提示 */}
                  {status && (
                    <div className="absolute inset-0 flex items-center justify-center">
                      {status.status === 'SCANNED' && (
                        <div className="bg-black bg-opacity-70 text-white p-4 rounded-md w-full h-full flex items-center justify-center">
                          <p className="text-center">扫码成功，正在绑定...</p>
                        </div>
                      )}
                    </div>
                  )}
                </>
              ) : (
                <div className="w-[200px] h-[200px] flex items-center justify-center">
                  <p className="text-gray-500">二维码加载失败</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
