'use client';

import React, { useState } from 'react';
import { UserResponse } from '@/api/client/types/user';
import BasicInfo from './components/BasicInfo';
import OrderInfo from './components/OrderInfo';

interface UserCenterContentProps {
  userInfo: UserResponse | null;
}

/**
 * 用户中心内容组件
 */
export default function UserCenterContent({ userInfo }: UserCenterContentProps) {
  const [activeTab, setActiveTab] = useState<'basic' | 'order'>('basic');

  const tabs = [
    { key: 'basic', label: '基本信息' },
    { key: 'order', label: '订单信息' },
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'basic':
        return <BasicInfo userInfo={userInfo} />;
      case 'order':
        return <OrderInfo userInfo={userInfo} />;
      default:
        return <BasicInfo userInfo={userInfo} />;
    }
  };

  return (
    <div className="w-full h-full px-12 py-6">
      {/* 页面标题 */}
      <div className="flex items-center mb-6">
        <h1 className="text-xl font-medium text-gray-800">个人中心</h1>
      </div>

      {/* 主内容区域 */}
      <div>
        {/* 标签导航 */}
        <div className="flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as 'basic' | 'order')}
              className={`px-6 py-2 text-base font-medium transition-colors rounded-lg cursor-pointer ${
                activeTab === tab.key
                  ? 'text-purple-600 bg-purple-50'
                  : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50'
              }`}
            >
              {tab.label}
            </button>
          ))}
        </div>

        {/* 内容区域 */}
        <div className="pt-6">
          <div className="w-full">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
}
