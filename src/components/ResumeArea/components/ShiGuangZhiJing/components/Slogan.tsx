'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { useModuleStore } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'

const Slogan: React.FC = () => {
  const { slogan, updateSlogan, saveResume } = useModuleStore()
  const { line_spacing, font_size, font_gray } = useResumeStyleStore()

  // 计算字体大小
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = baseFontSize + 4
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editTitle, setEditTitle] = useState('')
  const [editSlogan, setEditSlogan] = useState('')
  const [isClosing, setIsClosing] = useState(false)

  // 如果没有slogan数据，返回null
  if (!slogan) {
    return null
  }

  const { title, slogan: sloganText } = slogan

  // 打开编辑弹窗
  const handleEditClick = () => {
    setEditTitle(title?.value || '个人简历')
    setEditSlogan(sloganText?.value || '在追求中发现可能，在创造中实现价值')
    setIsDialogOpen(true)
  }

  // 保存编辑
  const handleSave = async () => {
    try {
      // 更新store中的slogan数据
      updateSlogan({
        title: { label: '大标题', value: editTitle },
        slogan: { label: 'Slogan', value: editSlogan }
      })

      // 调用保存简历接口
      await saveResume()

      setIsDialogOpen(false)
      toast.success('保存成功', {
        position: 'top-center'
      })
    } catch (error) {
      toast.error('保存失败，请重试', {
        position: 'top-center'
      })
    }
  }



  return (
    <div
      className="flex items-center justify-center relative overflow-hidden cursor-pointer group "
      onClick={() => {
        // 如果弹窗已经打开或正在关闭，不要重复触发
        if (!isDialogOpen && !isClosing) {
          handleEditClick()
        }
      }}
    >

      {/* 内容区域 */}
      <div className="relative z-10 text-center px-6 flex flex-col items-center justify-center">
        {/* 标题 */}
        {title?.value && (
          <h1
            className="font-semibold mb-1 text-gray-800"
            style={{
              fontSize: `${titleFontSize}px`,
              lineHeight: line_spacing
            }}
          >
            {title.value}
          </h1>
        )}

        {/* Slogan文本 */}
        {sloganText?.value && (
          <p
            style={{
              fontSize: font_size,
              lineHeight: line_spacing,
              color: font_gray || '#6b7280' // 使用store的font_gray，如果为空则使用默认的gray-600颜色
            }}
          >
            {sloganText.value}
          </p>
        )}
      </div>

      {/* Hover编辑提示 */}
      <div
        className="absolute inset-0 bg-gray-100 bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center cursor-pointer rounded"
        onClick={(e) => {
          e.stopPropagation()
          if (!isDialogOpen && !isClosing) {
            handleEditClick()
          }
        }}
      >
        <Image
          src="/resume/edit-icon.svg"
          alt="编辑"
          width={24}
          height={24}
          className="opacity-60"
        />
      </div>

      {/* 编辑弹窗 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className="sm:max-w-md p-6"
          onEscapeKeyDown={() => setIsDialogOpen(false)}
          onPointerDownOutside={(e) => {
            e.preventDefault()
            setIsClosing(true)
            setIsDialogOpen(false)
            // 延迟重置关闭状态，防止立即重新打开
            setTimeout(() => {
              setIsClosing(false)
            }, 100)
          }}
        >
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg font-medium">简历大标题与Slogan设置</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* 大标题输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                大标题:
              </label>
              <Input
                value={editTitle}
                onChange={(e) => setEditTitle(e.target.value)}
                placeholder="个人简历"
                className="w-full"
              />
            </div>

            {/* Slogan输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                写一句你的Slogan:
              </label>
              <Input
                value={editSlogan}
                onChange={(e) => setEditSlogan(e.target.value)}
                placeholder="在追求中发现可能，在创造中实现价值"
                className="w-full"
              />
            </div>
          </div>

          <div className="flex justify-center pt-4">
            <button
              onClick={handleSave}
              className="px-24 py-2 text-sm text-white rounded-md transition-colors cursor-pointer"
              style={{ backgroundColor: 'var(--primary)' }}
            >
              确定
            </button>
          </div>
        </DialogContent>
      </Dialog>

    </div>
  )
}

export default Slogan
