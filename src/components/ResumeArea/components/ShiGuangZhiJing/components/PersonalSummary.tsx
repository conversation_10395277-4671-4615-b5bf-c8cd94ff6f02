'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { toast } from 'sonner'
import { useModuleStore, type PersonalSummaryItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import ResumeTitle from '@/components/Title/ResumeTitle'
import ModuleActionButtons from './ModuleActionButtons'
import DeleteConfirmDialog from '@/components/ModuleManager/components/DeleteConfirmDialog'
import { useModuleActions } from '../hooks/useModuleActions'
import MarkdownViewer from '@/components/MarkdownViewer'
import PersonalIcon from '@/assets/svg/personal-icon.svg'

const PersonalSummary: React.FC = () => {
  const { modules, setActiveModule, deleteModule } = useModuleStore()
  const { page_margin, font_size } = useResumeStyleStore()

  // 计算标题字体大小（比基础字体大2px）
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = `${baseFontSize + 2}px`

  // 删除弹窗状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  // 获取个人总结数据
  const personalSummaryModule = modules['personal_summary']
  const personalSummaryItem = useMemo(() =>
    (personalSummaryModule?.item as PersonalSummaryItem) || { summary: { label: '', value: '' } },
    [personalSummaryModule?.item]
  )

  // 处理删除模块弹窗
  const handleShowDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(true)
  }, [])

  // 确认删除模块
  const handleConfirmDelete = useCallback(() => {
    deleteModule('personal_summary')
    setDeleteDialogOpen(false)
    toast.success(`${personalSummaryModule?.name || '个人总结'}模块已删除`)
  }, [deleteModule, personalSummaryModule?.name])

  // 使用模块操作hook
  const { handleMoveUp, handleMoveDown, handleDelete } = useModuleActions({
    moduleId: 'personal_summary',
    moduleName: personalSummaryModule?.name || '个人总结',
    onDelete: handleShowDeleteDialog
  })

  // 处理点击事件，切换到个人总结模块
  const handleClick = () => {
    setActiveModule('personal_summary')
  }

  // 如果没有个人总结数据或不可见，不渲染
  if (!personalSummaryModule?.is_visible || !personalSummaryItem.summary?.value) {
    return null
  }

  return (
    <div
      className="w-full cursor-move transition-all duration-300 hover:bg-purple-50 rounded-lg group/module relative"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
      onClick={handleClick}
    >
      {/* Hover时显示的操作按钮 */}
      <ModuleActionButtons
        onMoveUp={handleMoveUp}
        onMoveDown={handleMoveDown}
        onDelete={handleDelete}
      />
      <div className="space-y-4">
        {/* 模块标题 */}
        <ResumeTitle
          title={personalSummaryModule?.name || '个人总结'}
          fontSize={titleFontSize}
          lineColor="rgb(229, 229, 229)"
          icon={PersonalIcon}
        />

        {/* 个人总结内容 */}
        <div className="leading-relaxed">
          <MarkdownViewer
            content={personalSummaryItem.summary?.value || ''}
            className="leading-relaxed"
          />
        </div>
      </div>

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        moduleName={personalSummaryModule?.name || '个人总结'}
        onConfirm={handleConfirmDelete}
      />
    </div>
  )
}

export default PersonalSummary
