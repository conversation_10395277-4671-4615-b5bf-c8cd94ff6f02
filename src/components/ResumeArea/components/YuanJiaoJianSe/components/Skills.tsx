'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { toast } from 'sonner'
import { useModuleStore, type SkillsItem, type SkillValueItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import ResumeTitle from '@/components/Title/ResumeTitle'
import ModuleActionButtons from './ModuleActionButtons'
import DeleteConfirmDialog from '@/components/ModuleManager/components/DeleteConfirmDialog'
import { useModuleActions } from '../hooks/useModuleActions'
import SkillBar from '@/components/SkillBar'
import CustomIcon from '@/assets/svg/project-icon.svg'

const Skills: React.FC = () => {
  const { modules, setActiveModule, deleteModule } = useModuleStore()
  const { page_margin, font_size, resume_color } = useResumeStyleStore()

  // 计算标题字体大小（比基础字体大2px）
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = `${baseFontSize + 2}px`

  // 删除弹窗状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  // 获取技能数据
  const skillsModule = modules['skills']
  const skillsData = useMemo(() => {
    if (skillsModule?.item && typeof skillsModule.item === 'object' && 'values' in skillsModule.item) {
      return skillsModule.item as SkillsItem
    }
    return null
  }, [skillsModule?.item])

  const skillItems = useMemo(() => {
    if (skillsData?.values?.value) {
      return (skillsData.values.value as SkillValueItem[]).sort((a, b) => a.index - b.index)
    }
    return []
  }, [skillsData])

  // 从模块数据中获取样式配置，如果没有则使用默认值
  const moduleSkillLayout = skillsData?.skillLayout?.value || '1'
  const moduleSkillStyle = skillsData?.skillStyle?.value || '1'



  // 计算每列的最大标题宽度
  const calculateTitleWidths = () => {
    if (!skillItems.length) return []

    const columns = parseInt(moduleSkillLayout) || 1
    const titleWidths: string[] = []

    // 创建一个临时的canvas来测量文字宽度
    const canvas = document.createElement('canvas')
    const context = canvas.getContext('2d')
    if (context) {
      // 设置字体样式，与实际渲染保持一致
      context.font = `500 ${font_size} -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif`

      // 对于每一列，找出该列中所有元素的最大宽度
      for (let col = 0; col < columns; col++) {
        let maxWidth = 0

        // 遍历所有技能项，找出属于当前列的项目
        for (let i = 0; i < skillItems.length; i++) {
          // 在网格布局中，第i个元素属于第 (i % columns) 列
          if (i % columns === col) {
            const item = skillItems[i]
            const textWidth = context.measureText(item.name?.value || '').width
            maxWidth = Math.max(maxWidth, textWidth)
          }
        }

        // 添加1px的padding，让距离非常紧凑
        titleWidths.push(`${Math.ceil(maxWidth + 1)}px`)
      }
    }

    return titleWidths
  }

  const titleWidths = calculateTitleWidths()



  // 根据布局渲染技能项
  const renderSkillItems = () => {
    if (moduleSkillLayout === '1') {
      // 单栏布局
      return (
        <div className="space-y-3">
          {skillItems.map((item) => (
            <SkillBar
              key={item.index}
              item={{
                name: item.name?.value || '',
                score: item.score?.value || 0,
                index: item.index,
                desc: item.desc?.value || ''
              }}
              style={`style${moduleSkillStyle}`}
              fontSize={font_size}
              titleWidth={titleWidths[0]}
            />
          ))}
        </div>
      )
    } else if (moduleSkillLayout === '2') {
      // 双栏布局
      return (
        <div className="grid grid-cols-2 gap-3">
          {skillItems.map((item, index) => {
            const columnIndex = index % 2  // 在网格布局中，第i个元素属于第 (i % columns) 列
            return (
              <SkillBar
                key={item.index}
                item={{
                  name: item.name?.value || '',
                  score: item.score?.value || 0,
                  index: item.index,
                  desc: item.desc?.value || ''
                }}
                style={`style${moduleSkillStyle}`}
                fontSize={font_size}
                titleWidth={titleWidths[columnIndex]}
              />
            )
          })}
        </div>
      )
    } else if (moduleSkillLayout === '3') {
      // 三栏布局
      return (
        <div className="grid grid-cols-3 gap-3">
          {skillItems.map((item, index) => {
            const columnIndex = index % 3  // 在网格布局中，第i个元素属于第 (i % columns) 列
            return (
              <SkillBar
                key={item.index}
                item={{
                  name: item.name?.value || '',
                  score: item.score?.value || 0,
                  index: item.index,
                  desc: item.desc?.value || ''
                }}
                style={`style${moduleSkillStyle}`}
                fontSize={font_size}
                titleWidth={titleWidths[columnIndex]}
              />
            )
          })}
        </div>
      )
    } else {
      // 默认单栏布局
      return (
        <div className="space-y-3">
          {skillItems.map((item) => (
            <SkillBar
              key={item.index}
              item={{
                name: item.name?.value || '',
                score: item.score?.value || 0,
                index: item.index,
                desc: item.desc?.value || ''
              }}
              style={`style${moduleSkillStyle}`}
              fontSize={font_size}
              titleWidth={titleWidths[0]}
            />
          ))}
        </div>
      )
    }
  }

  // 处理删除模块弹窗
  const handleShowDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(true)
  }, [])

  // 确认删除模块
  const handleConfirmDelete = useCallback(() => {
    deleteModule('skills')
    setDeleteDialogOpen(false)
    toast.success(`${skillsModule?.name || '技能专长'}模块已删除`)
  }, [deleteModule, skillsModule?.name])

  // 使用模块操作hook
  const { handleMoveUp, handleMoveDown, handleDelete } = useModuleActions({
    moduleId: 'skills',
    moduleName: skillsModule?.name || '技能专长',
    onDelete: handleShowDeleteDialog
  })

  // 处理点击事件，切换到技能模块
  const handleClick = () => {
    setActiveModule('skills')
  }

  // 如果没有技能数据或不可见，不渲染
  if (!skillsModule?.is_visible || !skillItems.length) {
    return null
  }

  return (
    <div
      className="w-full cursor-move transition-all duration-300 hover:bg-purple-50 rounded-lg group/module relative"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
      onClick={handleClick}
    >
      {/* Hover时显示的操作按钮 */}
      <ModuleActionButtons
        onMoveUp={handleMoveUp}
        onMoveDown={handleMoveDown}
        onDelete={handleDelete}
      />
      <div className="space-y-4">
        {/* 模块标题 */}
        <ResumeTitle
          title={skillsModule?.name || '技能专长'}
          fontSize={titleFontSize}
          lineColor="black"
          icon={CustomIcon}
        />

        {/* 技能内容 */}
        {renderSkillItems()}
      </div>

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        moduleName={skillsModule?.name || '技能专长'}
        onConfirm={handleConfirmDelete}
      />
    </div>
  )
}

export default Skills
