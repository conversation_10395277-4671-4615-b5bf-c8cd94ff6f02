'use client'

import React, { useState, useMemo } from 'react'
import { useModuleStore, type PortfolioItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import QRCode from 'react-qr-code'
import { ReactSortable } from 'react-sortablejs'
import { toast } from 'sonner'
import ResumeTitle from '@/components/Title/ResumeTitle'
import ModuleActionButtons from './ModuleActionButtons'
import DeleteConfirmDialog from '@/components/ModuleManager/components/DeleteConfirmDialog'
import { useModuleActions } from '../hooks/useModuleActions'
import PortfolioIcon from '@/assets/svg/portfolio-icon.svg'

const Portfolio: React.FC = () => {
  const { modules, setActiveModule, updateModuleItem, deleteModule } = useModuleStore()
  const { page_margin, font_size, resume_color2 } = useResumeStyleStore()

  // 删除弹窗状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  // 计算标题字体大小（比基础字体大2px）
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = `${baseFontSize + 2}px`

  // 获取作品集数据
  const portfolioModule = modules['portfolio']
  const portfolioItems = useMemo(() =>
    (portfolioModule?.item as PortfolioItem[]) || [],
    [portfolioModule?.item]
  )

  // 使用模块操作hook
  const { handleMoveUp, handleMoveDown } = useModuleActions({
    moduleId: 'portfolio',
    moduleName: '作品集',
    onDelete: () => setDeleteDialogOpen(true)
  })

  // 确认删除模块
  const confirmDelete = () => {
    deleteModule('portfolio')
    setDeleteDialogOpen(false)
    toast.success('作品集模块删除成功')
  }

  // 处理删除模块
  const handleDelete = (e: React.MouseEvent) => {
    e.stopPropagation()
    setDeleteDialogOpen(true)
  }

  // 如果没有数据，不渲染组件
  if (!portfolioModule?.is_visible || portfolioItems.length === 0) {
    return null
  }

  // 转换为可排序的数据格式
  const sortableItems = portfolioItems.map(item => ({
    id: item.id,
    name: item.name?.value || '',
    url: item.url?.value || '',
    index: item.index
  }))

  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setActiveModule('portfolio')
  }

  // 处理排序
  const handleSort = (newList: typeof sortableItems) => {
    const updatedItems = newList.map((item, index) => ({
      id: item.id,
      name: { label: "作品名称", value: item.name },
      url: { label: "作品链接", value: item.url },
      index
    }))
    updateModuleItem('portfolio', updatedItems)
  }

  return (
    <>
      <div
        className="w-full cursor-move transition-all duration-300 hover:bg-purple-50 rounded-lg group/module relative"
        style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
        onClick={handleClick}
      >
        {/* Hover时显示的操作按钮 */}
        <ModuleActionButtons
          onMoveUp={handleMoveUp}
          onMoveDown={handleMoveDown}
          onDelete={handleDelete}
        />
        <div className="space-y-4">
          {/* 模块标题 */}
          <ResumeTitle
            title="作品集"
            fontSize={titleFontSize}
            lineColor={resume_color2}
            icon={PortfolioIcon}
          />

          {/* 作品集二维码列表 - 一行显示5个 */}
          <ReactSortable
            list={sortableItems}
            setList={handleSort}
            animation={200}
            delay={2}
            ghostClass="opacity-50"
            chosenClass="shadow-lg"
            dragClass="rotate-1"
            group="portfolio"
            className="grid grid-cols-5 gap-4"
          >
            {sortableItems.map((item) => (
              <div key={item.id} className="flex flex-col items-center cursor-move">
                {/* 二维码 - 直接显示 */}
                <QRCode
                  value={item.url || 'https://example.com'}
                  size={72}
                  style={{ height: "72px", maxWidth: "72px", width: "72px" }}
                  viewBox={`0 0 256 256`}
                />
                {/* 作品名称 - 链接样式 */}
                <a
                  href={item.url || 'https://example.com'}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-xs text-center mt-2 font-semibold truncate w-[72px] text-blue-600 hover:text-blue-800 hover:underline cursor-pointer transition-colors duration-200"
                  title={item.name}
                  onClick={(e) => e.stopPropagation()}
                >
                  {item.name || '未命名作品'}
                </a>
              </div>
            ))}
          </ReactSortable>
        </div>
      </div>

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        moduleName="作品集"
        onConfirm={confirmDelete}
      />
    </>
  )
}

export default Portfolio
