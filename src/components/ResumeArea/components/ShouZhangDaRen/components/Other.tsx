'use client'

import React, { useState, useCallback, useMemo } from 'react'

import { toast } from 'sonner'
import { useModuleStore, type OtherItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import ResumeTitle from '@/components/Title/ResumeTitle'
import ModuleActionButtons from './ModuleActionButtons'
import DeleteConfirmDialog from '@/components/ModuleManager/components/DeleteConfirmDialog'
import { useModuleActions } from '../hooks/useModuleActions'
import MarkdownViewer from '@/components/MarkdownViewer'
import OtherIcon from '@/assets/svg/honors-icon.svg'


const Other: React.FC = () => {
  const { modules, setActiveModule, setActiveIndex, deleteModule } = useModuleStore()
  const { page_margin, font_size } = useResumeStyleStore()

  // 计算标题字体大小（比基础字体大2px）
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = `${baseFontSize + 2}px`

  // 删除弹窗状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)



  // 获取其他信息数据
  const otherModule = modules['other']
  const otherItems = useMemo(() =>
    (otherModule?.item as OtherItem[]) || [],
    [otherModule?.item]
  )

  // 排序后的其他信息列表
  const sortableItems = useMemo(() =>
    [...otherItems].sort((a, b) => a.index - b.index),
    [otherItems]
  )

  // 处理删除模块弹窗
  const handleShowDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(true)
  }, [])

  // 确认删除模块
  const handleConfirmDelete = useCallback(() => {
    deleteModule('other')
    setDeleteDialogOpen(false)
    toast.success(`${otherModule?.name || '其他'}模块已删除`)
  }, [deleteModule, otherModule?.name])

  // 使用模块操作hook
  const { handleMoveUp, handleMoveDown, handleDelete } = useModuleActions({
    moduleId: 'other',
    moduleName: otherModule?.name || '其他',
    onDelete: handleShowDeleteDialog
  })

  // 处理点击事件，切换到其他模块
  const handleClick = () => {
    setActiveModule('other')
  }

  // 处理其他信息项点击事件，设置对应的 activeIndex
  const handleItemClick = useCallback((itemIndex: number) => (e: React.MouseEvent) => {
    e.stopPropagation() // 阻止事件冒泡到模块点击事件
    setActiveModule('other') // 确保切换到其他模块
    setActiveIndex([itemIndex]) // 设置对应的 activeIndex
  }, [setActiveModule, setActiveIndex])



  // 如果没有其他信息数据，不渲染
  if (!sortableItems.length) {
    return null
  }

  return (
    <div
      className="w-full cursor-move transition-all duration-300 hover:bg-purple-50 rounded-lg group/module relative"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
      onClick={handleClick}
    >
      {/* Hover时显示的操作按钮 */}
      <ModuleActionButtons
        onMoveUp={handleMoveUp}
        onMoveDown={handleMoveDown}
        onDelete={handleDelete}
      />
      <div className="space-y-4">
        {/* 模块标题 */}
        <ResumeTitle
          title={otherModule?.name || '其他'}
          fontSize={titleFontSize}
          lineColor="rgb(229, 229, 229)"
          icon={OtherIcon}
        />

        {/* 其他信息列表 */}
        <div>
          {sortableItems.map((item, index) => (
            <div
              key={item.id}
              className="flex items-start cursor-pointer hover:bg-purple-50 rounded-lg p-2 transition-all duration-300"
              onClick={handleItemClick(index)}
            >
              {/* 标记符号 */}
              <div className="flex-shrink-0 pr-3 flex items-center" style={{ height: '1.5rem' }}>
                <span className="inline-block w-1 h-1 bg-black rounded-full"></span>
              </div>

              {/* 内容区域 */}
              <div className="flex-1 flex items-baseline">
                {/* 项目名称（如：技能、证书、语言） */}
                <div className="font-semibold flex-shrink-0 mr-2">
                  {item.name?.value || ''}:
                </div>

                {/* 项目描述 */}
                <div className="flex-1 leading-relaxed">
                  <MarkdownViewer
                    content={item.desc?.value || ''}
                    className="leading-relaxed"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        moduleName={otherModule?.name || '其他'}
        onConfirm={handleConfirmDelete}
      />


    </div>
  )
}

export default Other
