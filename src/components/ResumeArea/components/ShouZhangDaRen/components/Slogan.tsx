'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { useModuleStore } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'

const Slogan: React.FC = () => {
  const { slogan, updateSlogan, saveResume } = useModuleStore()
  const { line_spacing, page_margin } = useResumeStyleStore()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editSlogan, setEditSlogan] = useState('')
  const [isClosing, setIsClosing] = useState(false)

  // 如果没有slogan数据，返回null
  if (!slogan) {
    return null
  }

  const { slogan: sloganText } = slogan

  // 打开编辑弹窗
  const handleEditClick = () => {
    setEditSlogan(sloganText?.value || '在追求中发现可能，在创造中实现价值')
    setIsDialogOpen(true)
  }

  // 保存编辑
  const handleSave = async () => {
    try {
      // 更新store中的slogan数据
      updateSlogan({
        title: { label: '大标题', value: '' }, // 传入空值，不显示大标题
        slogan: { label: 'Slogan', value: editSlogan }
      })

      // 调用保存简历接口
      await saveResume()

      setIsDialogOpen(false)
      toast.success('保存成功', {
        position: 'top-center'
      })
    } catch (error) {
      toast.error('保存失败，请重试', {
        position: 'top-center'
      })
    }
  }



  return (
    <div
      className="w-full flex flex-col justify-center items-start gap-2 cursor-pointer transition-all duration-300 hover:bg-purple-50 rounded-lg group relative"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
      onClick={() => {
        // 如果弹窗已经打开或正在关闭，不要重复触发
        if (!isDialogOpen && !isClosing) {
          handleEditClick()
        }
      }}
    >
      {/* 主要内容区域 */}
      <div className="flex flex-col gap-[13px]">
        {/* 气泡字体装饰图片 */}
        <div className="w-[303px] h-[36px]">
          <Image
            src="/resume/bubble-font.png"
            alt="装饰"
            width={303}
            height={36}
            className="w-full h-full object-contain"
          />
        </div>

        {/* Slogan文本 */}
        {sloganText?.value && (
          <div
            className="text-left text-black"
            style={{
              lineHeight: line_spacing,
              fontSize: '14px',
              borderLeft: '4px solid #bfd4f0',
              paddingLeft: '9px'
            }}
          >
            {sloganText.value}
          </div>
        )}
      </div>

      {/* Hover时显示的编辑按钮 */}
      <div className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10 flex items-center justify-center">
        <button
          onClick={(e) => {
            e.stopPropagation()
            if (!isDialogOpen && !isClosing) {
              handleEditClick()
            }
          }}
          className="cursor-pointer"
          title="编辑Slogan"
        >
          <Image
            src="/resume/edit-icon.svg"
            alt="编辑"
            width={24}
            height={24}
            className="filter brightness-0"
          />
        </button>
      </div>



      {/* 编辑弹窗 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className="sm:max-w-md p-6"
          onEscapeKeyDown={() => setIsDialogOpen(false)}
          onPointerDownOutside={(e) => {
            e.preventDefault()
            setIsClosing(true)
            setIsDialogOpen(false)
            // 延迟重置关闭状态，防止立即重新打开
            setTimeout(() => {
              setIsClosing(false)
            }, 100)
          }}
        >
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg font-medium">Slogan设置</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Slogan输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                写一句你的Slogan:
              </label>
              <Input
                value={editSlogan}
                onChange={(e) => setEditSlogan(e.target.value)}
                placeholder="在追求中发现可能，在创造中实现价值"
                className="w-full"
              />
            </div>
          </div>

          <div className="flex justify-center pt-4">
            <button
              onClick={handleSave}
              className="px-24 py-2 text-sm text-white rounded-md transition-colors cursor-pointer"
              style={{ backgroundColor: 'var(--primary)' }}
            >
              确定
            </button>
          </div>
        </DialogContent>
      </Dialog>

    </div>
  )
}

export default Slogan
