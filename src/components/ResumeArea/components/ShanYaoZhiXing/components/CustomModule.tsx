'use client'

import React, { useMemo } from 'react'
import { useModuleStore, type CustomItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import ResumeTitle from '@/components/Title/ResumeTitle'
import MarkdownViewer from '@/components/MarkdownViewer'
import ProjectIcon from '@/assets/svg/project-icon.svg'

interface CustomModuleProps {
  moduleId: string
}

const CustomModule: React.FC<CustomModuleProps> = ({ moduleId }) => {
  const { customModules, setActiveModule, setActiveIndex } = useModuleStore()
  const { page_margin, font_size, date_format, separator, date_align, title_align } = useResumeStyleStore()

  // 计算标题字体大小（比基础字体大2px）
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = `${baseFontSize + 2}px`

  // 获取自定义模块数据
  const customModule = useMemo(() => {
    return customModules.find(module => module.id === moduleId)
  }, [customModules, moduleId])

  const customItems = useMemo(() => {
    return customModule?.items || []
  }, [customModule?.items])

  // 排序后的自定义模块项列表
  const sortableItems = useMemo(() =>
    [...customItems].sort((a, b) => a.index - b.index),
    [customItems]
  )

  // 处理自定义模块项点击事件，设置对应的 activeIndex
  const handleItemClick = (itemIndex: number) => (e: React.MouseEvent) => {
    e.stopPropagation() // 阻止事件冒泡到模块点击事件
    setActiveModule(moduleId) // 确保切换到自定义模块
    setActiveIndex([itemIndex]) // 设置对应的 activeIndex
  }

  // 如果没有自定义模块数据，不渲染
  if (!customModule || !sortableItems.length) {
    return null
  }

  // 格式化时间显示
  const formatTimeRange = (startDate: string, endDate: string) => {
    if (!startDate && !endDate) return ''

    // 格式化单个日期
    const formatSingleDate = (date: string) => {
      if (!date || date === '至今') return date

      // 解析日期格式 YYYY-MM
      const parts = date.split('-')
      if (parts.length !== 2) return date

      const year = parts[0]
      const month = parts[1]

      // 根据date_format配置格式化
      if (date_format === 'YYYY年MM月') {
        return `${year}年${month}月`
      } else {
        // 默认格式 'YYYY.MM'
        return `${year}.${month}`
      }
    }

    const formattedStart = formatSingleDate(startDate)
    const formattedEnd = formatSingleDate(endDate)

    if (!startDate) return formattedEnd
    if (!endDate) return `${formattedStart}${separator}`
    return `${formattedStart}${separator}${formattedEnd}`
  }

  return (
    <div
      className="w-full"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
    >
      <div className="space-y-4">
        {/* 模块标题 */}
        <ResumeTitle
          title={customModule.name}
          fontSize={titleFontSize}
          lineColor="black"
          icon={ProjectIcon}
          showDecoration={false}
        />

        {/* 自定义模块项列表 */}
        <div className="space-y-4">
          {sortableItems.map((item, index) => (
            <div
              key={item.id}
              className="space-y-2 cursor-pointer hover:bg-purple-50 rounded-lg p-2 transition-all duration-300"
              onClick={handleItemClick(index)}
            >
              {/* 根据title_align配置调整布局 */}
              {title_align === 'justify' ? (
                // 均匀分布：使用flex布局，模拟el-row el-col结构
                <>
                  {/* 第一行：根据date_align调整列的顺序 */}
                  <div className="flex gap-1 font-semibold">
                    {date_align === 'left' ? (
                      <>
                        {/* 日期在左 (6/24 = 25%) */}
                        <div className="flex-none text-left" style={{ width: '25%' }}>
                          <span>{formatTimeRange(item.start_month?.value || '', item.end_month?.value || '')}</span>
                        </div>
                        {/* 项目名称 (8/24 = 33.33%) */}
                        <div className="flex-none" style={{ width: '33.33%' }}>
                          <span>{item.name?.value || ''}</span>
                        </div>
                        {/* 角色 (10/24 = 41.67%) */}
                        <div className="flex-none text-left" style={{ width: '41.67%' }}>
                          <span>{item.role?.value || ''}</span>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* 项目名称 (8/24 = 33.33%) */}
                        <div className="flex-none" style={{ width: '33.33%' }}>
                          <span>{item.name?.value || ''}</span>
                        </div>
                        {/* 角色 (10/24 = 41.67%) */}
                        <div className="flex-none text-left" style={{ width: '41.67%' }}>
                          <span>{item.role?.value || ''}</span>
                        </div>
                        {/* 日期在右 (6/24 = 25%) */}
                        <div className="flex-none text-right" style={{ width: '25%' }}>
                          <span>{formatTimeRange(item.start_month?.value || '', item.end_month?.value || '')}</span>
                        </div>
                      </>
                    )}
                  </div>
                </>
              ) : (
                // 自适应布局：原有的flex布局
                <>
                  {/* 第一行：项目名称+角色 vs 日期 */}
                  <div className="flex justify-between items-start">
                    {date_align === 'left' ? (
                      <>
                        {/* 日期在左 */}
                        <div className="mr-4 flex-shrink-0 font-semibold text-left">
                          {formatTimeRange(item.start_month?.value || '', item.end_month?.value || '')}
                        </div>
                        {/* 项目信息在右 */}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 justify-end">
                            <span className="font-semibold">
                              {item.name?.value || ''} - {item.role?.value || ''}
                            </span>
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* 项目信息在左 */}
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">
                              {item.name?.value || ''} - {item.role?.value || ''}
                            </span>
                          </div>
                        </div>
                        {/* 日期在右 */}
                        <div className="ml-4 flex-shrink-0 font-semibold text-right">
                          {formatTimeRange(item.start_month?.value || '', item.end_month?.value || '')}
                        </div>
                      </>
                    )}
                  </div>
                </>
              )}

              {/* 第二行：描述信息 */}
              <MarkdownViewer
                content={item.desc?.value || ''}
                className="leading-relaxed"
              />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default CustomModule
