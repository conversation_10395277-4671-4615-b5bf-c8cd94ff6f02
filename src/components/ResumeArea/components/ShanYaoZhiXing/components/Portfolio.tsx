'use client'

import React, { useMemo } from 'react'
import { useModuleStore, type PortfolioItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import QRCode from 'react-qr-code'
import ResumeTitle from '@/components/Title/ResumeTitle'
import PortfolioIcon from '@/assets/svg/portfolio-icon.svg'

const Portfolio: React.FC = () => {
  const { modules } = useModuleStore()
  const { page_margin, font_size } = useResumeStyleStore()

  // 计算标题字体大小（比基础字体大2px）
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = `${baseFontSize + 2}px`

  // 获取作品集数据
  const portfolioModule = modules['portfolio']
  const portfolioItems = useMemo(() =>
    (portfolioModule?.item as PortfolioItem[]) || [],
    [portfolioModule?.item]
  )

  // 如果没有数据，不渲染组件
  if (!portfolioModule?.is_visible || portfolioItems.length === 0) {
    return null
  }

  // 转换为可排序的数据格式
  const sortableItems = portfolioItems.map(item => ({
    id: item.id,
    name: item.name?.value || '',
    url: item.url?.value || '',
    index: item.index
  }))

  return (
    <div
      className="w-full"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
    >
      <div className="space-y-4">
        {/* 模块标题 */}
        <ResumeTitle
          title="作品集"
          fontSize={titleFontSize}
          lineColor="black"
          icon={PortfolioIcon}
          showDecoration={false}
        />

        {/* 作品集二维码列表 - 一行显示5个 */}
        <div className="grid grid-cols-5 gap-4">
          {sortableItems.map((item) => (
            <div key={item.id} className="flex flex-col items-center">
              {/* 二维码 - 直接显示 */}
              <QRCode
                value={item.url || 'https://example.com'}
                size={72}
                style={{ height: "72px", maxWidth: "72px", width: "72px" }}
                viewBox={`0 0 256 256`}
              />
              {/* 作品名称 - 链接样式 */}
              <a
                href={item.url || 'https://example.com'}
                target="_blank"
                rel="noopener noreferrer"
                className="text-xs text-center mt-2 font-semibold truncate w-[72px] text-blue-600 hover:text-blue-800 hover:underline cursor-pointer transition-colors duration-200"
                title={item.name}
              >
                {item.name || '未命名作品'}
              </a>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default Portfolio
