'use client'

import React from 'react'
import Image from 'next/image'
import { useModuleStore } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'



interface CustomField {
  id: string
  label: string
  value: string
}

const BasicInfo: React.FC = () => {
  const { modules, setActiveModule } = useModuleStore()
  const { font_size, base_info, header_layout, avatar_layout, page_margin, resume_color } = useResumeStyleStore()



  // 获取基础信息数据
  const basicInfoModule = modules['basic_info']
  const basicInfo = basicInfoModule?.item as Record<string, { label: string; value: string }>

  // 计算字体大小
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = baseFontSize + 8

  // 计算图标大小，与字体大小保持一致
  const iconSize = baseFontSize

  // 如果没有基础信息数据，返回null
  if (!basicInfo) {
    return null
  }

  // 处理点击事件，切换到基本信息模块
  const handleClick = () => {
    setActiveModule('basic_info')
  }

  // 基础信息项配置
  const basicInfoItems = [
    { key: 'phone', icon: 'phone-icon.svg', label: '电话', value: basicInfo.phone?.value },
    { key: 'email', icon: 'email-icon.svg', label: '邮箱', value: basicInfo.email?.value },
    { key: 'city', icon: 'home-icon.svg', label: '城市', value: basicInfo.city?.value },
    { key: 'birth', icon: 'birthday-icon.svg', label: '生日', value: basicInfo.birth?.value },
    { key: 'gender', icon: 'smile-icon.svg', label: '性别', value: basicInfo.gender?.value },
    { key: 'height', icon: 'height-icon.svg', label: '身高', value: basicInfo.height?.value },
    { key: 'weight', icon: 'person-icon.svg', label: '体重', value: basicInfo.weight?.value },
    { key: 'job', icon: 'send-icon.svg', label: '职位', value: basicInfo.job?.value },
    { key: 'job_status', icon: 'job-status-icon.svg', label: '求职状态', value: basicInfo.job_status?.value },
    { key: 'intended_city', icon: 'city-icon.svg', label: '意向城市', value: basicInfo.intended_city?.value },
    { key: 'max_salary', icon: 'add-icon.svg', label: '期望薪资', value: basicInfo.max_salary?.value },
    { key: 'origin', icon: 'home-icon.svg', label: '籍贯', value: basicInfo.origin?.value },
    { key: 'ethnicity', icon: 'ethnicity-icon.svg', label: '民族', value: basicInfo.ethnicity?.value },
    { key: 'political_affiliation', icon: 'qr-code-icon.svg', label: '政治面貌', value: basicInfo.political_affiliation?.value },
    { key: 'marital', icon: 'heart-icon.svg', label: '婚姻状况', value: basicInfo.marital?.value },
    { key: 'wechat', icon: 'wechat-icon.svg', label: '微信', value: basicInfo.wechat?.value },
    { key: 'github', icon: 'github-icon.svg', label: 'GitHub', value: basicInfo.github?.value },
    { key: 'gitee', icon: 'globe-icon.svg', label: 'Gitee', value: basicInfo.gitee?.value },
    { key: 'site', icon: 'globe-icon.svg', label: '个人网站', value: basicInfo.site?.value }
  ]

  // 自定义字段
  const customFieldsValue = basicInfo.customize_fields?.value
  const customFields: CustomField[] = Array.isArray(customFieldsValue) ? customFieldsValue : []

  // 过滤掉空值的基础信息项目
  const visibleBasicItems = basicInfoItems.filter(item => item.value && item.value.trim() !== '')

  // 过滤掉空值的自定义字段
  const visibleCustomFields = customFields.filter((field: CustomField) => field.value && field.value.trim() !== '')

  // 合并所有可见项目
  const allVisibleItems = [
    ...visibleBasicItems,
    ...visibleCustomFields.map((field: CustomField) => ({
      key: `custom_${field.id}`,
      icon: 'star-icon.svg', // 自定义字段使用星形图标
      label: field.label || '自定义',
      value: field.value
    }))
  ]

  // 根据avatar_layout决定布局方向和顺序
  const isVerticalLayout = avatar_layout === 'center'
  const isAvatarRight = avatar_layout === 'right'

  // 根据header_layout决定信息对齐方式
  const infoJustifyClass = header_layout === 'left' ? 'justify-start' :
                          header_layout === 'right' ? 'justify-end' : 'justify-center'

  // 根据header_layout决定文本对齐方式
  const textAlignClass = header_layout === 'left' ? 'text-left' :
                        header_layout === 'right' ? 'text-right' : 'text-center'

  // 基础信息区域的完整样式类
  const infoAreaClass = `flex flex-wrap ${infoJustifyClass} ${textAlignClass}`

  // 计算最大宽度
  const maxWidth = `calc(130mm - ${page_margin})`

  // 为整个信息区域添加对齐样式
  const infoContainerClass = header_layout === 'right' ? 'flex flex-col items-end' :
                            header_layout === 'center' ? 'flex flex-col items-center' :
                            'flex flex-col items-start'

  // 头像组件
  const AvatarComponent = basicInfo.avatar?.value ? (
    <div
      className="flex-shrink-0"
      style={{
        width: '105px',
        height: '105px',
        border: '2px solid #fff',
        borderRadius: '50%',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        overflow: 'hidden'
      }}
    >
      <Image
        src={basicInfo.avatar.value}
        alt="头像"
        width={105}
        height={105}
        className="object-cover"
        style={{
          width: '100%',
          height: '100%',
          objectPosition: 'center top'
        }}
      />
    </div>
  ) : null

  // 信息组件
  const InfoComponent = (
    <div className={`${isVerticalLayout ? 'w-full' : 'flex-1'} ${infoContainerClass}`}>
      {/* 姓名标题 */}
      {basicInfo.name?.value && (
        <div className="mb-4">
          <h2
            className="font-semibold text-white"
            style={{
              fontSize: `${titleFontSize}px`
            }}
          >
            {basicInfo.name.value}
          </h2>
        </div>
      )}

      {/* 基础信息区域 */}
      <div
        className={infoAreaClass}
        style={{
          gap: '5px 12px',
          maxWidth: maxWidth
        }}
      >
        {allVisibleItems.map((item, index) => (
          <div key={index} className="flex items-center space-x-2">
            {/* 根据base_info配置决定显示图标还是文本标签 */}
            {base_info === 'icon' && (
              <Image
                src={`/resume/basic/${item.icon}`}
                alt={item.key}
                width={iconSize}
                height={iconSize}
                className="opacity-80 brightness-0 invert"
                style={{ filter: 'brightness(0) invert(1)' }}
              />
            )}
            {base_info === 'text' && (
              <span
                className="text-nowrap text-white"
              >
                {item.label}:
              </span>
            )}
            <span
              className="truncate text-white"
            >
              {item.value}
            </span>
          </div>
        ))}
      </div>
    </div>
  )

  return (
    <div
      className="w-full py-[30px] cursor-move transition-all duration-300 hover:bg-purple-50  group/module relative"
      style={{
        paddingLeft: `calc(${page_margin} + 12px)`,
        paddingRight: `calc(${page_margin} + 12px)`,
        backgroundColor: resume_color,
      }}
      onClick={handleClick}
    >

      {isVerticalLayout ? (
        // 垂直布局（居中）
        <div className="flex flex-col items-center space-y-6">
          {AvatarComponent}
          {InfoComponent}
        </div>
      ) : (
        // 水平布局
        <div className="flex items-start space-x-6">
          {isAvatarRight ? (
            // 头像在右侧：先显示信息，再显示头像
            <>
              {InfoComponent}
              {AvatarComponent}
            </>
          ) : (
            // 头像在左侧：先显示头像，再显示信息
            <>
              {AvatarComponent}
              {InfoComponent}
            </>
          )}
        </div>
      )}

      {/* 底部装饰块 */}
      <div
        className="absolute left-0 bottom-0 w-full h-[20px]"
        style={{
          backgroundColor: resume_color,
          transform: 'translateY(19px)',
          clipPath: 'polygon(35% 0, 100% 0, 100% 100%, 45% 100%)'
        }}
      />
    </div>
  )
}

export default BasicInfo
