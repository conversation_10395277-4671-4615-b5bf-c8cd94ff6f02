'use client';

import React, { useEffect, useRef } from 'react';

interface NoCopyWrapperProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  allowDrag?: boolean; // 是否允许拖拽功能
}

/**
 * 防复制包装组件
 * 禁用文本选择、复制、右键菜单等功能
 */
export default function NoCopyWrapper({ children, className, style, allowDrag = true }: NoCopyWrapperProps) {
  const wrapperRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const wrapper = wrapperRef.current;
    if (!wrapper) return;

    // 禁用右键菜单
    const handleContextMenu = (e: MouseEvent) => {
      e.preventDefault();
      return false;
    };

    // 禁用选择文本
    const handleSelectStart = (e: Event) => {
      e.preventDefault();
      return false;
    };

    // 禁用拖拽（但根据 allowDrag 参数决定是否允许排序库的拖拽功能）
    const handleDragStart = (e: DragEvent) => {
      // 如果不允许拖拽，直接阻止所有拖拽
      if (!allowDrag) {
        e.preventDefault();
        return false;
      }

      const target = e.target as HTMLElement;

      // 如果允许拖拽，检查是否是排序库的拖拽元素
      // 1. ReactSortable (SortableJS) - 检查是否在 ReactSortable 容器内或具有相关类名
      // 2. DndKit - 检查是否有拖拽手柄的 cursor-grab 类
      // 3. 其他拖拽库的标识
      if (
        // ReactSortable / SortableJS 相关检查
        target.closest('.sortable') || // SortableJS 默认容器类
        target.closest('[data-sortable]') || // 自定义 sortable 标识
        target.classList.contains('cursor-move') || // ReactSortable 常用的拖拽样式
        target.closest('.cursor-move') || // 父元素有 cursor-move

        // DndKit 相关检查
        target.closest('.cursor-grab') || // DndKit 拖拽手柄
        target.closest('.cursor-grabbing') || // DndKit 拖拽中状态
        target.classList.contains('cursor-grab') || // 直接在拖拽手柄上
        target.classList.contains('cursor-grabbing') || // 拖拽中状态

        // 通用拖拽标识
        target.closest('[draggable="true"]') || // 明确标记为可拖拽的元素
        target.getAttribute('draggable') === 'true' || // 元素本身可拖拽

        // 其他拖拽库标识
        target.closest('[data-rbd-draggable-id]') || // react-beautiful-dnd
        target.closest('[data-sortable-id]') || // dnd-kit sortable

        // 检查是否在 ReactSortable 组件内（通过检查父元素的 className）
        target.closest('.flex.flex-col') || // ReactSortable 常用的容器样式
        target.closest('.space-y-4') || // ReactSortable 项目间距样式

        // 检查是否有 SortableJS 添加的动态类名
        target.classList.contains('sortable-chosen') ||
        target.classList.contains('sortable-ghost') ||
        target.classList.contains('sortable-drag')
      ) {
        // 允许排序拖拽
        return true;
      }

      // 阻止其他拖拽行为（如图片拖拽、文本拖拽等）
      e.preventDefault();
      return false;
    };

    // 禁用复制快捷键
    const handleKeyDown = (e: KeyboardEvent) => {
      // 禁用 Ctrl+C, Ctrl+A, Ctrl+S, Ctrl+P, F12 等
      if (
        (e.ctrlKey || e.metaKey) && 
        (e.key === 'c' || e.key === 'a' || e.key === 's' || e.key === 'p' || e.key === 'v')
      ) {
        e.preventDefault();
        return false;
      }
      
      // 禁用 F12 开发者工具
      if (e.key === 'F12') {
        e.preventDefault();
        return false;
      }

      // 禁用 Ctrl+Shift+I 开发者工具
      if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'I') {
        e.preventDefault();
        return false;
      }

      // 禁用 Ctrl+U 查看源代码
      if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
        e.preventDefault();
        return false;
      }
    };

    // 添加事件监听器
    wrapper.addEventListener('contextmenu', handleContextMenu);
    wrapper.addEventListener('selectstart', handleSelectStart);
    wrapper.addEventListener('dragstart', handleDragStart);
    wrapper.addEventListener('keydown', handleKeyDown);

    // 清理函数
    return () => {
      wrapper.removeEventListener('contextmenu', handleContextMenu);
      wrapper.removeEventListener('selectstart', handleSelectStart);
      wrapper.removeEventListener('dragstart', handleDragStart);
      wrapper.removeEventListener('keydown', handleKeyDown);
    };
  }, [allowDrag]);

  return (
    <div
      ref={wrapperRef}
      className={className}
      style={{
        userSelect: 'none', // CSS 禁用文本选择
        WebkitUserSelect: 'none', // Safari
        MozUserSelect: 'none', // Firefox
        msUserSelect: 'none', // IE
        WebkitTouchCallout: 'none', // iOS Safari
        WebkitTapHighlightColor: 'transparent', // 移除点击高亮
        ...style
      }}
    >
      {children}
    </div>
  );
}
