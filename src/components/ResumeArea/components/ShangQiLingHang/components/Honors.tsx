'use client'

import React, { useState, useCallback, useMemo } from 'react'
import { toast } from 'sonner'
import { useModuleStore, type HonorsItem, type HonorValueItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import ModuleActionButtons from './ModuleActionButtons'
import DeleteConfirmDialog from '@/components/ModuleManager/components/DeleteConfirmDialog'
import { useModuleActions } from '../hooks/useModuleActions'
import HonorStylePreview from '@/components/HonorStylePreview'
import CustomModuleTitle from './CustomModuleTitle'

const Honors: React.FC = () => {
  const { modules, setActiveModule, deleteModule } = useModuleStore()
  const { page_margin, module_spacing, resume_color } = useResumeStyleStore()

  // 删除弹窗状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  // 获取荣誉数据
  const honorsModule = modules['honors']
  const honorsData = useMemo(() => {
    if (honorsModule?.item && typeof honorsModule.item === 'object' && 'values' in honorsModule.item) {
      return honorsModule.item as HonorsItem
    }
    return null
  }, [honorsModule?.item])

  const honorItems = useMemo(() => {
    if (honorsData?.values?.value) {
      return (honorsData.values.value as HonorValueItem[]).sort((a, b) => a.index - b.index)
    }
    return []
  }, [honorsData])

  // 从模块数据中获取样式配置，如果没有则使用默认值
  const moduleHonorWallLayout = honorsData?.honorWallLayout?.value || '1'
  const moduleHonorWallStyle = honorsData?.honorWallStyle?.value || '1'



  // 处理删除模块弹窗
  const handleShowDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(true)
  }, [])

  // 确认删除模块
  const handleConfirmDelete = useCallback(() => {
    deleteModule('honors')
    setDeleteDialogOpen(false)
    toast.success(`${honorsModule?.name || '荣誉奖项'}模块已删除`)
  }, [deleteModule, honorsModule?.name])

  // 使用模块操作hook
  const { handleMoveUp, handleMoveDown, handleDelete } = useModuleActions({
    moduleId: 'honors',
    moduleName: honorsModule?.name || '荣誉奖项',
    onDelete: handleShowDeleteDialog
  })

  // 处理点击事件，切换到荣誉模块
  const handleClick = () => {
    setActiveModule('honors')
  }

  // 如果没有荣誉数据或不可见，不渲染
  if (!honorsModule?.is_visible || !honorItems.length) {
    return null
  }

  // 根据布局渲染荣誉项
  const renderHonorItems = () => {
    if (moduleHonorWallLayout === '1') {
      // 标题平铺-底色布局
      return (
        <div className="flex flex-wrap gap-2">
          {honorItems.map((item) => (
            <div
              key={item.index}
              className="inline-block px-3 py-1 bg-gray-100 rounded-md"
            >
              <HonorStylePreview style={moduleHonorWallStyle} text={item.name?.value || ''} />
            </div>
          ))}
        </div>
      )
    } else if (moduleHonorWallLayout === '2') {
      // 标题平铺-边框布局
      return (
        <div className="flex flex-wrap gap-2">
          {honorItems.map((item) => (
            <div
              key={item.index}
              className="inline-block px-3 py-1 border border-gray-300 rounded-md"
            >
              <HonorStylePreview style={moduleHonorWallStyle} text={item.name?.value || ''} />
            </div>
          ))}
        </div>
      )
    } else if (moduleHonorWallLayout === '3') {
      // 分栏罗列-单栏布局
      return (
        <div className="space-y-2">
          {honorItems.map((item) => (
            <div key={item.index} className="flex items-start">
              <HonorStylePreview style={moduleHonorWallStyle} text={item.name?.value || ''} />
            </div>
          ))}
        </div>
      )
    } else if (moduleHonorWallLayout === '4') {
      // 分栏罗列-双栏布局
      return (
        <div className="grid grid-cols-2 gap-2">
          {honorItems.map((item) => (
            <div key={item.index} className="flex items-start">
              <HonorStylePreview style={moduleHonorWallStyle} text={item.name?.value || ''} />
            </div>
          ))}
        </div>
      )
    } else {
      // 默认列表布局
      return (
        <div className="space-y-2">
          {honorItems.map((item) => (
            <div key={item.index} className="flex items-start">
              <HonorStylePreview style={moduleHonorWallStyle} text={item.name?.value || ''} />
            </div>
          ))}
        </div>
      )
    }
  }

  return (
    <div
      className="w-full cursor-move transition-all duration-300 hover:bg-purple-50 rounded-lg group/module relative"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
      onClick={handleClick}
    >
      {/* Hover时显示的操作按钮 */}
      <ModuleActionButtons
        onMoveUp={handleMoveUp}
        onMoveDown={handleMoveDown}
        onDelete={handleDelete}
      />
      <div className="space-y-0">
        {/* 自定义模块标题 */}
        <CustomModuleTitle
          title={honorsModule?.name || '荣誉奖项'}
        />
        {/* 荣誉内容 */}
        <div
          style={{
            borderTop: `1px solid ${resume_color}`,
            borderLeft: `1px solid ${resume_color}`,
            padding: `6px 6px ${module_spacing} 16px`
          }}
        >
          {renderHonorItems()}
        </div>
      </div>

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        moduleName={honorsModule?.name || '荣誉奖项'}
        onConfirm={handleConfirmDelete}
      />
    </div>
  )
}

export default Honors
