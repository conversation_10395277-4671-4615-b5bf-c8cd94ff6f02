'use client'

import React from 'react'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'

interface CustomModuleTitleProps {
  title: string
}

const CustomModuleTitle: React.FC<CustomModuleTitleProps> = ({ title }) => {
  const { resume_color, font_size } = useResumeStyleStore()

  // 计算标题字体大小（比基础字体大2px）
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = `${baseFontSize + 2}px`

  return (
    <div className="titleCom13-box relative" style={{ transform: 'translate(-12px)', zIndex: 10 }}>
      <div 
        className="title-box relative inline-block px-6 py-1 font-bold text-white whitespace-nowrap"
        style={{
          fontSize: titleFontSize,
          backgroundColor: resume_color,
          paddingLeft: '24px',
          paddingRight: '10px'
        }}
      >
        {title}
      </div>
      <div 
        className="decoration absolute"
        style={{
          width: '12px',
          height: '10px',
          clipPath: 'polygon(100% 0, 0 0, 100% 50%)',
          backgroundColor: '#cacaca',
          bottom: '-10px',
          left: '0'
        }}
      ></div>
    </div>
  )
}

export default CustomModuleTitle
