'use client'

import React, { useState, useCallback, useEffect, useMemo } from 'react'
import { ReactSortable } from 'react-sortablejs'
import { toast } from 'sonner'
import { useModuleStore, type ResearchItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import ModuleActionButtons from './ModuleActionButtons'
import ItemActionButtons from './ItemActionButtons'
import DeleteConfirmDialog from '@/components/ModuleManager/components/DeleteConfirmDialog'
import ItemDeleteConfirmDialog from './ItemDeleteConfirmDialog'
import { useModuleActions } from '../hooks/useModuleActions'
import MarkdownViewer from '@/components/MarkdownViewer'
import CustomModuleTitle from './CustomModuleTitle'

const Research: React.FC = () => {
  const { modules, setActiveModule, setActiveIndex, updateModuleItem, deleteModule } = useModuleStore()
  const { page_margin, date_format, separator, date_align, title_align, module_spacing, resume_color } = useResumeStyleStore()

  // 删除弹窗状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  // 研究经历项删除弹窗状态
  const [itemDeleteDialogOpen, setItemDeleteDialogOpen] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<string | null>(null)

  // 获取研究经历数据
  const researchModule = modules['research']
  const researchItems = useMemo(() =>
    (researchModule?.item as ResearchItem[]) || [],
    [researchModule?.item]
  )

  // 本地状态管理排序后的研究经历列表
  const [sortableItems, setSortableItems] = useState(() =>
    [...researchItems].sort((a, b) => a.index - b.index)
  )

  // 处理拖拽排序
  const handleSort = useCallback((newList: ResearchItem[]) => {
    // 更新索引
    const updatedItems = newList.map((item, index) => ({
      ...item,
      index
    }))

    setSortableItems(updatedItems)

    // 更新store中的数据
    updateModuleItem('research', updatedItems)
  }, [updateModuleItem])

  // 同步store数据变化到本地状态
  useEffect(() => {
    const sortedItems = [...researchItems].sort((a, b) => a.index - b.index)
    setSortableItems(sortedItems)
  }, [researchItems])

  // 处理删除模块弹窗
  const handleShowDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(true)
  }, [])

  // 确认删除模块
  const handleConfirmDelete = useCallback(() => {
    deleteModule('research')
    setDeleteDialogOpen(false)
    toast.success('研究经历模块已删除')
  }, [deleteModule])

  // 处理研究经历项删除弹窗
  const handleShowItemDeleteDialog = useCallback((itemId: string) => {
    setItemToDelete(itemId)
    setItemDeleteDialogOpen(true)
  }, [])

  // 确认删除研究经历项
  const handleConfirmItemDelete = useCallback(() => {
    if (itemToDelete) {
      // 找到要删除的项目信息用于提示
      const itemToDeleteInfo = sortableItems.find(item => item.id === itemToDelete)
      const researchName = itemToDeleteInfo?.name?.value || '该研究经历'

      const newItems = sortableItems.filter(item => item.id !== itemToDelete)

      // 更新索引
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        index
      }))

      setSortableItems(updatedItems)
      updateModuleItem('research', updatedItems)
      toast.success(`已删除 ${researchName}`)
    }

    setItemDeleteDialogOpen(false)
    setItemToDelete(null)
  }, [itemToDelete, sortableItems, updateModuleItem])

  // 使用模块操作hook
  const { handleMoveUp, handleMoveDown, handleDelete } = useModuleActions({
    moduleId: 'research',
    moduleName: '研究经历',
    onDelete: handleShowDeleteDialog
  })

  // 处理点击事件，切换到研究经历模块
  const handleClick = () => {
    setActiveModule('research')
  }

  // 处理研究经历项点击事件，设置对应的 activeIndex
  const handleItemClick = useCallback((itemIndex: number) => (e: React.MouseEvent) => {
    e.stopPropagation() // 阻止事件冒泡到模块点击事件
    setActiveModule('research') // 确保切换到研究经历模块
    setActiveIndex([itemIndex]) // 设置对应的 activeIndex
  }, [setActiveModule, setActiveIndex])

  // 处理研究经历项的向上移动
  const handleItemMoveUp = useCallback((itemId: string) => (e: React.MouseEvent) => {
    e.stopPropagation()
    const currentIndex = sortableItems.findIndex(item => item.id === itemId)

    // 检查是否已经是第一个
    if (currentIndex === 0) {
      toast.error('已经是第一个研究经历了')
      return
    }

    if (currentIndex > 0) {
      const newItems = [...sortableItems]
      const [movedItem] = newItems.splice(currentIndex, 1)
      newItems.splice(currentIndex - 1, 0, movedItem)

      // 更新索引
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        index
      }))

      setSortableItems(updatedItems)
      updateModuleItem('research', updatedItems)
      toast.success('研究经历已向上移动')
    }
  }, [sortableItems, updateModuleItem])

  // 处理研究经历项的向下移动
  const handleItemMoveDown = useCallback((itemId: string) => (e: React.MouseEvent) => {
    e.stopPropagation()
    const currentIndex = sortableItems.findIndex(item => item.id === itemId)

    // 检查是否已经是最后一个
    if (currentIndex === sortableItems.length - 1) {
      toast.error('已经是最后一个研究经历了')
      return
    }

    if (currentIndex < sortableItems.length - 1) {
      const newItems = [...sortableItems]
      const [movedItem] = newItems.splice(currentIndex, 1)
      newItems.splice(currentIndex + 1, 0, movedItem)

      // 更新索引
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        index
      }))

      setSortableItems(updatedItems)
      updateModuleItem('research', updatedItems)
      toast.success('研究经历已向下移动')
    }
  }, [sortableItems, updateModuleItem])

  // 处理研究经历项的删除
  const handleItemDelete = useCallback((itemId: string) => (e: React.MouseEvent) => {
    e.stopPropagation()
    handleShowItemDeleteDialog(itemId)
  }, [handleShowItemDeleteDialog])

  // 如果没有研究经历数据，不渲染
  if (!sortableItems.length) {
    return null
  }

  // 格式化时间显示
  const formatTimeRange = (startDate: string, endDate: string) => {
    if (!startDate && !endDate) return ''

    // 格式化单个日期
    const formatSingleDate = (date: string) => {
      if (!date || date === '至今') return date

      // 解析日期格式 YYYY-MM
      const parts = date.split('-')
      if (parts.length !== 2) return date

      const year = parts[0]
      const month = parts[1]

      // 根据date_format配置格式化
      if (date_format === 'YYYY年MM月') {
        return `${year}年${month}月`
      } else {
        // 默认格式 'YYYY.MM'
        return `${year}.${month}`
      }
    }

    const formattedStart = formatSingleDate(startDate)
    const formattedEnd = formatSingleDate(endDate)

    if (!startDate) return formattedEnd
    if (!endDate) return `${formattedStart}${separator}`
    return `${formattedStart}${separator}${formattedEnd}`
  }

  return (
    <div
      className="w-full cursor-move transition-all duration-300 hover:bg-purple-50 rounded-lg group/module relative"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
      onClick={handleClick}
    >
      {/* Hover时显示的操作按钮 */}
      <ModuleActionButtons
        onMoveUp={handleMoveUp}
        onMoveDown={handleMoveDown}
        onDelete={handleDelete}
      />
      <div className="space-y-0">
        {/* 自定义模块标题 */}
        <CustomModuleTitle
          title="研究经历"
        />
        {/* 研究经历列表 */}
        <ReactSortable
          list={sortableItems}
          setList={handleSort}
          animation={200}
          delay={2}
          ghostClass="opacity-50"
          chosenClass="shadow-lg"
          dragClass="rotate-1"
          group="research"
          className="space-y-4"
          style={{
            borderTop: `1px solid ${resume_color}`,
            borderLeft: `1px solid ${resume_color}`,
            padding: `6px 6px ${module_spacing} 16px`
          }}
        >
          {sortableItems.map((item, index) => (
            <div
              key={item.id}
              className="space-y-2 rounded-lg transition-all duration-300 hover:bg-purple-100 cursor-move relative group/item"
              onClick={handleItemClick(index)}
            >
              {/* 研究经历项的操作按钮 */}
              <ItemActionButtons
                onMoveUp={handleItemMoveUp(item.id)}
                onMoveDown={handleItemMoveDown(item.id)}
                onDelete={handleItemDelete(item.id)}
              />
              {/* 根据title_align配置调整布局 */}
              {title_align === 'justify' ? (
                // 均匀分布：使用flex布局，模拟el-row el-col结构
                <>
                  {/* 第一行：根据date_align调整列的顺序 */}
                  <div className="flex gap-1 font-semibold">
                    {date_align === 'left' ? (
                      <>
                        {/* 日期在左 (6/24 = 25%) */}
                        <div className="flex-none text-left" style={{ width: '25%' }}>
                          <span>{formatTimeRange(item.start_month?.value || '', item.end_month?.value || '')}</span>
                        </div>
                        {/* 研究项目名称 (8/24 = 33.33%) */}
                        <div className="flex-none" style={{ width: '33.33%' }}>
                          <span>{item.name?.value || ''}</span>
                        </div>
                        {/* 担任角色 (5/24 = 20.83%) */}
                        <div className="flex-none text-left" style={{ width: '20.83%' }}>
                          <span>{item.role?.value || ''}</span>
                        </div>
                        {/* 所属部门 (5/24 = 20.83%) */}
                        <div className="flex-none text-left" style={{ width: '20.83%' }}>
                          <span>{item.department?.value || ''}</span>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* 研究项目名称 (8/24 = 33.33%) */}
                        <div className="flex-none" style={{ width: '33.33%' }}>
                          <span>{item.name?.value || ''}</span>
                        </div>
                        {/* 担任角色 (5/24 = 20.83%) */}
                        <div className="flex-none text-left" style={{ width: '20.83%' }}>
                          <span>{item.role?.value || ''}</span>
                        </div>
                        {/* 所属部门 (5/24 = 20.83%) */}
                        <div className="flex-none text-left" style={{ width: '20.83%' }}>
                          <span>{item.department?.value || ''}</span>
                        </div>
                        {/* 日期在右 (6/24 = 25%) */}
                        <div className="flex-none text-right" style={{ width: '25%' }}>
                          <span>{formatTimeRange(item.start_month?.value || '', item.end_month?.value || '')}</span>
                        </div>
                      </>
                    )}
                  </div>
                  {/* 第二行：城市 */}
                  <div className="flex gap-1 font-semibold">
                    <div className="flex-none" style={{ width: '100%' }}>
                      <span>{item.city?.value || ''}</span>
                    </div>
                  </div>
                </>
              ) : (
                // 自适应布局：原有的flex布局
                <>
                  {/* 第一行：研究项目名称+担任角色 vs 日期 */}
                  <div className="flex justify-between items-start">
                    {date_align === 'left' ? (
                      <>
                        {/* 日期在左 */}
                        <div className="mr-4 flex-shrink-0 font-semibold text-left">
                          {formatTimeRange(item.start_month?.value || '', item.end_month?.value || '')}
                        </div>
                        {/* 研究信息在右 */}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 justify-end">
                            <span className="font-semibold">
                              {item.name?.value || ''} - {item.role?.value || ''}
                            </span>
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* 研究信息在左 */}
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">
                              {item.name?.value || ''} - {item.role?.value || ''}
                            </span>
                          </div>
                        </div>
                        {/* 日期在右 */}
                        <div className="ml-4 flex-shrink-0 font-semibold text-right">
                          {formatTimeRange(item.start_month?.value || '', item.end_month?.value || '')}
                        </div>
                      </>
                    )}
                  </div>
                  {/* 第二行：所属部门 vs 城市 */}
                  <div className="flex justify-between items-center">
                    <div className="font-semibold">
                      {item.department?.value || ''}
                    </div>
                    <div className="font-semibold">
                      {item.city?.value || ''}
                    </div>
                  </div>
                </>
              )}

              {/* 第三行：研究描述 */}
              <MarkdownViewer
                content={item.desc?.value || ''}
                className="leading-relaxed"
              />
            </div>
          ))}
        </ReactSortable>
      </div>

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        moduleName="研究经历"
        onConfirm={handleConfirmDelete}
      />

      {/* 研究经历项删除确认弹窗 */}
      <ItemDeleteConfirmDialog
        open={itemDeleteDialogOpen}
        onOpenChange={setItemDeleteDialogOpen}
        onConfirm={handleConfirmItemDelete}
      />
    </div>
  )
}

export default Research
