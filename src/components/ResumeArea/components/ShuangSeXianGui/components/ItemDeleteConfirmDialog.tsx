'use client';

import React from 'react';
import {
  Dialog,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogClose
} from '@/components/ui/dialog';
import CustomDialogContent from '@/components/ModuleManager/components/CustomDialogContent';
import { AlertCircle } from 'lucide-react';

interface ItemDeleteConfirmDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => void;
}

export default function ItemDeleteConfirmDialog({
  open,
  onOpenChange,
  onConfirm
}: ItemDeleteConfirmDialogProps) {
  return (
    <Dialog
      open={open}
      onOpenChange={onOpenChange}
    >
      <CustomDialogContent
        className="sm:max-w-[425px]"
      >
        <DialogHeader>
          <div className="flex items-center justify-center gap-2 text-red-500">
            <AlertCircle className="w-6 h-6" />
            <DialogTitle className="text-center text-xl font-medium">确认删除</DialogTitle>
          </div>
        </DialogHeader>

        <div className="py-6 text-center">
          <p className="text-lg">确认删除</p>
        </div>

        <DialogFooter className="flex justify-center gap-4">
          <DialogClose asChild>
            <button
              onClick={(e) => {
                e.stopPropagation();
              }}
              className="px-8 py-2 text-sm bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 transition-colors cursor-pointer"
            >
              取消
            </button>
          </DialogClose>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onConfirm();
            }}
            className="px-8 py-2 text-sm bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors cursor-pointer"
          >
            确认删除
          </button>
        </DialogFooter>
      </CustomDialogContent>
    </Dialog>
  );
}
