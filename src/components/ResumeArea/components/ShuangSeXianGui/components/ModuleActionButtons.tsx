'use client'

import React from 'react'
import Image from 'next/image'

interface ModuleActionButtonsProps {
  onMoveUp: (e: React.MouseEvent) => void
  onMoveDown: (e: React.MouseEvent) => void
  onDelete: (e: React.MouseEvent) => void
}

const ModuleActionButtons: React.FC<ModuleActionButtonsProps> = ({
  onMoveUp,
  onMoveDown,
  onDelete
}) => {
  return (
    <div className="absolute top-2 right-2 opacity-0 group-hover/module:opacity-100 group-hover/item:opacity-100 transition-opacity duration-300 flex gap-2 z-10">
      <button
        onClick={onMoveUp}
        className="w-7 h-7 bg-white rounded shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center hover:bg-gray-50 cursor-pointer"
        title="向上移动"
      >
        <Image
          src="/resume/upload-icon.svg"
          alt="向上移动"
          width={12}
          height={12}
        />
      </button>
      <button
        onClick={onMoveDown}
        className="w-7 h-7 bg-white rounded shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center hover:bg-gray-50 cursor-pointer"
        title="向下移动"
      >
        <Image
          src="/resume/download-icon.svg"
          alt="向下移动"
          width={12}
          height={12}
        />
      </button>
      <button
        onClick={onDelete}
        className="w-7 h-7 bg-white rounded shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center hover:bg-red-50 cursor-pointer"
        title="删除模块"
      >
        <Image
          src="/resume/delete-icon.svg"
          alt="删除"
          width={12}
          height={12}
        />
      </button>
    </div>
  )
}

export default ModuleActionButtons
