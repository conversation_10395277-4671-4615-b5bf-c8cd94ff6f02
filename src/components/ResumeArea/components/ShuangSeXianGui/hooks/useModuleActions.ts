import { useCallback } from 'react'
import { toast } from 'sonner'
import { useModuleStore } from '@/store/useModuleStore'

interface UseModuleActionsProps {
  moduleId: string
  moduleName: string
  onDelete?: () => void // 可选的删除回调，用于触发删除弹窗
}

export const useModuleActions = ({ moduleId, moduleName, onDelete }: UseModuleActionsProps) => {
  const { modules, customModules, reorderModules } = useModuleStore()

  // 获取所有可见模块（包括传统模块和自定义模块），按index排序
  const visibleModules = [
    // 添加传统的 modules
    ...Object.values(modules).filter(module => module.is_visible),
    // 添加新的 customModules，转换为 Module 格式
    ...customModules.map(customModule => ({
      id: customModule.id,
      name: customModule.name,
      type: 'custom' as const,
      is_visible: true,
      is_custom: true,
      is_required: false,
      support_ai: false,
      index: customModule.index,
      item: customModule.items
    }))
  ].sort((a, b) => a.index - b.index)

  // 找到当前模块在可见模块中的索引
  const currentIndex = visibleModules.findIndex(module => module.id === moduleId)

  // 处理向上移动
  const handleMoveUp = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()

    // 检查是否已经是第一个
    if (currentIndex <= 0) {
      toast.error(`${moduleName}已经是第一个模块了`)
      return
    }

    // 执行向上移动
    reorderModules(currentIndex, currentIndex - 1)
    toast.success(`${moduleName}已向上移动`)
  }, [currentIndex, moduleName, reorderModules])

  // 处理向下移动
  const handleMoveDown = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()

    // 检查是否已经是最后一个
    if (currentIndex >= visibleModules.length - 1) {
      toast.error(`${moduleName}已经是最后一个模块了`)
      return
    }

    // 执行向下移动
    reorderModules(currentIndex, currentIndex + 1)
    toast.success(`${moduleName}已向下移动`)
  }, [currentIndex, moduleName, reorderModules, visibleModules.length])

  // 处理删除模块
  const handleDelete = useCallback((e: React.MouseEvent) => {
    e.stopPropagation()

    // 如果提供了删除回调，则调用它（用于显示删除弹窗）
    if (onDelete) {
      onDelete()
    } else {
      // 否则直接删除（这种情况不应该发生，但作为后备）
    }
  }, [moduleName, onDelete])

  return {
    handleMoveUp,
    handleMoveDown,
    handleDelete
  }
}
