'use client'

import React from 'react'
import Image from 'next/image'

interface ItemActionButtonsProps {
  onMoveUp: (e: React.MouseEvent) => void
  onMoveDown: (e: React.MouseEvent) => void
  onDelete: (e: React.MouseEvent) => void
}

const ItemActionButtons: React.FC<ItemActionButtonsProps> = ({
  onMoveUp,
  onMoveDown,
  onDelete
}) => {
  return (
    <div className="absolute top-1 right-1 opacity-0 group-hover/item:opacity-100 transition-opacity duration-300 flex gap-1 z-20">
      <button
        onClick={onMoveUp}
        className="w-5 h-5 bg-white rounded shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center hover:bg-gray-50 cursor-pointer"
        title="向上移动"
      >
        <Image
          src="/resume/upload-icon.svg"
          alt="向上移动"
          width={10}
          height={10}
        />
      </button>
      <button
        onClick={onMoveDown}
        className="w-5 h-5 bg-white rounded shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center hover:bg-gray-50 cursor-pointer"
        title="向下移动"
      >
        <Image
          src="/resume/download-icon.svg"
          alt="向下移动"
          width={10}
          height={10}
        />
      </button>
      <button
        onClick={onDelete}
        className="w-5 h-5 bg-white rounded shadow-sm hover:shadow-md transition-all duration-200 flex items-center justify-center hover:bg-red-50 cursor-pointer"
        title="删除项目"
      >
        <Image
          src="/resume/delete-icon.svg"
          alt="删除"
          width={10}
          height={10}
        />
      </button>
    </div>
  )
}

export default ItemActionButtons
