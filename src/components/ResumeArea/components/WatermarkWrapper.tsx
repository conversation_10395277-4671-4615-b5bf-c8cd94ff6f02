'use client';

import React from 'react';

interface WatermarkWrapperProps {
  children: React.ReactNode;
  className?: string;
  style?: React.CSSProperties;
  opacity?: number; // 水印透明度，默认0.1
  showWatermark?: boolean; // 是否显示水印，默认true
}

/**
 * 水印包装组件
 * 使用背景图片的方式添加水印
 */
export default function WatermarkWrapper({
  children,
  className,
  style,
  opacity = 0.1,
  showWatermark = true
}: WatermarkWrapperProps) {
  const watermarkImageUrl = '/bg/water.jpg';

  // 创建更密集的水印位置 - 9个水印位置，3x3网格布局
  const watermarkPositions = [
    '15% 15%',   // 左上
    '50% 15%',   // 中上
    '85% 15%',   // 右上
    '15% 50%',   // 左中
    '50% 50%',   // 正中
    '85% 50%',   // 右中
    '15% 85%',   // 左下
    '50% 85%',   // 中下
    '85% 85%'    // 右下
  ];

  // 生成多个水印图片URL
  const watermarkImages = watermarkPositions.map(() => `url(${watermarkImageUrl})`).join(', ');

  // 生成重复设置
  const watermarkRepeats = watermarkPositions.map(() => 'no-repeat').join(', ');

  // 生成位置设置
  const watermarkPositionStr = watermarkPositions.join(', ');

  // 生成尺寸设置 - 使用较小的尺寸让水印更密集
  const watermarkSizes = watermarkPositions.map(() => '150px 75px').join(', ');

  return (
    <div
      id='watermark-wrapper'
      className={className}
      style={{
        position: 'relative',
        width: '100%',
        height: '100%',
        backgroundImage: showWatermark ? watermarkImages : 'none',
        backgroundRepeat: showWatermark ? watermarkRepeats : 'no-repeat',
        backgroundPosition: showWatermark ? watermarkPositionStr : 'center',
        backgroundSize: showWatermark ? watermarkSizes : 'auto',
        opacity: 1, // 容器本身不透明
        ...style
      }}
    >
      {/* 水印遮罩层 */}
      {showWatermark && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: watermarkImages,
            backgroundRepeat: watermarkRepeats,
            backgroundPosition: watermarkPositionStr,
            backgroundSize: watermarkSizes,
            opacity: opacity,
            pointerEvents: 'none', // 不阻挡鼠标事件
            zIndex: 1
          }}
        />
      )}

      {/* 内容层 */}
      <div style={{ position: 'relative', zIndex: 2 }}>
        {children}
      </div>
    </div>
  );
}
