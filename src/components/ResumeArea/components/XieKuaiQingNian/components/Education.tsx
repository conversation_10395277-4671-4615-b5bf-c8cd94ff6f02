'use client'

import React, { useState, useCallback, useEffect, useMemo } from 'react'
import { ReactSortable } from 'react-sortablejs'
import { toast } from 'sonner'
import { useModuleStore, type EducationItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import ResumeTitle from '@/components/Title/ResumeTitle'
import ModuleActionButtons from './ModuleActionButtons'
import ItemActionButtons from './ItemActionButtons'
import DeleteConfirmDialog from '@/components/ModuleManager/components/DeleteConfirmDialog'
import ItemDeleteConfirmDialog from './ItemDeleteConfirmDialog'
import { useModuleActions } from '../hooks/useModuleActions'
import MarkdownViewer from '@/components/MarkdownViewer'
import EducationIcon from '@/assets/svg/education-icon.svg'

const Education: React.FC = () => {
  const { modules, setActiveModule, setActiveIndex, updateModuleItem, deleteModule } = useModuleStore()
  const { page_margin, font_size, date_format, separator, date_align, title_align } = useResumeStyleStore()

  // 删除弹窗状态
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)

  // 教育经历项删除弹窗状态
  const [itemDeleteDialogOpen, setItemDeleteDialogOpen] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<string | null>(null)

  // 计算标题字体大小（比基础字体大2px）
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = `${baseFontSize + 2}px`

  // 获取教育经历数据
  const educationModule = modules['education']
  const educationItems = useMemo(() =>
    (educationModule?.item as EducationItem[]) || [],
    [educationModule?.item]
  )

  // 本地状态管理排序后的教育经历列表
  const [sortableItems, setSortableItems] = useState(() =>
    [...educationItems].sort((a, b) => a.index - b.index)
  )

  // 处理拖拽排序
  const handleSort = useCallback((newList: EducationItem[]) => {
    // 更新索引
    const updatedItems = newList.map((item, index) => ({
      ...item,
      index
    }))

    setSortableItems(updatedItems)

    // 更新store中的数据
    updateModuleItem('education', updatedItems)
  }, [updateModuleItem])

  // 同步store数据变化到本地状态
  useEffect(() => {
    const sortedItems = [...educationItems].sort((a, b) => a.index - b.index)
    setSortableItems(sortedItems)
  }, [educationItems])

  // 处理删除模块弹窗
  const handleShowDeleteDialog = useCallback(() => {
    setDeleteDialogOpen(true)
  }, [])

  // 确认删除模块
  const handleConfirmDelete = useCallback(() => {
    deleteModule('education')
    setDeleteDialogOpen(false)
    toast.success(`${educationModule?.name || '教育经历'}模块已删除`)
  }, [deleteModule, educationModule?.name])

  // 处理教育经历项删除弹窗
  const handleShowItemDeleteDialog = useCallback((itemId: string) => {
    setItemToDelete(itemId)
    setItemDeleteDialogOpen(true)
  }, [])

  // 确认删除教育经历项
  const handleConfirmItemDelete = useCallback(() => {
    if (itemToDelete) {
      // 找到要删除的项目信息用于提示
      const itemToDeleteInfo = sortableItems.find(item => item.id === itemToDelete)
      const schoolName = itemToDeleteInfo?.school_name?.value || '该教育经历'

      const newItems = sortableItems.filter(item => item.id !== itemToDelete)

      // 更新索引
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        index
      }))

      setSortableItems(updatedItems)
      updateModuleItem('education', updatedItems)
      toast.success(`已删除 ${schoolName}`)
    }

    setItemDeleteDialogOpen(false)
    setItemToDelete(null)
  }, [itemToDelete, sortableItems, updateModuleItem])

  // 使用模块操作hook
  const { handleMoveUp, handleMoveDown, handleDelete } = useModuleActions({
    moduleId: 'education',
    moduleName: educationModule?.name || '教育经历',
    onDelete: handleShowDeleteDialog
  })

  // 处理点击事件，切换到教育经历模块
  const handleClick = () => {
    setActiveModule('education')
  }

  // 处理教育经历项点击事件，设置对应的 activeIndex
  const handleItemClick = useCallback((itemIndex: number) => (e: React.MouseEvent) => {
    e.stopPropagation() // 阻止事件冒泡到模块点击事件
    setActiveModule('education') // 确保切换到教育经历模块
    setActiveIndex([itemIndex]) // 设置对应的 activeIndex
  }, [setActiveModule, setActiveIndex])

  // 处理教育经历项的向上移动
  const handleItemMoveUp = useCallback((itemId: string) => (e: React.MouseEvent) => {
    e.stopPropagation()
    const currentIndex = sortableItems.findIndex(item => item.id === itemId)

    // 检查是否已经是第一个
    if (currentIndex === 0) {
      toast.error('已经是第一个教育经历了')
      return
    }

    if (currentIndex > 0) {
      const newItems = [...sortableItems]
      const [movedItem] = newItems.splice(currentIndex, 1)
      newItems.splice(currentIndex - 1, 0, movedItem)

      // 更新索引
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        index
      }))

      setSortableItems(updatedItems)
      updateModuleItem('education', updatedItems)
      toast.success('教育经历已向上移动')
    }
  }, [sortableItems, updateModuleItem])

  // 处理教育经历项的向下移动
  const handleItemMoveDown = useCallback((itemId: string) => (e: React.MouseEvent) => {
    e.stopPropagation()
    const currentIndex = sortableItems.findIndex(item => item.id === itemId)

    // 检查是否已经是最后一个
    if (currentIndex === sortableItems.length - 1) {
      toast.error('已经是最后一个教育经历了')
      return
    }

    if (currentIndex < sortableItems.length - 1) {
      const newItems = [...sortableItems]
      const [movedItem] = newItems.splice(currentIndex, 1)
      newItems.splice(currentIndex + 1, 0, movedItem)

      // 更新索引
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        index
      }))

      setSortableItems(updatedItems)
      updateModuleItem('education', updatedItems)
      toast.success('教育经历已向下移动')
    }
  }, [sortableItems, updateModuleItem])

  // 处理教育经历项的删除
  const handleItemDelete = useCallback((itemId: string) => (e: React.MouseEvent) => {
    e.stopPropagation()
    handleShowItemDeleteDialog(itemId)
  }, [handleShowItemDeleteDialog])

  // 如果没有教育经历数据，不渲染
  if (!sortableItems.length) {
    return null
  }

  // 格式化时间显示
  const formatTimeRange = (startDate: string, endDate: string) => {
    if (!startDate && !endDate) return ''

    // 格式化单个日期
    const formatSingleDate = (date: string) => {
      if (!date || date === '至今') return date

      // 解析日期格式 YYYY-MM
      const parts = date.split('-')
      if (parts.length !== 2) return date

      const year = parts[0]
      const month = parts[1]

      // 根据date_format配置格式化
      if (date_format === 'YYYY年MM月') {
        return `${year}年${month}月`
      } else {
        // 默认格式 'YYYY.MM'
        return `${year}.${month}`
      }
    }

    const formattedStart = formatSingleDate(startDate)
    const formattedEnd = formatSingleDate(endDate)

    if (!startDate) return formattedEnd
    if (!endDate) return `${formattedStart}${separator}`
    return `${formattedStart}${separator}${formattedEnd}`
  }

  return (
    <div
      className="w-full cursor-move transition-all duration-300 hover:bg-purple-50 rounded-lg group/module relative"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
      onClick={handleClick}
    >
      {/* Hover时显示的操作按钮 */}
      <ModuleActionButtons
        onMoveUp={handleMoveUp}
        onMoveDown={handleMoveDown}
        onDelete={handleDelete}
      />
      <div className="space-y-4">
        {/* 模块标题 */}
        <ResumeTitle
          title={educationModule?.name || '教育经历'}
          fontSize={titleFontSize}
          lineColor="rgb(229, 229, 229)"
          icon={EducationIcon}
        />

        {/* 教育经历列表 */}
        <ReactSortable
          list={sortableItems}
          setList={handleSort}
          animation={200}
          delay={2}
          ghostClass="opacity-50"
          chosenClass="shadow-lg"
          dragClass="rotate-1"
          group="education"
          className="space-y-4"
        >
          {sortableItems.map((item, index) => (
            <div
              key={item.id}
              className="space-y-2 rounded-lg transition-all duration-300 hover:bg-purple-100 cursor-move relative group/item"
              onClick={handleItemClick(index)}
            >
              {/* 教育经历项的操作按钮 */}
              <ItemActionButtons
                onMoveUp={handleItemMoveUp(item.id)}
                onMoveDown={handleItemMoveDown(item.id)}
                onDelete={handleItemDelete(item.id)}
              />
              {/* 根据title_align配置调整布局 */}
              {title_align === 'justify' ? (
                // 均匀分布：使用flex布局，模拟el-row el-col结构
                <>
                  {/* 第一行：根据date_align调整列的顺序 */}
                  <div className="flex gap-1 font-semibold">
                    {date_align === 'left' ? (
                      <>
                        {/* 日期在左 (6/24 = 25%) */}
                        <div className="flex-none text-left" style={{ width: '25%' }}>
                          <span>{formatTimeRange(item.start_date?.value || '', item.end_date?.value || '')}</span>
                        </div>
                        {/* 学校名称+标签 (8/24 = 33.33%) */}
                        <div className="flex-none" style={{ width: '33.33%' }}>
                          <div className="flex gap-2 items-center">
                            <span>{item.school_name?.value || ''}</span>
                            {(item.school_tags?.value || []).length > 0 && (
                              <div className="flex gap-1">
                                {(item.school_tags?.value || []).map((tag, index) => (
                                  <span
                                    key={index}
                                    className="px-[6px] py-[2px] text-xs rounded-[3px] whitespace-nowrap font-normal"
                                    style={{
                                      backgroundColor: '#E6F9FF',
                                      color: '#3E8ED8',
                                      lineHeight: '1'
                                    }}
                                  >
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                        {/* 专业 (5/24 = 20.83%) */}
                        <div className="flex-none text-left" style={{ width: '20.83%' }}>
                          <span>{item.major?.value || ''}</span>
                        </div>
                        {/* 学历 (5/24 = 20.83%) */}
                        <div className="flex-none text-left" style={{ width: '20.83%' }}>
                          <span>{item.degree?.value || ''}</span>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* 学校名称+标签 (8/24 = 33.33%) */}
                        <div className="flex-none" style={{ width: '33.33%' }}>
                          <div className="flex gap-2 items-center">
                            <span>{item.school_name?.value || ''}</span>
                            {(item.school_tags?.value || []).length > 0 && (
                              <div className="flex gap-1">
                                {(item.school_tags?.value || []).map((tag, index) => (
                                  <span
                                    key={index}
                                    className="px-[6px] py-[2px] text-xs rounded-[3px] whitespace-nowrap font-normal"
                                    style={{
                                      backgroundColor: '#E6F9FF',
                                      color: '#3E8ED8',
                                      lineHeight: '1'
                                    }}
                                  >
                                    {tag}
                                  </span>
                                ))}
                              </div>
                            )}
                          </div>
                        </div>
                        {/* 专业 (5/24 = 20.83%) */}
                        <div className="flex-none text-left" style={{ width: '20.83%' }}>
                          <span>{item.major?.value || ''}</span>
                        </div>
                        {/* 学历 (5/24 = 20.83%) */}
                        <div className="flex-none text-left" style={{ width: '20.83%' }}>
                          <span>{item.degree?.value || ''}</span>
                        </div>
                        {/* 日期在右 (6/24 = 25%) */}
                        <div className="flex-none text-right" style={{ width: '25%' }}>
                          <span>{formatTimeRange(item.start_date?.value || '', item.end_date?.value || '')}</span>
                        </div>
                      </>
                    )}
                  </div>
                  {/* 第二行：学院(12) | 城市(12) */}
                  <div className="flex gap-1 font-semibold">
                    <div className="flex-none" style={{ width: '50%' }}>
                      <span>{item.college_name?.value || ''}</span>
                    </div>
                    <div className="flex-none text-right" style={{ width: '50%' }}>
                      <span>{item.city?.value || ''}</span>
                    </div>
                  </div>
                </>
              ) : (
                // 自适应布局：原有的flex布局
                <>
                  {/* 第一行：学校名称+学历+标签 vs 日期 */}
                  <div className="flex justify-between items-start">
                    {date_align === 'left' ? (
                      <>
                        {/* 日期在左 */}
                        <div className="mr-4 flex-shrink-0 font-semibold text-left">
                          {formatTimeRange(item.start_date?.value || '', item.end_date?.value || '')}
                        </div>
                        {/* 学校信息在右 */}
                        <div className="flex-1">
                          <div className="flex items-center gap-2 justify-end">
                            <span className="font-semibold">
                              {item.school_name?.value || ''} - {item.degree?.value || ''}
                            </span>
                            {(item.school_tags?.value || []).map((tag, index) => (
                              <span
                                key={index}
                                className="px-2 text-xs rounded"
                                style={{
                                  backgroundColor: '#e6f3ff',
                                  color: '#0066cc'
                                }}
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                      </>
                    ) : (
                      <>
                        {/* 学校信息在左 */}
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-semibold">
                              {item.school_name?.value || ''} - {item.degree?.value || ''}
                            </span>
                            {(item.school_tags?.value || []).map((tag, index) => (
                              <span
                                key={index}
                                className="px-2 text-xs rounded"
                                style={{
                                  backgroundColor: '#e6f3ff',
                                  color: '#0066cc'
                                }}
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        </div>
                        {/* 日期在右 */}
                        <div className="ml-4 flex-shrink-0 font-semibold text-right">
                          {formatTimeRange(item.start_date?.value || '', item.end_date?.value || '')}
                        </div>
                      </>
                    )}
                  </div>
                  {/* 第二行：专业-学院 vs 城市 */}
                  <div className="flex justify-between items-center">
                    <div className="font-semibold">
                      {item.major?.value && item.college_name?.value && (
                        <span>{item.major.value} - {item.college_name.value}</span>
                      )}
                      {item.major?.value && !item.college_name?.value && (
                        <span>{item.major.value}</span>
                      )}
                      {!item.major?.value && item.college_name?.value && (
                        <span>{item.college_name.value}</span>
                      )}
                    </div>
                    <div className="font-semibold">
                      {item.city?.value || ''}
                    </div>
                  </div>
                </>
              )}

              {/* 第三行：描述信息 */}
              <MarkdownViewer
                content={item.description?.value || ''}
                className="leading-relaxed"
              />
            </div>
          ))}
        </ReactSortable>
      </div>

      {/* 删除确认弹窗 */}
      <DeleteConfirmDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        moduleName={educationModule?.name || '教育经历'}
        onConfirm={handleConfirmDelete}
      />

      {/* 教育经历项删除确认弹窗 */}
      <ItemDeleteConfirmDialog
        open={itemDeleteDialogOpen}
        onOpenChange={setItemDeleteDialogOpen}
        onConfirm={handleConfirmItemDelete}
      />
    </div>
  )
}

export default Education
