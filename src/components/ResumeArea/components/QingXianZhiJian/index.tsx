'use client'

import React, { useState, useCallback, useId } from 'react'
import { ReactSortable } from 'react-sortablejs'
import BasicInfo from './components/BasicInfo'
import Education from './components/Education'
import Work from './components/Work'
import Project from './components/Project'
import Research from './components/Research'
import Team from './components/Team'
import Portfolio from './components/Portfolio'
import Other from './components/Other'
import PersonalSummary from './components/PersonalSummary'
import Honors from './components/Honors'
import Skills from './components/Skills'
import CustomModule from './components/CustomModule'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import { useModuleStore, Module } from '@/store/useModuleStore'


// 模块组件映射
const moduleComponents = {
  education: Education,
  work: Work,
  project: Project,
  research: Research,
  team: Team,
  portfolio: Portfolio,
  other: Other,
  personal_summary: PersonalSummary,
  honors: Honors,
  skills: Skills,
}

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
interface QingXianZhiJianProps {
  // 简历组件的props类型
}

const QingXianZhiJian: React.FC<QingXianZhiJianProps> = () => {
  const { module_spacing, line_spacing, paper_style, font_family, font_gray, font_size, page_margin, resume_color } = useResumeStyleStore()
  const { modules, customModules, reorderModules } = useModuleStore()

  // 使用React的useId hook生成唯一ID，避免跨页面key冲突
  const instanceId = useId()

  // 添加一个随机后缀，确保即使useId相同也能区分
  const [uniqueSuffix] = useState(() => Math.random().toString(36).substring(2, 8))

  // 合并 modules 和 customModules，获取除了basic_info和personal_summary之外的可见模块，按index排序
  const visibleModules = [
    // 添加传统的 modules（除了basic_info和personal_summary）
    ...Object.values(modules).filter(module =>
      module.id !== 'basic_info' &&
      module.id !== 'personal_summary' &&
      module.is_visible
    ),
    // 添加新的 customModules，转换为 Module 格式（customModules 中的所有模块都是可见的）
    ...customModules.map(customModule => ({
      id: customModule.id,
      name: customModule.name,
      type: 'custom',
      is_visible: true,
      is_custom: true,
      index: customModule.index
    } as Module))
  ].sort((a, b) => a.index - b.index)

  // 为react-sortablejs准备的数据格式
  const [sortableModules, setSortableModules] = useState(
    visibleModules.map(module => ({ ...module, id: module.id }))
  )

  // 更新sortableModules当modules或customModules变化时
  React.useEffect(() => {
    const newSortableModules = [
      // 添加传统的 modules（除了basic_info和personal_summary）
      ...Object.values(modules).filter(module =>
        module.id !== 'basic_info' &&
        module.id !== 'personal_summary' &&
        module.is_visible
      ),
      // 添加新的 customModules，转换为 Module 格式
      ...customModules.map(customModule => ({
        id: customModule.id,
        name: customModule.name,
        type: 'custom',
        is_visible: true,
        is_custom: true,
        index: customModule.index
      } as Module))
    ].sort((a, b) => a.index - b.index)
      .map(module => ({ ...module, id: module.id }))
    setSortableModules(newSortableModules)
  }, [modules, customModules])

  // 处理拖拽排序
  const handleSort = useCallback((newList: Module[]) => {
    setSortableModules(newList)

    // 更新store中的模块顺序
    // 合并所有可见模块（包括basic_info、personal_summary和customModules），按index排序
    const allVisibleModules = [
      ...Object.values(modules).filter(module => module.is_visible),
      ...customModules.map(customModule => ({
        id: customModule.id,
        name: customModule.name,
        type: 'custom',
        is_visible: true,
        is_custom: true,
        index: customModule.index
      } as Module))
    ].sort((a, b) => a.index - b.index)

    // 找到第一个发生变化的模块（排除basic_info和personal_summary）
    const originalFilteredModules = allVisibleModules.filter(module =>
      module.id !== 'basic_info' && module.id !== 'personal_summary'
    )

    for (let i = 0; i < newList.length && i < originalFilteredModules.length; i++) {
      const newModule = newList[i]
      const originalModule = originalFilteredModules[i]

      if (newModule && originalModule && newModule.id !== originalModule.id) {
        // 找到在所有模块中的索引位置
        const sourceIndex = allVisibleModules.findIndex(m => m.id === newModule.id)
        const targetIndex = allVisibleModules.findIndex(m => m.id === originalModule.id)

        if (sourceIndex !== -1 && targetIndex !== -1) {
          reorderModules(sourceIndex, targetIndex)
          break // 只处理第一个变化，让store处理剩余的更新
        }
      }
    }
  }, [modules, customModules, reorderModules])

  // 渲染模块组件
  const renderModuleComponent = (module: Module) => {
    // 如果是自定义模块，使用CustomModule组件
    if (module.is_custom || module.type === 'custom') {
      return <CustomModule moduleId={module.id} />
    }

    const Component = moduleComponents[module.id as keyof typeof moduleComponents]

    if (!Component) {
      return (
        <div className="w-full py-4 cursor-move transition-all duration-300 hover:bg-purple-50 rounded-lg group"
             style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}>
          <div className="space-y-2">
            <h3 className="text-lg font-semibold text-gray-800">{module.name}</h3>
            <div className="text-gray-600">
              <p>模块 {module.name} 暂未实现</p>
            </div>
          </div>
        </div>
      )
    }

    return <Component />
  }

  // 获取个人总结模块（从modules中直接获取，不从sortableModules中获取）
  const personalSummaryModule = modules.personal_summary && modules.personal_summary.is_visible
    ? modules.personal_summary
    : null

  return (
    <div className="flex relative"
        style={{
          lineHeight: line_spacing,
          fontFamily: font_family,
          color: font_gray,
          fontSize: font_size,
          background: paper_style && paper_style !== '' ? `url(${paper_style}) center top / 100% no-repeat` : undefined
        }}>

      {/* 左侧区域 - 占据1/3宽度 */}
      <div
        className="w-1/3 flex flex-col relative"
        style={{
          gap: module_spacing
        }}
      >
        {/* 左上：基础信息 */}
        <div className='basic'>
          <BasicInfo />
        </div>

        {/* 左下：个人总结 */}
        {personalSummaryModule && (
          <div>
            {renderModuleComponent(personalSummaryModule)}
          </div>
        )}

        {/* 分割线 */}
        <div
          style={{
            position: 'absolute',
            right: 0,
            top: `var(--resume-pageMargin)`,
            height: `calc(100% - var(--resume-pageMargin) * 2)`,
            width: '2px',
            backgroundColor: resume_color
          }}
        />
      </div>

      {/* 右侧区域 - 占据2/3宽度 */}
      <div className="w-2/3 pt-10">
        {/* 可拖拽排序的其他模块 */}
        <ReactSortable
          list={sortableModules}
          setList={handleSort}
          animation={200}
          delay={2}
          ghostClass="opacity-50"
          chosenClass="shadow-lg"
          dragClass="rotate-2"
          group="modules"
          className="flex flex-col"
          style={{ gap: module_spacing }}
        >
          {sortableModules.map((module, index) => (
            <div key={`${module.id}-${instanceId}-${uniqueSuffix}-${index}`}>
              {renderModuleComponent(module)}
            </div>
          ))}
        </ReactSortable>
      </div>
    </div>
  )
}

export default QingXianZhiJian
