'use client'

import React, { useMemo } from 'react'
import { useModuleStore, type PersonalSummaryItem } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import ResumeTitle from '@/components/Title/ResumeTitle'
import MarkdownViewer from '@/components/MarkdownViewer'
import PersonalIcon from '@/assets/svg/personal-icon.svg'

const PersonalSummary: React.FC = () => {
  const { modules, setActiveModule } = useModuleStore()
  const { page_margin, font_size, resume_color } = useResumeStyleStore()

  // 计算标题字体大小（比基础字体大2px）
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const titleFontSize = `${baseFontSize + 2}px`

  // 获取个人总结数据
  const personalSummaryModule = modules['personal_summary']
  const personalSummaryItem = useMemo(() =>
    (personalSummaryModule?.item as PersonalSummaryItem) || { summary: { label: '', value: '' } },
    [personalSummaryModule?.item]
  )

  // 处理点击事件，切换到个人总结模块
  const handleClick = () => {
    setActiveModule('personal_summary')
  }

  // 如果没有个人总结数据或不可见，不渲染
  if (!personalSummaryModule?.is_visible || !personalSummaryItem.summary?.value) {
    return null
  }

  return (
    <div
      className="w-full cursor-pointer transition-all duration-300 hover:bg-purple-50 rounded-lg"
      style={{ paddingLeft: `calc(${page_margin} + 12px)`, paddingRight: `calc(${page_margin} + 12px)` }}
      onClick={handleClick}
    >
      <div className="space-y-4">
        {/* 模块标题 */}
        <ResumeTitle
          title={personalSummaryModule?.name || '个人总结'}
          fontSize={titleFontSize}
          lineColor="rgb(210, 210, 210)"
          icon={PersonalIcon}
        />

        {/* 个人总结内容 */}
        <div className="leading-relaxed">
          <MarkdownViewer
            content={personalSummaryItem.summary?.value || ''}
            className="leading-relaxed"
          />
        </div>
      </div>
    </div>
  )
}

export default PersonalSummary
