'use client'

import React, { useState } from 'react'
import Image from 'next/image'
import { useModuleStore } from '@/store/useModuleStore'
import { useResumeStyleStore } from '@/store/useResumeStyleStore'
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { toast } from 'sonner'

const Slogan: React.FC = () => {
  const { slogan, updateSlogan, saveResume } = useModuleStore()
  const { line_spacing, resume_color2, font_size } = useResumeStyleStore()
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [editSlogan, setEditSlogan] = useState('')
  const [isClosing, setIsClosing] = useState(false)

  // 如果没有slogan数据，返回null
  if (!slogan) {
    return null
  }

  const { slogan: sloganText } = slogan

  // 计算字体大小：基础字体大小+4px
  const baseFontSize = parseInt(font_size.replace('px', ''))
  const sloganFontSize = `${baseFontSize + 4}px`

  // 打开编辑弹窗
  const handleEditClick = () => {
    setEditSlogan(sloganText?.value || '在追求中发现可能，在创造中实现价值')
    setIsDialogOpen(true)
  }

  // 保存编辑
  const handleSave = async () => {
    try {
      // 更新store中的slogan数据
      updateSlogan({
        title: slogan.title, // 保持原有标题不变
        slogan: { label: 'Slogan', value: editSlogan }
      })

      // 调用保存简历接口
      await saveResume()

      setIsDialogOpen(false)
      toast.success('保存成功', {
        position: 'top-center'
      })
    } catch (error) {
      toast.error('保存失败，请重试', {
        position: 'top-center'
      })
    }
  }



  return (
    <div
      className="inline-block text-white relative overflow-hidden cursor-pointer group"
      style={{ height: '6mm', backgroundColor: resume_color2, padding: '0 20px' }}
      onClick={() => {
        // 如果弹窗已经打开或正在关闭，不要重复触发
        if (!isDialogOpen && !isClosing) {
          handleEditClick()
        }
      }}
    >

      {/* 内容区域 */}
      <div className="relative z-10 text-center px-6 flex flex-col items-center justify-center h-full">
        {/* Slogan文本 */}
        {sloganText?.value && (
          <p
            className="font-bold text-white opacity-90"
            style={{
              lineHeight: line_spacing,
              fontSize: sloganFontSize
            }}
          >
            {sloganText.value}
          </p>
        )}
      </div>



      {/* Hover蒙版和编辑图标 */}
      <div
        className="absolute inset-0 bg-black bg-opacity-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center cursor-pointer"
        onClick={(e) => {
          e.stopPropagation()
          if (!isDialogOpen && !isClosing) {
            handleEditClick()
          }
        }}
      >
        <Image
          src="/resume/edit-icon.svg"
          alt="编辑"
          width={32}
          height={32}
          className="text-white opacity-100 brightness-200"
        />
      </div>

      {/* 编辑弹窗 */}
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent
          className="sm:max-w-md p-6"
          onEscapeKeyDown={() => setIsDialogOpen(false)}
          onPointerDownOutside={(e) => {
            e.preventDefault()
            setIsClosing(true)
            setIsDialogOpen(false)
            // 延迟重置关闭状态，防止立即重新打开
            setTimeout(() => {
              setIsClosing(false)
            }, 100)
          }}
        >
          <DialogHeader className="pb-4">
            <DialogTitle className="text-lg font-medium">Slogan设置</DialogTitle>
          </DialogHeader>

          <div className="space-y-4">
            {/* Slogan输入 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                写一句你的Slogan:
              </label>
              <Input
                value={editSlogan}
                onChange={(e) => setEditSlogan(e.target.value)}
                placeholder="在追求中发现可能，在创造中实现价值"
                className="w-full"
              />
            </div>
          </div>

          <div className="flex justify-center pt-4">
            <button
              onClick={handleSave}
              className="px-24 py-2 text-sm text-white rounded-md transition-colors cursor-pointer"
              style={{ backgroundColor: 'var(--primary)' }}
            >
              确定
            </button>
          </div>
        </DialogContent>
      </Dialog>

    </div>
  )
}

export default Slogan
