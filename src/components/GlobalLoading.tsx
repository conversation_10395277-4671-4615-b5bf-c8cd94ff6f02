'use client';

import React from 'react';

interface GlobalLoadingProps {
  isLoading: boolean;
  message?: string;
}

/**
 * 全局 Loading 组件
 * 显示在页面最顶层的 loading 遮罩
 */
export default function GlobalLoading({ isLoading, message = '正在加载...' }: GlobalLoadingProps) {
  if (!isLoading) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center bg-white">
      <div className="flex flex-col items-center space-y-6">
        {/* Loading 动画 */}
        <div className="relative">
          {/* 外圈 */}
          <div className="w-20 h-20 border-4 border-gray-200 rounded-full"></div>
          {/* 内圈 - 旋转动画 */}
          <div className="absolute top-0 left-0 w-20 h-20 border-4 border-transparent border-t-primary rounded-full animate-spin"></div>
        </div>

        {/* Loading 文字 */}
        <div className="text-gray-700 text-xl font-medium">
          {message}
        </div>

        {/* 进度点动画 */}
        <div className="flex space-x-2">
          <div className="w-3 h-3 bg-primary rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-3 h-3 bg-primary rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-3 h-3 bg-primary rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
      </div>
    </div>
  );
}
