'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { X, Check, Crown, Loader2, Download, ArrowRight } from 'lucide-react';
import { UserType } from '@/api/client/types/user';
import { MembershipPlanResponse } from '@/api/client/types/membership';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useUserStore } from '@/store/useUserStore';
import { useMembershipStore } from '@/store/useMembershipStore';
import { toast } from 'sonner';
import { animated } from '@react-spring/web';
import QRCode from 'react-qr-code';
import { orderApi } from '@/api/client';
import { PaymentStatus } from '@/api/client/types/order';

// 生成随机通知数据的函数
const generateRandomNotifications = (plans: MembershipPlanResponse[]) => {
  // 生成10条随机通知
  return Array.from({ length: 10 }, () => {
    // 随机分钟数 (1-30分钟)
    const minutes = Math.floor(Math.random() * 30) + 1;

    // 随机用户ID (1000-9999)
    const userId = Math.floor(Math.random() * 9000) + 1000;

    // 随机选择一个套餐名称
    const planName = plans[Math.floor(Math.random() * plans.length)].name;

    return {
      time: `${minutes}分钟前`,
      user: `用户${userId}`,
      plan: planName,
      action: '支付成功'
    };
  });
};

interface MembershipDialogProps {
  isOpen: boolean;
  onClose: () => void;
  // 下载券相关功能暂时注释掉
  // onCloseWithDownloadCoupon?: () => void;
  // onSwitchToDownloadCoupon?: () => void;
}



// 通知数据类型
interface NotificationItem {
  time: string;
  user: string;
  plan: string;
  action: string;
}

export default function MembershipDialog({ isOpen, onClose }: MembershipDialogProps) {
  const [currentNotification, setCurrentNotification] = useState(0);
  // 通知数据状态
  const [notificationData, setNotificationData] = useState<NotificationItem[]>([
    { time: '刚刚', user: '用户1234', plan: '简历会员', action: '支付成功' }
  ]);


  // 支付相关状态
  const [paymentMethod, setPaymentMethod] = useState<'wechat' | 'alipay'>('wechat');
  const [orderNo, setOrderNo] = useState<string>('');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [isPolling, setIsPolling] = useState(false);

  // 轮询定时器引用
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 从 store 中获取用户信息
  const { id, username, avatar, user_type, getGuestUserInfo } = useUserStore();

  // 从 store 中获取会员套餐信息
  const {
    plans,
    selectedPlanId,
    selectPlan,
    getSelectedPlan,
    loading,
    error
  } = useMembershipStore();

  // 生成随机通知数据
  useEffect(() => {
    if (isOpen && plans.length > 0) {
      setNotificationData(generateRandomNotifications(plans));
    }
  }, [isOpen, plans]);

  // 弹窗打开时默认选中微信支付并创建订单
  useEffect(() => {
    if (isOpen && selectedPlanId && !orderNo && !isCreatingOrder) {
      // 默认创建微信支付订单
      createOrder('wechat');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, selectedPlanId, orderNo, isCreatingOrder]);

  // 通知轮播效果
  useEffect(() => {
    if (notificationData.length === 0) return;

    const interval = setInterval(() => {
      setCurrentNotification((prev) => (prev + 1) % notificationData.length);
    }, 3000); // 每3秒切换一次

    return () => clearInterval(interval);
  }, [notificationData.length]);



  // 清理轮询定时器
  useEffect(() => {
    // 当弹窗关闭时，重置状态
    if (!isOpen) {
      setPaymentMethod('wechat');
      setOrderNo('');
      setQrCodeUrl('');
      setPaymentStatus(null);
      setIsCreatingOrder(false);
      setIsPolling(false);

      // 清理轮询定时器
      if (pollingTimerRef.current) {
        clearInterval(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }
    }

    // 当组件卸载时，清理轮询定时器
    return () => {
      if (pollingTimerRef.current) {
        clearInterval(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }
    };
  }, [isOpen]);

  const handlePlanSelect = (planId: number) => {
    selectPlan(planId);

    // 选择套餐后，根据当前支付方式重新创建订单
    if (planId) {
      createOrder(paymentMethod);
    }
  };

  // 创建订单并开始轮询状态
  const createOrder = async (method: 'wechat' | 'alipay') => {
    // 获取当前选中的套餐
    const currentPlan = getSelectedPlan();

    if (!currentPlan || !currentPlan.id) {
      toast.error('未找到选中的套餐信息，请重新选择', {
        position: 'top-center',
        style: {
          color: 'white',
          backgroundColor: 'var(--destructive)',
          border: '1px solid var(--destructive)'
        }
      });
      return;
    }

    try {
      setIsCreatingOrder(true);
      setPaymentMethod(method);

      // 清除之前的轮询
      if (pollingTimerRef.current) {
        clearInterval(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }

      // 使用当前选中套餐的ID创建订单，而不是使用selectedPlanId

      // 从 localStorage 获取百度投放ID
      const bdVid = localStorage.getItem('bd_vid') || '';

      // 创建订单
      const orderData = method === 'wechat'
        ? await orderApi.createWechatPayOrder(currentPlan.id, bdVid)
        : await orderApi.createAlipayOrder(currentPlan.id, bdVid);

      // 保存订单信息
      setOrderNo(orderData.order_no);
      setQrCodeUrl(orderData.code_url);
      setPaymentStatus(PaymentStatus.Pending);

      // 开始轮询订单状态
      startPollingOrderStatus(orderData.order_no);
    } catch (error) {
      // 显示错误提示
      const errorMsg = error instanceof Error ? error.message : '创建订单失败，请稍后重试';
      toast.error(errorMsg, {
        position: 'top-center',
        style: {
          color: 'white',
          backgroundColor: 'var(--destructive)',
          border: '1px solid var(--destructive)'
        }
      });

      // 设置错误状态
      setPaymentStatus(null);
    } finally {
      setIsCreatingOrder(false);
    }
  };

  // 开始轮询订单状态
  const startPollingOrderStatus = (orderNo: string) => {
    setIsPolling(true);

    // 每2秒查询一次订单状态
    pollingTimerRef.current = setInterval(async () => {
      try {
        const statusData = await orderApi.queryOrderStatus(orderNo);
        setPaymentStatus(statusData.payment_status);

        // 根据支付状态处理
        if (statusData.payment_status === PaymentStatus.Success) {
          // 支付成功，停止轮询
          if (pollingTimerRef.current) {
            clearInterval(pollingTimerRef.current);
            pollingTimerRef.current = null;
          }
          setIsPolling(false);

          // 必应转化跟踪
          if (statusData.channel_name === 'Bing' && typeof window !== 'undefined' && (window as any).uetq) {
            (window as any).uetq.push('event', 'purchase', {
              "revenue_value": statusData.amount,
              "currency": "CNY"
            });
          }

          // 刷新用户信息
          try {
            await getGuestUserInfo();
          } catch (error) {
          }

          // 显示成功提示并关闭弹窗
          toast.success('支付成功！', {
            position: 'top-center'
          });
          onClose();
        } else if (
          statusData.payment_status === PaymentStatus.Failed ||
          statusData.payment_status === PaymentStatus.Timeout
        ) {
          // 支付失败或超时，停止轮询
          if (pollingTimerRef.current) {
            clearInterval(pollingTimerRef.current);
            pollingTimerRef.current = null;
          }
          setIsPolling(false);

          // 显示错误信息
          const failReason = statusData.fail_reason || '支付失败，请重试';
          toast.error(failReason, {
            position: 'top-center',
            style: {
              color: 'white',
              backgroundColor: 'var(--destructive)',
              border: '1px solid var(--destructive)'
            }
          });
        }
        // 待支付和处理中状态继续轮询
      } catch (error) {
        // 显示错误提示
        const errorMsg = error instanceof Error ? error.message : '查询订单状态失败，请刷新页面重试';
        toast.error(errorMsg, {
          position: 'top-center',
          style: {
            color: 'white',
            backgroundColor: 'var(--destructive)',
            border: '1px solid var(--destructive)'
          }
        });
      }
    }, 2000);
  };



  // 处理关闭弹窗
  const handleClose = () => {
    // 下载券相关功能暂时注释掉
    // if (onCloseWithDownloadCoupon) {
    //   onCloseWithDownloadCoupon();
    // } else {
      onClose();
    // }
  };

  // 获取选中的套餐信息
  const selectedPlanData = getSelectedPlan();

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        // 只有当用户点击关闭按钮时才会触发关闭
        if (!open) {
          handleClose();
        }
      }}
      modal={true} // 确保弹窗是模态的
    >
      <DialogContent
        className="!w-[1300px] !max-w-[1300px] !p-0 !gap-0 overflow-hidden max-h-[90vh] [&>button]:hidden !border-0 !shadow-xl"
        onEscapeKeyDown={(e) => e.preventDefault()} // 禁用ESC键关闭
        onPointerDownOutside={(e) => e.preventDefault()} // 禁用点击背景关闭
        onInteractOutside={(e) => e.preventDefault()} // 禁用外部交互关闭
        onClick={(e) => e.stopPropagation()} // 阻止事件冒泡
      >
        <DialogTitle className="sr-only" style={{display:"none"}}>会员充值</DialogTitle>

        {/* 顶部栏 */}
        <div className="text-white p-3 relative bg-cover bg-center" style={{ backgroundImage: 'url(/home/<USER>' }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                <Image
                  src={avatar || 'https://cdn.shineresume.com/avatar/6.svg'}
                  alt="用户头像"
                  width={28}
                  height={28}
                  className="rounded-full"
                />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <p className="text-sm font-medium">{username || '游客用户'}</p>
                  {user_type === UserType.Member && (
                    <span className="flex items-center gap-1 bg-yellow-100 text-yellow-500 text-xs px-2 py-1 rounded">
                      <Crown className="w-3 h-3" />
                      <span>会员</span>
                    </span>
                  )}
                </div>
                <p className="text-xs opacity-80">用户ID: {id || '未登录'}</p>
              </div>
            </div>

            <button
              onClick={(e) => {
                e.stopPropagation();
                handleClose();
              }}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <X className="h-5 w-5 stroke-[1.5]" />
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex bg-white">
          <div className="w-[950px] p-6 overflow-y-auto max-h-[calc(90vh-56px)]">
            {/* 会员权益一览和套餐 */}
            <div className="grid grid-cols-4 gap-4 items-center">
              {/* 会员权益一览 */}
              <div className="col-span-1 flex justify-center">
                <div className="flex flex-col items-center text-center">
                  <div className="flex items-center gap-2 mb-2">
                    <Crown className="w-5 h-5 text-[#824dfc]" />
                    <h3 className="text-lg font-bold bg-gradient-to-r from-[#824dfc] to-blue-500 bg-clip-text text-transparent">
                      会员权益一览
                    </h3>
                  </div>
                  <div className="text-xs text-gray-600 whitespace-nowrap">解锁全部AI功能，让简历制作高效</div>
                </div>
              </div>

              {/* 套餐选择 */}
              <div className="col-span-3">
                <div className="grid grid-cols-4 gap-4">
                {loading ? (
                  // 加载状态
                  <div className="col-span-4 flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-2 text-gray-600">加载中...</span>
                  </div>
                ) : error ? (
                  // 错误状态
                  <div className="col-span-4 text-center py-8">
                    <p className="text-red-500 mb-2">{error}</p>
                    <button
                      className="px-4 py-2 bg-primary text-white rounded-md hover:bg-purple-600"
                      onClick={() => onClose()}
                    >
                      关闭
                    </button>
                  </div>
                ) : plans.length === 0 ? (
                  // 空状态
                  <div className="col-span-4 text-center py-8">
                    <p className="text-gray-500">暂无可用套餐</p>
                  </div>
                ) : (
                  // 正常显示套餐列表
                  plans.map((plan) => (
                    <div
                      key={plan.id}
                      className={cn(
                        "bg-white border-2 rounded-lg px-4 py-1 cursor-pointer transition-all relative",
                        selectedPlanId === plan.id
                          ? "border-[#824dfc] shadow-lg"
                          : "border-gray-200 hover:border-purple-300"
                      )}
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePlanSelect(plan.id);
                      }}
                    >
                      {/* 角标 */}
                      {plan.corner_image_url && (
                        <div className="absolute -top-4 -right-1">
                          <img
                            src={plan.corner_image_url}
                            alt="标签"
                            className="h-5 w-auto"
                          />
                        </div>
                      )}

                      {/* 套餐名称 */}
                      <div className="text-center mb-1">
                        <h4 className="text-base font-bold text-[#824dfc]">{plan.name}</h4>
                        <div className="text-xs text-gray-500 mt-1">
                          {plan.name.includes('7天') || plan.name.includes('体验') ? '（7天）' :
                           plan.name.includes('月') ? '（30天）' :
                           plan.name.includes('年') ? '（365天）' :
                           plan.name.includes('终身') ? '（永久）' : ''}
                        </div>
                      </div>

                      {/* 价格 */}
                      <div className="text-center ">
                        <div className="flex items-baseline justify-center gap-1">
                          <span className="text-xl font-bold">
                            <span className="text-xs text-gray-400">¥</span><span className="text-2xl text-black">{plan.actual_price.toFixed(1)}</span>
                          </span>
                          <span className="text-sm text-gray-400 line-through">
                            <span className="text-xs">¥</span>{plan.original_price.toFixed(0)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))
                )}
                </div>
              </div>
            </div>

            {/* 功能对比表格 */}
            <div className="mt-3">
              {/* 表格行 */}
              <div className="grid grid-cols-5 border-b border-gray-100">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>简历修改、下载</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-1`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-[#824dfc]">∞不限</span>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-5 border-b border-gray-100">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>简历创建份数</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-2`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-gray-800">
                      {plan.resume_limit === 0 || plan.resume_limit > 100000 ? (
                        <span className="text-[#824dfc]">∞不限</span>
                      ) : (
                        `${plan.resume_limit}份`
                      )}
                    </span>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-5 border-b border-gray-100">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>AI生成</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-3`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-gray-800">
                      {plan.ai_generate_limit === 0 || plan.ai_generate_limit > 100000 ? (
                        <span className="text-[#824dfc]">∞不限</span>
                      ) : (
                        `${plan.ai_generate_limit}次`
                      )}
                    </span>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-5 border-b border-gray-100">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>AI改写</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-4`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-gray-800">
                      {plan.ai_rewrite_limit === 0 || plan.ai_rewrite_limit > 100000 ? (
                        <span className="text-[#824dfc]">∞不限</span>
                      ) : (
                        `${plan.ai_rewrite_limit}次`
                      )}
                    </span>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-5 border-b border-gray-100">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>AI简历优化</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-5`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-gray-800">
                      {plan.ai_optimize_limit === 0 || plan.ai_optimize_limit > 100000 ? (
                        <span className="text-[#824dfc]">∞不限</span>
                      ) : (
                        `${plan.ai_optimize_limit}次`
                      )}
                    </span>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-5 border-b border-gray-100">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>AI简历诊断</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-6`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-gray-800">
                      {plan.ai_diagnose_limit === 0 || plan.ai_diagnose_limit > 100000 ? (
                        <span className="text-[#824dfc]">∞不限</span>
                      ) : (
                        `${plan.ai_diagnose_limit}次`
                      )}
                    </span>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-5 border-b border-gray-100">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>AI一键生成简历</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-7`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-gray-800">
                      {plan.ai_one_click_limit === 0 || plan.ai_one_click_limit > 100000 ? (
                        <span className="text-[#824dfc]">∞不限</span>
                      ) : (
                        `${plan.ai_one_click_limit}次`
                      )}
                    </span>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-5 border-b border-gray-100">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>一键更换模板</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-8`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-[#824dfc]">∞不限</span>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-5 border-b border-gray-100">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>简历发送至邮箱</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-9`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-[#824dfc]">∞不限</span>
                  </div>
                ))}
              </div>

              <div className="grid grid-cols-5">
                <div className="px-4 py-[14px] flex items-center gap-2 text-sm font-semibold text-gray-700">
                  <Check className="w-4 h-4 text-green-500" />
                  <span>1v1专属客服</span>
                </div>
                {plans.length > 0 && plans.slice(0, 4).map((plan) => (
                  <div key={`${plan.id}-10`} className="px-4 py-[14px] text-sm font-semibold text-center">
                    <span className="text-gray-800">优先支持</span>
                  </div>
                ))}
              </div>
            </div>

            {/* 下载券推荐区域 - 暂时注释掉 */}
            {/* {onSwitchToDownloadCoupon && (
              <div className="mt-4 relative overflow-hidden">
                <div className="absolute inset-0 bg-gradient-to-r from-blue-50 via-indigo-50 to-purple-50 rounded-xl"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-blue-100/30 via-indigo-100/30 to-purple-100/30 rounded-xl animate-pulse"></div>

                <div className="relative bg-white/90 backdrop-blur-sm rounded-xl p-3 border-2 border-dashed border-blue-300 shadow-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div className="w-10 h-10 bg-gradient-to-r from-blue-400 via-indigo-500 to-purple-500 rounded-full flex items-center justify-center shadow-lg">
                          <Download className="w-5 h-5 text-white" />
                        </div>
                        <div className="absolute inset-0 bg-gradient-to-r from-blue-400 via-indigo-500 to-purple-500 rounded-full animate-ping opacity-20"></div>
                      </div>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="text-sm font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            💡 功能太多用不完？
                          </h5>
                          <span className="text-xs bg-blue-500 text-white px-2 py-0.5 rounded-full font-bold">
                            省钱
                          </span>
                        </div>
                        <p className="text-xs text-gray-600 font-medium">
                          下载券按需付费，只为下载而生！
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-blue-600 font-semibold">✓ 按需付费</span>
                          <span className="text-xs text-green-600 font-semibold">✓ 永不过期</span>
                          <span className="text-xs text-purple-600 font-semibold">✓ 简单直接</span>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onSwitchToDownloadCoupon();
                      }}
                      className="group flex items-center gap-2 px-4 py-2 bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 text-white text-sm font-bold rounded-full hover:from-blue-600 hover:via-indigo-600 hover:to-purple-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 hover:-translate-y-0.5"
                    >
                      <span>试试下载券</span>
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
                    </button>
                  </div>

                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white text-xs font-bold animate-bounce shadow-lg">
                    💰
                  </div>
                  <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-gradient-to-r from-indigo-400 to-blue-400 rounded-full opacity-80 animate-pulse"></div>

                  <div className="absolute top-2 right-8 w-2 h-2 bg-white rounded-full animate-ping"></div>
                  <div className="absolute bottom-3 left-8 w-1.5 h-1.5 bg-blue-300 rounded-full animate-ping delay-500"></div>
                </div>
              </div>
            )} */}

          </div>

          {/* 订单详情 */}
          <div className="w-[350px] p-4 overflow-y-auto max-h-[calc(90vh-56px)]">
            <div className="bg-white rounded-lg px-5 py-3 border border-gray-200">
              {/* 订单详情标题 */}
              <div className="mb-2">
                <h3 className="text-lg font-bold text-gray-800 mb-2">订单详情</h3>
              </div>

              {/* 价格信息 */}
              <div className="mb-2 px-4 py-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-base font-medium">{selectedPlanData ? selectedPlanData.name : '月度会员'}</span>
                  <span className="text-2xl font-bold text-[#824dfc]">
                    ¥{selectedPlanData ? selectedPlanData.actual_price.toFixed(1) : '29.9'}
                  </span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">原价</span>
                  <span className="text-sm text-gray-400 line-through">
                    ¥{selectedPlanData ? selectedPlanData.original_price.toFixed(0) : '99'}
                  </span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">优惠</span>
                  <span className="text-sm text-green-600">
                    -¥{selectedPlanData
                      ? (selectedPlanData.original_price - selectedPlanData.actual_price).toFixed(1)
                      : '9.1'}
                  </span>
                </div>
              </div>

              {/* 支付方式 */}
              <div className="mb-3">
                <h4 className="text-sm font-medium text-gray-800 mb-3">支付方式</h4>
                <div className="space-y-2">
                  <button
                    className={cn(
                      "w-full flex items-center gap-3 p-3 rounded-lg border-2 transition-all",
                      paymentMethod === 'wechat'
                        ? "border-green-500 bg-green-50"
                        : "border-gray-200 hover:border-gray-300"
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      createOrder('wechat');
                    }}
                    disabled={isCreatingOrder}
                  >
                    <Image
                      src="/home/<USER>"
                      alt="微信支付"
                      width={24}
                      height={24}
                    />
                    <span className="flex-1 text-left">微信支付</span>
                    {paymentMethod === 'wechat' && (
                      <div className="w-4 h-4 rounded-full bg-green-500 flex items-center justify-center">
                        <Check className="w-2.5 h-2.5 text-white" />
                      </div>
                    )}
                  </button>

                  <button
                    className={cn(
                      "w-full flex items-center gap-3 p-3 rounded-lg border-2 transition-all",
                      paymentMethod === 'alipay'
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      createOrder('alipay');
                    }}
                    disabled={isCreatingOrder}
                  >
                    <Image
                      src="/home/<USER>"
                      alt="支付宝支付"
                      width={24}
                      height={24}
                    />
                    <span className="flex-1 text-left">支付宝支付</span>
                    {paymentMethod === 'alipay' && (
                      <div className="w-4 h-4 rounded-full bg-blue-500 flex items-center justify-center">
                        <Check className="w-2.5 h-2.5 text-white" />
                      </div>
                    )}
                  </button>
                </div>
              </div>

              {/* 二维码 */}
              <div className="mb-3">
                <div className="flex flex-col items-center relative">
                  <div className="w-40 h-40 border border-gray-200 rounded-lg flex items-center justify-center relative overflow-hidden">
                    {isCreatingOrder ? (
                      <div className="flex flex-col items-center">
                        <Loader2 className="w-8 h-8 animate-spin text-primary mb-2" />
                        <span className="text-xs text-gray-500">创建订单中...</span>
                      </div>
                    ) : qrCodeUrl ? (
                      <div className="w-36 h-36 flex items-center justify-center">
                        <QRCode
                          value={qrCodeUrl}
                          size={200}
                          style={{ height: "200px", width: "200px", display: "block" }}
                          viewBox={`0 0 256 256`}
                        />
                      </div>
                    ) : (
                      <span className="text-xs text-gray-500">请选择支付方式</span>
                    )}

                    {/* 支付状态覆盖层 */}
                    {isPolling && paymentStatus === PaymentStatus.Processing && (
                      <div className="absolute inset-0 bg-black bg-opacity-10 flex items-center justify-center rounded-lg">
                        <div className="bg-white p-2 rounded-md shadow-md text-xs flex flex-col items-center">
                          <Loader2 className="w-4 h-4 animate-spin text-primary mb-1" />
                          <span>处理中...</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-xs text-gray-600 text-center mt-1">
                  {paymentStatus === PaymentStatus.Pending && qrCodeUrl ? '扫码支付' :
                   paymentStatus === PaymentStatus.Processing ? '支付处理中...' :
                   paymentStatus === PaymentStatus.Success ? '支付成功' :
                   paymentStatus === PaymentStatus.Failed ? '支付失败' :
                   paymentStatus === PaymentStatus.Timeout ? '支付超时' :
                   '扫码支付'}
                </div>
              </div>

              {/* 通知 */}
              <div className="space-y-2">
                <div className="bg-blue-50 text-blue-700 text-xs px-3 py-2 rounded flex items-center">
                  <Image src="/home/<USER>" alt="公告" width={14} height={14} className="mr-2 flex-shrink-0" />
                  <div className="relative overflow-hidden flex-1" style={{ height: '16px' }}>
                    {notificationData.map((notification: NotificationItem, i: number) => (
                      <animated.div
                        key={i}
                        className="absolute w-full whitespace-nowrap"
                        style={{
                          opacity: i === currentNotification ? 1 : 0,
                          transform: i === currentNotification
                            ? 'translateY(0px)'
                            : i < currentNotification
                              ? 'translateY(-16px)'
                              : 'translateY(16px)',
                          transition: 'opacity 0.5s ease, transform 0.5s ease'
                        }}
                      >
                        {notification.time} {notification.user}
                        购买{notification.plan}，
                        {notification.action}！
                      </animated.div>
                    ))}
                  </div>
                </div>
              </div>

              {/* 协议 */}
              <div className="mt-2 text-center text-xs text-gray-500">
                <label className="flex items-center justify-center gap-2 ">
                  <input type="checkbox" className="w-3 h-3" defaultChecked />
                  <span>支付即同意
                    <a href="/value-added-services" target="_blank" rel="noopener noreferrer" className="text-[#824dfc] hover:underline">《会员服务协议》</a>
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
