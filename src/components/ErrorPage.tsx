'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

interface ErrorPageProps {
  title?: string;
  message?: string;
  redirectDelay?: number;
  redirectTo?: string;
}

export default function ErrorPage({
  title = '页面出错了',
  message = '抱歉，页面加载失败，即将跳转到首页...',
  redirectDelay = 1000,
  redirectTo = '/'
}: ErrorPageProps) {
  const router = useRouter();

  useEffect(() => {
    const timer = setTimeout(() => {
      router.push(redirectTo);
    }, redirectDelay);

    return () => clearTimeout(timer);
  }, [router, redirectDelay, redirectTo]);

  return (
    <div className="min-h-screen bg-[#f7f7fc] flex items-center justify-center">
      <div className="bg-white rounded-lg shadow-lg p-8 max-w-md w-full mx-4">
        <div className="text-center">
          {/* 错误图标 */}
          <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
            <svg
              className="h-8 w-8 text-red-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
              />
            </svg>
          </div>

          {/* 错误标题 */}
          <h1 className="text-xl font-semibold text-gray-900 mb-2">
            {title}
          </h1>

          {/* 错误信息 */}
          <p className="text-gray-600 mb-6">
            {message}
          </p>

          {/* 加载动画 */}
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#824dfc]"></div>
            <span className="ml-2 text-sm text-gray-500">
              {Math.ceil(redirectDelay / 1000)} 秒后自动跳转...
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
