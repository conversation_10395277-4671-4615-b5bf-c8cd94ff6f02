/* MarkdownEditor 容器样式优化 */

/* 确保编辑器容器不会超出父容器 */
.toastui-editor-defaultUI {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* 确保编辑器主体区域不会超出 */
.toastui-editor-main {
  width: 100% !important;
  max-width: 100% !important;
  overflow: hidden !important;
}

/* 确保编辑器内容区域不会超出 */
.toastui-editor-contents,
.toastui-editor-md-container,
.toastui-editor-ww-container {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
}

/* 确保工具栏不会超出 */
.toastui-editor-toolbar {
  width: 100% !important;
  max-width: 100% !important;
  box-sizing: border-box !important;
  flex-wrap: wrap !important;
}

/* 确保编辑器在ScrollableContent内部正确显示 */
.scrollable-content .toastui-editor-defaultUI {
  position: relative !important;
  z-index: 1 !important;
}

/* 防止编辑器弹出层超出容器 */
.toastui-editor-popup,
.toastui-editor-dropdown {
  z-index: 9999 !important;
}

/* 确保编辑器高度固定，不会动态扩展 */
.toastui-editor-defaultUI-toolbar + .toastui-editor-main {
  height: calc(100% - 40px) !important;
  max-height: calc(100% - 40px) !important;
  overflow-y: auto !important;
}

/* 优化编辑器在小屏幕上的显示 */
@media (max-width: 768px) {
  .toastui-editor-toolbar {
    padding: 4px !important;
  }
  
  .toastui-editor-toolbar-group {
    margin-right: 4px !important;
  }
}

/* 确保编辑器内容不会水平滚动 */
.toastui-editor-contents {
  word-wrap: break-word !important;
  word-break: break-word !important;
  overflow-wrap: break-word !important;
}

/* 优化编辑器边框和圆角 */
.toastui-editor-defaultUI {
  border-radius: 6px !important;
  border: 1px solid #e2e8f0 !important;
}

/* 确保编辑器在父容器中居中对齐 */
.toastui-editor-defaultUI {
  margin: 0 auto !important;
}
