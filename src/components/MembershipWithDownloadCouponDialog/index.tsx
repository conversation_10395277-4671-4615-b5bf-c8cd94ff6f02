'use client';

import React, { useState } from 'react';
import MembershipDialog from '@/components/MembershipDialog';
import DownloadCouponDialog from '@/components/DownloadCouponDialog';

interface MembershipWithDownloadCouponDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

/**
 * 会员弹窗与下载券弹窗的组合组件
 * 当会员弹窗关闭时，会自动弹出下载券弹窗
 */
export default function MembershipWithDownloadCouponDialog({ 
  isOpen, 
  onClose 
}: MembershipWithDownloadCouponDialogProps) {
  const [showMembershipDialog, setShowMembershipDialog] = useState(false);
  const [showDownloadCouponDialog, setShowDownloadCouponDialog] = useState(false);

  // 当外部传入的 isOpen 变化时，控制会员弹窗的显示
  React.useEffect(() => {
    if (isOpen) {
      setShowMembershipDialog(true);
      setShowDownloadCouponDialog(false);
    } else {
      setShowMembershipDialog(false);
      setShowDownloadCouponDialog(false);
    }
  }, [isOpen]);

  // 处理会员弹窗关闭
  const handleMembershipClose = () => {
    setShowMembershipDialog(false);
    onClose();
  };

  // 处理会员弹窗关闭并弹出下载券弹窗（已禁用，直接关闭）
  const handleMembershipCloseWithDownloadCoupon = () => {
    setShowMembershipDialog(false);
    onClose(); // 直接关闭，不再弹出下载券弹窗
  };

  // 处理下载券弹窗关闭
  const handleDownloadCouponClose = () => {
    setShowDownloadCouponDialog(false);
    onClose();
  };

  // 处理从下载券弹窗切换到会员弹窗
  const handleSwitchToMembership = () => {
    setShowDownloadCouponDialog(false);
    setShowMembershipDialog(true);
  };

  // 处理从会员弹窗切换到下载券弹窗
  const handleSwitchToDownloadCoupon = () => {
    setShowMembershipDialog(false);
    setShowDownloadCouponDialog(true);
  };

  return (
    <>
      <MembershipDialog
        isOpen={showMembershipDialog}
        onClose={handleMembershipClose}
        // 下载券相关参数暂时注释掉
        // onCloseWithDownloadCoupon={handleMembershipCloseWithDownloadCoupon}
        // onSwitchToDownloadCoupon={handleSwitchToDownloadCoupon}
      />
      <DownloadCouponDialog
        isOpen={showDownloadCouponDialog}
        onClose={handleDownloadCouponClose}
        onSwitchToMembership={handleSwitchToMembership}
      />
    </>
  );
}
