'use client';

import React from 'react';
import ReactMarkdown from 'react-markdown';
import { useResumeStyleStore } from '@/store/useResumeStyleStore';

interface SimpleMarkdownViewerProps {
  content: string;
  className?: string;
  style?: React.CSSProperties;
  removePadding?: boolean;
  inheritLineHeight?: boolean;
}

const SimpleMarkdownViewer: React.FC<SimpleMarkdownViewerProps> = ({
  content,
  className = '',
  style = {},
  removePadding = true,
  inheritLineHeight = true
}) => {
  const { font_family, font_gray, line_spacing } = useResumeStyleStore();

  // 如果没有内容，不渲染
  if (!content) {
    return null;
  }

  // 检查是否是AI生成内容（通过className判断）
  const isAIContent = className?.includes('ai-markdown-content');

  // 构建样式对象
  const viewerStyle: React.CSSProperties = {
    ...(removePadding && { padding: 0 }),
    ...(inheritLineHeight && { lineHeight: line_spacing }),
    fontFamily: font_family,
    color: isAIContent ? 'white' : font_gray,
    ...style
  };

  return (
    <div
      className={className}
      style={viewerStyle}
    >
      <ReactMarkdown
        components={{
          // 自定义组件样式
          p: ({ children }) => <p className="mb-2 last:mb-0">{children}</p>,
          ul: ({ children }) => <ul className="list-disc list-inside mb-2">{children}</ul>,
          ol: ({ children }) => <ol className="list-decimal list-inside mb-2">{children}</ol>,
          li: ({ children }) => <li className="mb-1">{children}</li>,
          strong: ({ children }) => <strong className="font-semibold">{children}</strong>,
          em: ({ children }) => <em className="italic">{children}</em>,
          h1: ({ children }) => <h1 className="text-lg font-bold mb-2">{children}</h1>,
          h2: ({ children }) => <h2 className="text-base font-bold mb-2">{children}</h2>,
          h3: ({ children }) => <h3 className="text-sm font-bold mb-1">{children}</h3>,
        }}
      >
        {content}
      </ReactMarkdown>
    </div>
  );
};

export default SimpleMarkdownViewer;
