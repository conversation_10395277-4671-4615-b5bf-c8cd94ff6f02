import React from 'react';
import { useResumeStyleStore } from '@/store/useResumeStyleStore';

interface BackgroundStyleProps {
  children: React.ReactNode;
}

export default function BackgroundStyle({ children }: BackgroundStyleProps) {
  const paper_style = useResumeStyleStore(state => state.paper_style);

  // 获取背景图片URL
  let backgroundUrl = '';

  // 如果paper_style为none、default或未定义，则不显示背景
  if (!paper_style || paper_style === 'none' || paper_style === 'default') {
    // 无背景
  } else {
    switch (paper_style) {
      case 'lines':
        backgroundUrl = '/bg/流线.png';
        break;
      case 'maze':
        backgroundUrl = '/bg/迷宫.png';
        break;
      case 'memphis':
        backgroundUrl = '/bg/孟菲斯.png';
        break;
      case 'dots':
        backgroundUrl = '/bg/波点.png';
        break;
      case 'tech-circle':
        backgroundUrl = '/bg/科技圈.png';
        break;
      case 'contour':
        backgroundUrl = '/bg/等高线.png';
        break;
      case 'donut':
        backgroundUrl = '/bg/甜甜圈.png';
        break;
      case 'super-tech':
        backgroundUrl = '/bg/科技超级圈.png';
        break;
    }
  }

  // 如果没有背景图片，直接返回children
  if (!backgroundUrl) {
    return <>{children}</>;
  }

  // 创建一个样式类，将背景应用到简历的上半部分
  const cssClass = `
    .w-\\[210mm\\] {
      position: relative !important;
    }

    .w-\\[210mm\\]:before {
      content: "" !important;
      position: absolute !important;
      top: 0 !important;
      left: 0 !important;
      width: 100% !important;
      height: 50% !important; /* 上半部分 */
      background: url(${backgroundUrl}) center top / 100% no-repeat !important;
      opacity: 0.15 !important; /* 降低透明度，使背景更淡 */
      z-index: 0 !important;
      pointer-events: none !important;
    }

    .w-\\[210mm\\] > * {
      position: relative !important;
      z-index: 1 !important;
    }
  `;

  return (
    <>
      <style>{cssClass}</style>
      {children}
    </>
  );
}
