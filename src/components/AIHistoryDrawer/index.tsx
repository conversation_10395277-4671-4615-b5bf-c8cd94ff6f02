'use client';

import React, { useEffect, useRef, useCallback } from 'react';
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerClose,
} from '@/components/ui/drawer';
import { X, RotateCcw } from 'lucide-react';
import { aiApi } from '@/api/client/ai';
import { useInfiniteScroll } from '@/hooks/useInfiniteScroll';
import { useModuleStore } from '@/store/useModuleStore';
import RecordItem from './RecordItem';
import type { AICallRecordResponse } from '@/api/client/types/ai';

interface AIHistoryDrawerProps {
  /** 是否打开 */
  open: boolean;
  /** 关闭回调 */
  onClose: () => void;
  /** 选择历史记录回调 */
  onSelectRecord?: (content: string) => void;
}

/**
 * AI历史记录抽屉组件
 */
const AIHistoryDrawer: React.FC<AIHistoryDrawerProps> = ({
  open,
  onClose,
  onSelectRecord
}) => {
  const { resumeId } = useModuleStore();
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  // 使用 useCallback 缓存 fetchData 函数，避免不必要的重新创建
  const fetchData = useCallback(async (page: number, pageSize: number) => {
    if (!resumeId) {
      throw new Error('简历ID不存在');
    }
    return await aiApi.getCallRecords({
      resume_id: resumeId,
      page,
      page_size: pageSize
    });
  }, [resumeId]);

  // 无限滚动加载历史记录
  const {
    data: records,
    loading,
    hasMore,
    loadMore,
    refresh,
    error,
    total
  } = useInfiniteScroll<AICallRecordResponse>({
    fetchData,
    pageSize: 20,
    enabled: open && !!resumeId
  });

  // 监听滚动事件，实现无限加载
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (!container) return;

    const handleScroll = () => {
      const { scrollTop, scrollHeight, clientHeight } = container;
      // 当滚动到底部附近时加载更多
      if (scrollHeight - scrollTop - clientHeight < 100 && hasMore && !loading) {
        loadMore();
      }
    };

    container.addEventListener('scroll', handleScroll);
    return () => container.removeEventListener('scroll', handleScroll);
  }, [hasMore, loading, loadMore]);

  // 处理选择历史记录
  const handleSelectRecord = (content: string) => {
    if (onSelectRecord) {
      onSelectRecord(content);
    }
  };

  return (
    <Drawer open={open} onOpenChange={onClose} direction="right">
      <DrawerContent className="h-full w-[60vw] min-w-[800px] max-w-[95vw] bg-gradient-to-br from-[#fafbff] via-[#f7f8fc] to-[#f3f4f8] border-l border-[#e1e5f0] rounded-l-2xl shadow-2xl shadow-purple-100/50">
        <DrawerHeader className="relative border-b border-purple-100/30 bg-gradient-to-r from-white/95 to-purple-50/20 backdrop-blur-xl p-8">
          {/* 装饰性背景元素 */}
          <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-bl from-purple-100/20 to-transparent rounded-full -translate-y-16 translate-x-16"></div>
          <div className="absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-100/20 to-transparent rounded-full translate-y-12 -translate-x-12"></div>

          <div className="relative flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="relative">
                <div className="w-12 h-12 rounded-2xl bg-gradient-to-br from-[#824dfc] to-[#9d7afd] flex items-center justify-center shadow-lg shadow-purple-200/50">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="white"/>
                  </svg>
                </div>
                <div className="absolute -top-1 -right-1 w-4 h-4 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full flex items-center justify-center">
                  <div className="w-2 h-2 bg-white rounded-full"></div>
                </div>
              </div>
              <div>
                <DrawerTitle className="text-2xl font-bold bg-gradient-to-r from-[#1e2d3e] to-[#4a5568] bg-clip-text text-transparent mb-2">
                  AI调用历史记录
                </DrawerTitle>
                <div className="flex items-center gap-3">
                  {total > 0 && (
                    <span className="inline-flex items-center gap-1.5 text-sm text-[#6b7280] font-medium bg-white/60 px-3 py-1.5 rounded-full border border-purple-100">
                      <div className="w-2 h-2 bg-gradient-to-r from-[#824dfc] to-[#9d7afd] rounded-full"></div>
                      共 {total} 条记录
                    </span>
                  )}
                  <span className="text-xs text-[#9ca3af] bg-gray-100/60 px-2 py-1 rounded-full">
                    实时同步
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <button
                onClick={refresh}
                className="group relative p-3 bg-white/60 hover:bg-white/80 rounded-xl transition-all duration-300 text-[#824dfc] hover:scale-110 hover:shadow-lg hover:shadow-purple-200/50 border border-purple-100/50"
                title="刷新记录"
              >
                <RotateCcw size={18} className="group-hover:rotate-180 transition-transform duration-500" />
                <div className="absolute inset-0 bg-gradient-to-r from-purple-400/0 to-purple-400/0 group-hover:from-purple-400/10 group-hover:to-blue-400/10 rounded-xl transition-all duration-300"></div>
              </button>
              <DrawerClose asChild>
                <button className="group relative p-3 bg-white/60 hover:bg-red-50/80 rounded-xl transition-all duration-300 text-[#6b7280] hover:text-red-500 hover:scale-110 hover:shadow-lg hover:shadow-red-200/50 border border-gray-200/50 hover:border-red-200">
                  <X size={18} className="group-hover:rotate-90 transition-transform duration-300" />
                  <div className="absolute inset-0 bg-gradient-to-r from-red-400/0 to-red-400/0 group-hover:from-red-400/10 group-hover:to-red-400/10 rounded-xl transition-all duration-300"></div>
                </button>
              </DrawerClose>
            </div>
          </div>
        </DrawerHeader>

        <div
          ref={scrollContainerRef}
          className="flex-1 overflow-y-auto p-8 space-y-6 bg-gradient-to-b from-transparent to-purple-50/10"
          style={{
            scrollbarWidth: 'thin',
            scrollbarColor: '#824dfc20 transparent'
          }}
        >
          {error && (
            <div className="text-center py-16">
              <div className="relative mx-auto mb-6">
                <div className="w-20 h-20 mx-auto rounded-2xl bg-gradient-to-br from-red-50 to-red-100 flex items-center justify-center shadow-lg shadow-red-100/50">
                  <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 9V13M12 17H12.01M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="#ef4444" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
                <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-red-400 to-red-500 rounded-full flex items-center justify-center animate-pulse">
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  </svg>
                </div>
              </div>
              <h3 className="text-lg font-semibold text-[#1e2d3e] mb-2">加载失败</h3>
              <p className="text-[#6b7280] mb-6 font-medium max-w-sm mx-auto leading-relaxed">{error}</p>
              <button
                onClick={refresh}
                className="group px-8 py-3 bg-gradient-to-r from-[#824dfc] to-[#9d7afd] text-white rounded-xl hover:shadow-xl hover:shadow-purple-200/50 hover:scale-105 transition-all duration-300 font-semibold flex items-center gap-2 mx-auto"
              >
                <RotateCcw size={18} className="group-hover:rotate-180 transition-transform duration-500" />
                重新加载
              </button>
            </div>
          )}

          {records.length === 0 && !loading && !error && (
            <div className="text-center py-20">
              <div className="relative mx-auto mb-8">
                <div className="w-24 h-24 mx-auto rounded-3xl bg-gradient-to-br from-purple-50 via-purple-100/50 to-blue-50 flex items-center justify-center shadow-xl shadow-purple-100/50 border border-purple-100/30">
                  <svg width="40" height="40" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="url(#gradient)" />
                    <defs>
                      <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" stopColor="#824dfc" />
                        <stop offset="100%" stopColor="#9d7afd" />
                      </linearGradient>
                    </defs>
                  </svg>
                </div>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-yellow-400 to-orange-400 rounded-full animate-bounce"></div>
                <div className="absolute -bottom-2 -left-2 w-4 h-4 bg-gradient-to-br from-blue-400 to-purple-400 rounded-full animate-pulse"></div>
              </div>
              <h3 className="text-xl font-bold text-[#1e2d3e] mb-3">暂无历史记录</h3>
              <p className="text-[#6b7280] text-base mb-2 max-w-xs mx-auto leading-relaxed">开始使用AI功能后</p>
              <p className="text-[#9ca3af] text-sm max-w-xs mx-auto leading-relaxed">历史记录将自动显示在这里</p>
              <div className="mt-8 flex justify-center">
                <div className="flex items-center gap-2 text-xs text-[#9ca3af] bg-white/60 px-4 py-2 rounded-full border border-purple-100/50">
                  <div className="w-2 h-2 bg-gradient-to-r from-green-400 to-green-500 rounded-full animate-pulse"></div>
                  等待AI调用中...
                </div>
              </div>
            </div>
          )}

          {records.map((record) => (
            <RecordItem
              key={record.id}
              record={record}
              onSelect={handleSelectRecord}
            />
          ))}

          {/* 加载更多指示器 */}
          {loading && (
            <div className="text-center py-10">
              <div className="inline-flex items-center gap-4 px-8 py-4 bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg shadow-purple-100/50 border border-purple-100/30">
                <div className="relative">
                  <div className="w-6 h-6 border-3 border-purple-200 border-t-purple-600 rounded-full animate-spin"></div>
                  <div className="absolute inset-0 w-6 h-6 border-3 border-transparent border-r-blue-400 rounded-full animate-spin animate-reverse"></div>
                </div>
                <span className="text-[#6b7280] font-semibold">正在加载更多记录...</span>
              </div>
            </div>
          )}

          {/* 没有更多数据提示 */}
          {!hasMore && records.length > 0 && (
            <div className="text-center py-10">
              <div className="inline-flex items-center gap-3 px-8 py-4 bg-gradient-to-r from-purple-50/80 via-white/60 to-blue-50/80 backdrop-blur-xl rounded-2xl border border-purple-200/50 shadow-lg shadow-purple-100/30">
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-purple-500 animate-pulse"></div>
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-purple-500 animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                </div>
                <span className="text-[#824dfc] text-sm font-semibold bg-gradient-to-r from-[#824dfc] to-[#9d7afd] bg-clip-text text-transparent">
                  已加载全部记录
                </span>
                <div className="flex items-center gap-1">
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-purple-500 animate-pulse" style={{ animationDelay: '0.6s' }}></div>
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-blue-400 to-blue-500 animate-pulse" style={{ animationDelay: '0.8s' }}></div>
                  <div className="w-2 h-2 rounded-full bg-gradient-to-r from-purple-400 to-purple-500 animate-pulse" style={{ animationDelay: '1s' }}></div>
                </div>
              </div>
            </div>
          )}
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export default AIHistoryDrawer;