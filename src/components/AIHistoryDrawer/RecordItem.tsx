'use client';

import React from 'react';
import { Copy } from 'lucide-react';
import { toast } from 'sonner';
import Markdown<PERSON>iewer from '@/components/MarkdownViewer';
import type { AICallRecordResponse } from '@/api/client/types/ai';

interface RecordItemProps {
  /** 历史记录数据 */
  record: AICallRecordResponse;
  /** 选择回调 */
  onSelect?: (content: string) => void;
}

/**
 * 格式化时间显示
 */
const formatTime = (dateString: string): string => {
  const date = new Date(dateString);
  const now = new Date();
  const diff = now.getTime() - date.getTime();

  // 小于1分钟
  if (diff < 60 * 1000) {
    return '刚刚';
  }

  // 小于1小时
  if (diff < 60 * 60 * 1000) {
    const minutes = Math.floor(diff / (60 * 1000));
    return `${minutes}分钟前`;
  }

  // 小于1天
  if (diff < 24 * 60 * 60 * 1000) {
    const hours = Math.floor(diff / (60 * 60 * 1000));
    return `${hours}小时前`;
  }

  // 小于7天
  if (diff < 7 * 24 * 60 * 60 * 1000) {
    const days = Math.floor(diff / (24 * 60 * 60 * 1000));
    return `${days}天前`;
  }

  // 超过7天显示具体日期
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * AI历史记录项组件
 */
const RecordItem: React.FC<RecordItemProps> = ({ record, onSelect }) => {
  // 复制内容到剪贴板
  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(record.response_data);
      toast.success('内容已复制到剪贴板');
    } catch (error) {
      toast.error('复制失败，请手动复制');
    }
  };

  // 选择此记录
  const handleSelect = () => {
    if (onSelect) {
      onSelect(record.response_data);
    }
  };

  return (
    <div className="group relative bg-white/95 backdrop-blur-xl border border-purple-100/50 rounded-2xl p-7 hover:shadow-2xl hover:shadow-purple-200/30 hover:border-purple-300/60 transition-all duration-500 hover:scale-[1.02] hover:-translate-y-1 overflow-hidden">
      {/* 装饰性背景元素 */}
      <div className="absolute top-0 right-0 w-20 h-20 bg-gradient-to-bl from-purple-100/20 to-transparent rounded-full -translate-y-10 translate-x-10 group-hover:scale-150 transition-transform duration-700"></div>
      <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-blue-100/20 to-transparent rounded-full translate-y-8 -translate-x-8 group-hover:scale-125 transition-transform duration-700"></div>

      {/* 记录头部信息 */}
      <div className="relative flex items-center justify-between mb-5">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-3">
            <div className="relative">
              <div className="w-3 h-3 rounded-full bg-gradient-to-r from-[#824dfc] to-[#9d7afd] shadow-lg shadow-purple-200/50"></div>
              <div className="absolute inset-0 w-3 h-3 rounded-full bg-gradient-to-r from-[#824dfc] to-[#9d7afd] animate-ping opacity-20"></div>
            </div>
            <span className="text-base font-bold text-[#1e2d3e] group-hover:text-[#824dfc] transition-colors duration-300">
              {record.prompt_type_name}
            </span>
          </div>
          <span className="text-xs text-[#824dfc] bg-gradient-to-r from-purple-50/80 to-blue-50/80 px-4 py-2.5 rounded-xl font-semibold border border-purple-200/50 shadow-sm backdrop-blur-sm group-hover:shadow-md group-hover:scale-105 transition-all duration-300">
            {record.resume_module_name}
          </span>
        </div>
        <div className="flex items-center gap-4">
          <span className="text-xs text-[#6b7280] font-semibold bg-gradient-to-r from-gray-50/80 to-gray-100/80 px-4 py-2.5 rounded-xl border border-gray-200/50 backdrop-blur-sm group-hover:shadow-sm transition-all duration-300">
            {formatTime(record.created_at)}
          </span>
          <button
            onClick={handleCopy}
            className="group/copy relative p-3 hover:bg-purple-50/80 rounded-xl transition-all duration-300 hover:scale-110 hover:shadow-lg hover:shadow-purple-200/50 border border-transparent hover:border-purple-200/50"
            title="复制内容"
          >
            <Copy size={16} className="text-[#6b7280] group-hover/copy:text-[#824dfc] transition-colors duration-300" />
            <div className="absolute inset-0 bg-gradient-to-r from-purple-400/0 to-purple-400/0 group-hover/copy:from-purple-400/10 group-hover/copy:to-blue-400/10 rounded-xl transition-all duration-300"></div>
          </button>
        </div>
      </div>

      {/* 响应内容 */}
      <div className="relative bg-gradient-to-br from-[#fafbff] via-[#f7f8fc] to-[#f3f4f8] rounded-2xl p-6 mb-6 min-h-[140px] max-h-72 overflow-y-auto border border-purple-100/30 shadow-inner group-hover:shadow-lg group-hover:border-purple-200/50 transition-all duration-500"
           style={{
             scrollbarWidth: 'thin',
             scrollbarColor: '#824dfc30 transparent'
           }}>
        <div className="absolute top-4 right-4 w-8 h-8 rounded-xl bg-gradient-to-br from-purple-100/80 to-blue-100/80 flex items-center justify-center shadow-sm border border-purple-200/30 group-hover:scale-110 group-hover:rotate-12 transition-all duration-500">
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z" fill="url(#sparkle-gradient)"/>
            <defs>
              <linearGradient id="sparkle-gradient" x1="0%" y1="0%" x2="100%" y2="100%">
                <stop offset="0%" stopColor="#824dfc" />
                <stop offset="100%" stopColor="#9d7afd" />
              </linearGradient>
            </defs>
          </svg>
        </div>
        <MarkdownViewer
          content={record.response_data}
          className="text-base text-[#1e2d3e] leading-relaxed pr-12 group-hover:text-[#2d3748] transition-colors duration-300"
          removePadding={true}
        />
      </div>

      {/* 操作按钮 */}
      {onSelect && (
        <div className="relative flex justify-end">
          <button
            onClick={handleSelect}
            className="group/select relative px-8 py-3.5 bg-gradient-to-r from-[#824dfc] to-[#9d7afd] text-white text-sm font-bold rounded-xl hover:shadow-2xl hover:shadow-purple-300/50 hover:scale-110 hover:-translate-y-1 transition-all duration-400 flex items-center gap-3 overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover/select:translate-x-full transition-transform duration-700"></div>
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" className="relative z-10 group-hover/select:rotate-12 transition-transform duration-300">
              <path d="M20 6L9 17L4 12" stroke="currentColor" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
            <span className="relative z-10">使用此结果</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default RecordItem;