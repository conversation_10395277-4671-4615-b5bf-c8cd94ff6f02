/* AI生成内容的Markdown样式 - 适配深色背景 */

.ai-markdown-content {
  color: white !important;
}

.ai-markdown-content * {
  color: white !important;
}

.ai-markdown-content h1,
.ai-markdown-content h2,
.ai-markdown-content h3,
.ai-markdown-content h4,
.ai-markdown-content h5,
.ai-markdown-content h6 {
  color: white !important;
  font-weight: 600;
  margin: 0.5em 0;
}

.ai-markdown-content p {
  color: white !important;
  margin: 0.5em 0;
}

.ai-markdown-content a {
  color: #87CEEB !important; /* 浅蓝色，在深色背景下更易读 */
  text-decoration: underline;
}

.ai-markdown-content a:hover {
  color: #B0E0E6 !important; /* 悬停时更亮的蓝色 */
}

.ai-markdown-content strong {
  color: white !important;
  font-weight: bold;
}

.ai-markdown-content em {
  color: white !important;
  font-style: italic;
}

.ai-markdown-content ul,
.ai-markdown-content ol {
  color: white !important;
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.ai-markdown-content li {
  color: white !important;
  margin: 0.25em 0;
}

.ai-markdown-content li::marker {
  color: white !important;
}

.ai-markdown-content blockquote {
  color: white !important;
  border-left: 4px solid rgba(255, 255, 255, 0.5);
  margin: 0.5em 0;
  padding-left: 1em;
  font-style: italic;
  background-color: rgba(255, 255, 255, 0.05);
}

.ai-markdown-content code {
  background-color: rgba(255, 255, 255, 0.15) !important;
  color: #FFE4E1 !important; /* 浅粉色，更柔和 */
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 0.9em;
}

.ai-markdown-content pre {
  background-color: rgba(0, 0, 0, 0.4) !important;
  color: white !important;
  padding: 1em;
  border-radius: 4px;
  overflow-x: auto;
  margin: 0.5em 0;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.ai-markdown-content pre code {
  background-color: transparent !important;
  color: white !important;
  padding: 0;
}

/* 确保所有文本元素都是白色 */
.ai-markdown-content span,
.ai-markdown-content div,
.ai-markdown-content text,
.ai-markdown-content td,
.ai-markdown-content th,
.ai-markdown-content table,
.ai-markdown-content tbody,
.ai-markdown-content thead,
.ai-markdown-content tr {
  color: white !important;
}

/* Toast UI Editor 特定样式覆盖 */
.ai-markdown-content .toastui-editor-contents,
.ai-markdown-content .toastui-editor-contents *,
.ai-markdown-content .toastui-editor-contents p,
.ai-markdown-content .toastui-editor-contents div,
.ai-markdown-content .toastui-editor-contents span,
.ai-markdown-content .toastui-editor-contents li,
.ai-markdown-content .toastui-editor-contents ul,
.ai-markdown-content .toastui-editor-contents ol,
.ai-markdown-content .toastui-editor-contents blockquote,
.ai-markdown-content .toastui-editor-contents table,
.ai-markdown-content .toastui-editor-contents td,
.ai-markdown-content .toastui-editor-contents th {
  color: white !important;
}

/* 强制覆盖任何可能的黑色文字 */
.ai-markdown-content [style*="color: black"],
.ai-markdown-content [style*="color: #000"],
.ai-markdown-content [style*="color: rgb(0, 0, 0)"] {
  color: white !important;
}

/* AI按钮点击动画 */
@keyframes buttonPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.ai-button-pressed {
  animation: buttonPress 0.2s ease-in-out;
}

/* 页面轻微震动动画（可选） */
@keyframes gentleShake {
  0%, 100% { transform: translateY(0); }
  25% { transform: translateY(-2px); }
  75% { transform: translateY(2px); }
}

.ai-generating-shake {
  animation: gentleShake 0.3s ease-in-out;
}
