'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { X, Check, Download, Loader2, Crown, ArrowRight } from 'lucide-react';
import { UserType } from '@/api/client/types/user';
import { DownloadCouponPlanResponse } from '@/api/client/types/membership';
import Image from 'next/image';
import { cn } from '@/lib/utils';
import { useUserStore } from '@/store/useUserStore';
import { toast } from 'sonner';
import QRCode from 'react-qr-code';
import { orderApi } from '@/api/client';
import { membershipApi } from '@/api/client/membership';
import { PaymentStatus } from '@/api/client/types/order';

interface DownloadCouponDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSwitchToMembership?: () => void;
}

export default function DownloadCouponDialog({ isOpen, onClose, onSwitchToMembership }: DownloadCouponDialogProps) {
  // 下载券套餐相关状态
  const [plans, setPlans] = useState<DownloadCouponPlanResponse[]>([]);
  const [selectedPlanId, setSelectedPlanId] = useState<number | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 支付相关状态
  const [paymentMethod, setPaymentMethod] = useState<'wechat' | 'alipay'>('wechat');
  const [orderNo, setOrderNo] = useState<string>('');
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null);
  const [isCreatingOrder, setIsCreatingOrder] = useState(false);
  const [isPolling, setIsPolling] = useState(false);

  // 轮询定时器引用
  const pollingTimerRef = useRef<NodeJS.Timeout | null>(null);

  // 从 store 中获取用户信息
  const { id, username, avatar, user_type, getGuestUserInfo } = useUserStore();

  // 获取下载券套餐列表
  useEffect(() => {
    if (isOpen) {
      fetchDownloadCouponPlans();
    }
  }, [isOpen]);

  // 弹窗打开时默认选中微信支付并创建订单
  useEffect(() => {
    if (isOpen && selectedPlanId && !orderNo && !isCreatingOrder) {
      // 默认创建微信支付订单
      createOrder('wechat');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, selectedPlanId, orderNo, isCreatingOrder]);

  // 清理轮询定时器
  useEffect(() => {
    // 当弹窗关闭时，重置状态
    if (!isOpen) {
      setPaymentMethod('wechat');
      setOrderNo('');
      setQrCodeUrl('');
      setPaymentStatus(null);
      setIsCreatingOrder(false);
      setIsPolling(false);

      // 清理轮询定时器
      if (pollingTimerRef.current) {
        clearInterval(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }
    }

    // 当组件卸载时，清理轮询定时器
    return () => {
      if (pollingTimerRef.current) {
        clearInterval(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }
    };
  }, [isOpen]);

  const fetchDownloadCouponPlans = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await membershipApi.getDownloadCouponPlans();
      setPlans(response.list);
      
      // 默认选中第一个套餐
      if (response.list.length > 0) {
        setSelectedPlanId(response.list[0].id);
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '获取下载券套餐失败';
      setError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const handlePlanSelect = (planId: number) => {
    setSelectedPlanId(planId);

    // 选择套餐后，根据当前支付方式重新创建订单
    if (planId) {
      createOrder(paymentMethod);
    }
  };

  // 创建订单并开始轮询状态
  const createOrder = async (method: 'wechat' | 'alipay') => {
    // 获取当前选中的套餐
    const currentPlan = plans.find(plan => plan.id === selectedPlanId);

    if (!currentPlan || !currentPlan.id) {
      toast.error('未找到选中的套餐信息，请重新选择', {
        position: 'top-center',
        style: {
          color: 'white',
          backgroundColor: 'var(--destructive)',
          border: '1px solid var(--destructive)'
        }
      });
      return;
    }

    try {
      setIsCreatingOrder(true);
      setPaymentMethod(method);

      // 清除之前的轮询
      if (pollingTimerRef.current) {
        clearInterval(pollingTimerRef.current);
        pollingTimerRef.current = null;
      }

      // 从 localStorage 获取百度投放ID
      const bdVid = localStorage.getItem('bd_vid') || '';

      // 创建订单
      const orderData = method === 'wechat'
        ? await orderApi.createWechatPayOrder(currentPlan.id, bdVid)
        : await orderApi.createAlipayOrder(currentPlan.id, bdVid);

      // 保存订单信息
      setOrderNo(orderData.order_no);
      setQrCodeUrl(orderData.code_url);
      setPaymentStatus(PaymentStatus.Pending);

      // 开始轮询订单状态
      startPollingOrderStatus(orderData.order_no);
    } catch (error) {
      // 显示错误提示
      const errorMsg = error instanceof Error ? error.message : '创建订单失败，请稍后重试';
      toast.error(errorMsg, {
        position: 'top-center',
        style: {
          color: 'white',
          backgroundColor: 'var(--destructive)',
          border: '1px solid var(--destructive)'
        }
      });

      // 设置错误状态
      setPaymentStatus(null);
    } finally {
      setIsCreatingOrder(false);
    }
  };

  // 开始轮询订单状态
  const startPollingOrderStatus = (orderNo: string) => {
    setIsPolling(true);

    // 每2秒查询一次订单状态
    pollingTimerRef.current = setInterval(async () => {
      try {
        const statusData = await orderApi.queryOrderStatus(orderNo);
        setPaymentStatus(statusData.payment_status);

        // 根据支付状态处理
        if (statusData.payment_status === PaymentStatus.Success) {
          // 支付成功，停止轮询
          if (pollingTimerRef.current) {
            clearInterval(pollingTimerRef.current);
            pollingTimerRef.current = null;
          }
          setIsPolling(false);

          // 必应转化跟踪
          if (statusData.channel_name === 'Bing' && typeof window !== 'undefined' && (window as any).uetq) {
            const a = (window as any).uetq.push('event', 'purchase', {
              "revenue_value": statusData.amount,
              "currency": "CNY"
            });
          }

          // 刷新用户信息
          try {
            await getGuestUserInfo();
          } catch (error) {
          }

          // 显示成功提示并关闭弹窗
          toast.success('支付成功！下载券已到账', {
            position: 'top-center'
          });
          onClose();
        } else if (
          statusData.payment_status === PaymentStatus.Failed ||
          statusData.payment_status === PaymentStatus.Timeout
        ) {
          // 支付失败或超时，停止轮询
          if (pollingTimerRef.current) {
            clearInterval(pollingTimerRef.current);
            pollingTimerRef.current = null;
          }
          setIsPolling(false);

          // 显示错误信息
          const failReason = statusData.fail_reason || '支付失败，请重试';
          toast.error(failReason, {
            position: 'top-center',
            style: {
              color: 'white',
              backgroundColor: 'var(--destructive)',
              border: '1px solid var(--destructive)'
            }
          });
        }
        // 待支付和处理中状态继续轮询
      } catch (error) {
        // 显示错误提示
        const errorMsg = error instanceof Error ? error.message : '查询订单状态失败，请刷新页面重试';
        toast.error(errorMsg, {
          position: 'top-center',
          style: {
            color: 'white',
            backgroundColor: 'var(--destructive)',
            border: '1px solid var(--destructive)'
          }
        });
      }
    }, 2000);
  };

  // 获取选中的套餐信息
  const selectedPlanData = plans.find(plan => plan.id === selectedPlanId);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        // 只有当用户点击关闭按钮时才会触发关闭
        if (!open) {
          onClose();
        }
      }}
      modal={true} // 确保弹窗是模态的
    >
      <DialogContent
        className="!w-[900px] !max-w-[900px] !p-0 !gap-0 overflow-hidden max-h-[90vh] [&>button]:hidden !border-0 !shadow-xl"
        onEscapeKeyDown={(e) => e.preventDefault()} // 禁用ESC键关闭
        onPointerDownOutside={(e) => e.preventDefault()} // 禁用点击背景关闭
        onInteractOutside={(e) => e.preventDefault()} // 禁用外部交互关闭
        onClick={(e) => e.stopPropagation()} // 阻止事件冒泡
      >
        <DialogTitle className="sr-only" style={{display:"none"}}>下载券充值</DialogTitle>

        {/* 顶部栏 */}
        <div className="text-white p-3 relative bg-cover bg-center" style={{ backgroundImage: 'url(/home/<USER>' }}>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-8 h-8 rounded-full bg-white bg-opacity-20 flex items-center justify-center">
                <Image
                  src={avatar || 'https://cdn.shineresume.com/avatar/6.svg'}
                  alt="用户头像"
                  width={28}
                  height={28}
                  className="rounded-full"
                />
              </div>
              <div>
                <div className="flex items-center gap-2">
                  <p className="text-sm font-medium">{username || '游客用户'}</p>
                  {user_type === UserType.Member && (
                    <span className="flex items-center gap-1 bg-yellow-100 text-yellow-500 text-xs px-2 py-1 rounded">
                      <Download className="w-3 h-3" />
                      <span>会员</span>
                    </span>
                  )}
                </div>
                <p className="text-xs opacity-80">用户ID: {id || '未登录'}</p>
              </div>
            </div>

            <button
              onClick={(e) => {
                e.stopPropagation();
                onClose();
              }}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <X className="h-5 w-5 stroke-[1.5]" />
            </button>
          </div>
        </div>

        {/* 内容区域 */}
        <div className="flex bg-white">
          <div className="w-[550px] p-6 overflow-y-auto max-h-[calc(90vh-56px)]">
            {/* 下载券权益说明 */}
            <div className="text-center mb-6">
              <div className="flex items-center justify-center gap-2 mb-3">
                <Download className="w-6 h-6 text-[#824dfc]" />
                <h3 className="text-xl font-bold bg-gradient-to-r from-[#824dfc] to-blue-500 bg-clip-text text-transparent">
                  下载券充值
                </h3>
              </div>
              <p className="text-sm text-gray-600 mb-2">不想开通会员？没关系！</p>
              <p className="text-sm text-gray-700 font-medium">购买下载券，按需下载简历PDF文件</p>
            </div>

            {/* 套餐选择 */}
            <div className="mb-6">
              <div className="flex items-center justify-between mb-4">
                <h4 className="text-lg font-semibold text-gray-800">选择下载券套餐</h4>
                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded-full">会员替代方案</span>
              </div>
              <div className="space-y-3">
                {loading ? (
                  // 加载状态
                  <div className="flex justify-center items-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                    <span className="ml-2 text-gray-600">加载中...</span>
                  </div>
                ) : error ? (
                  // 错误状态
                  <div className="text-center py-8">
                    <p className="text-red-500 mb-2">{error}</p>
                    <button
                      className="px-4 py-2 bg-primary text-white rounded-md hover:bg-purple-600"
                      onClick={() => onClose()}
                    >
                      关闭
                    </button>
                  </div>
                ) : plans.length === 0 ? (
                  // 空状态
                  <div className="text-center py-8">
                    <p className="text-gray-500">暂无可用套餐</p>
                  </div>
                ) : (
                  // 正常显示套餐列表
                  plans.map((plan) => (
                    <div
                      key={plan.id}
                      className={cn(
                        "bg-white border-2 rounded-lg p-4 cursor-pointer transition-all hover:shadow-md",
                        selectedPlanId === plan.id
                          ? "border-[#824dfc] shadow-lg bg-purple-50"
                          : "border-gray-200 hover:border-purple-300"
                      )}
                      onClick={(e) => {
                        e.stopPropagation();
                        handlePlanSelect(plan.id);
                      }}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center gap-3">
                            <div className={cn(
                              "w-5 h-5 rounded-full border-2 flex items-center justify-center",
                              selectedPlanId === plan.id
                                ? "border-[#824dfc] bg-[#824dfc]"
                                : "border-gray-300"
                            )}>
                              {selectedPlanId === plan.id && (
                                <Check className="w-3 h-3 text-white" />
                              )}
                            </div>
                            <div>
                              <h5 className="text-lg font-semibold text-gray-800">{plan.name}</h5>
                              <p className="text-sm text-gray-600">
                                {plan.resume_limit}张下载券
                              </p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="flex items-baseline gap-2">
                            <span className="text-2xl font-bold text-[#824dfc]">
                              ¥{plan.actual_price.toFixed(1)}
                            </span>
                            <span className="text-sm text-gray-400 line-through">
                              ¥{plan.original_price.toFixed(0)}
                            </span>
                          </div>
                          <p className="text-xs text-green-600">
                            节省¥{(plan.original_price - plan.actual_price).toFixed(1)}
                          </p>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* 下载券说明 */}
            <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4 border border-blue-100">
              <div className="flex items-center gap-2 mb-3">
                <div className="w-6 h-6 bg-[#824dfc] rounded-full flex items-center justify-center">
                  <Download className="w-3 h-3 text-white" />
                </div>
                <h5 className="text-sm font-semibold text-gray-800">为什么选择下载券？</h5>
              </div>
              <div className="space-y-2 text-xs text-gray-700">
                <div className="flex items-start gap-2">
                  <span className="text-[#824dfc] font-bold">💡</span>
                  <span><strong>灵活按需</strong>：不想开通会员？下载券让你按需付费，用多少买多少</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-[#824dfc] font-bold">⚡</span>
                  <span><strong>即买即用</strong>：每下载一次简历PDF消耗1张下载券，简单直接</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-[#824dfc] font-bold">🎯</span>
                  <span><strong>永久有效</strong>：下载券永不过期，支持所有简历模板</span>
                </div>
                <div className="flex items-start gap-2">
                  <span className="text-[#824dfc] font-bold">💰</span>
                  <span><strong>经济实惠</strong>：相比会员更适合偶尔下载的用户</span>
                </div>
              </div>
            </div>

            {/* 会员推荐区域 */}
            {onSwitchToMembership && (
              <div className="mt-4 relative overflow-hidden">
                {/* 背景动画效果 */}
                <div className="absolute inset-0 bg-gradient-to-r from-amber-50 via-orange-50 to-red-50 rounded-xl"></div>
                <div className="absolute inset-0 bg-gradient-to-r from-amber-100/50 via-orange-100/50 to-red-100/50 rounded-xl animate-pulse"></div>

                <div className="relative bg-white/80 backdrop-blur-sm rounded-xl p-4 border-2 border-gradient-to-r from-amber-200 to-orange-300 shadow-lg">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="relative">
                        <div className="w-12 h-12 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 rounded-full flex items-center justify-center shadow-lg">
                          <Crown className="w-6 h-6 text-white" />
                        </div>
                        {/* 光环效果 */}
                        <div className="absolute inset-0 bg-gradient-to-r from-amber-400 via-orange-500 to-red-500 rounded-full animate-ping opacity-20"></div>
                      </div>
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h5 className="text-sm font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent">
                            🔥 还是想要会员？
                          </h5>
                          <span className="text-xs bg-red-500 text-white px-2 py-0.5 rounded-full font-bold animate-bounce">
                            HOT
                          </span>
                        </div>
                        <p className="text-xs text-gray-600 font-medium">
                          无限下载 + 全部AI功能，性价比更高！
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <span className="text-xs text-green-600 font-semibold">✓ 更划算</span>
                          <span className="text-xs text-blue-600 font-semibold">✓ 功能全</span>
                          <span className="text-xs text-purple-600 font-semibold">✓ 无限制</span>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onSwitchToMembership();
                      }}
                      className="group flex items-center gap-2 px-5 py-2.5 bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 text-white text-sm font-bold rounded-full hover:from-orange-600 hover:via-red-600 hover:to-pink-600 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 hover:-translate-y-0.5"
                    >
                      <span>立即开通</span>
                      <ArrowRight className="w-4 h-4 group-hover:translate-x-1 transition-transform duration-200" />
                    </button>
                  </div>

                  {/* 装饰性元素 */}
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-r from-red-500 to-pink-500 rounded-full flex items-center justify-center text-white text-xs font-bold animate-pulse shadow-lg">
                    ⚡
                  </div>
                  <div className="absolute -bottom-2 -left-2 w-6 h-6 bg-gradient-to-r from-yellow-400 to-orange-400 rounded-full opacity-80 animate-bounce"></div>

                  {/* 闪烁效果 */}
                  <div className="absolute top-2 right-8 w-2 h-2 bg-white rounded-full animate-ping"></div>
                  <div className="absolute bottom-3 left-8 w-1.5 h-1.5 bg-yellow-300 rounded-full animate-ping delay-300"></div>
                </div>
              </div>
            )}
          </div>

          {/* 订单详情 */}
          <div className="w-[350px] p-4 overflow-y-auto max-h-[calc(90vh-56px)]">
            <div className="bg-white rounded-lg px-5 py-3 border border-gray-200">
              {/* 订单详情标题 */}
              <div className="mb-2">
                <h3 className="text-lg font-bold text-gray-800 mb-2">订单详情</h3>
              </div>

              {/* 价格信息 */}
              <div className="mb-2 px-4 py-3 bg-gray-50 rounded-lg">
                <div className="flex items-center justify-between mb-3">
                  <span className="text-base font-medium">
                    {selectedPlanData ? selectedPlanData.name : '下载券套餐'}
                  </span>
                  <span className="text-2xl font-bold text-[#824dfc]">
                    ¥{selectedPlanData ? selectedPlanData.actual_price.toFixed(1) : '0.0'}
                  </span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">下载券数量</span>
                  <span className="text-sm font-semibold text-[#824dfc]">
                    {selectedPlanData ? selectedPlanData.resume_limit : 0}张
                  </span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">原价</span>
                  <span className="text-sm text-gray-400 line-through">
                    ¥{selectedPlanData ? selectedPlanData.original_price.toFixed(0) : '0'}
                  </span>
                </div>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600">优惠</span>
                  <span className="text-sm text-green-600">
                    -¥{selectedPlanData
                      ? (selectedPlanData.original_price - selectedPlanData.actual_price).toFixed(1)
                      : '0.0'}
                  </span>
                </div>
              </div>

              {/* 支付方式 */}
              <div className="mb-3">
                <h4 className="text-sm font-medium text-gray-800 mb-3">支付方式</h4>
                <div className="space-y-2">
                  <button
                    className={cn(
                      "w-full flex items-center gap-3 p-3 rounded-lg border-2 transition-all",
                      paymentMethod === 'wechat'
                        ? "border-green-500 bg-green-50"
                        : "border-gray-200 hover:border-gray-300"
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      createOrder('wechat');
                    }}
                    disabled={isCreatingOrder}
                  >
                    <Image
                      src="/home/<USER>"
                      alt="微信支付"
                      width={24}
                      height={24}
                    />
                    <span className="flex-1 text-left">微信支付</span>
                    {paymentMethod === 'wechat' && (
                      <div className="w-4 h-4 rounded-full bg-green-500 flex items-center justify-center">
                        <Check className="w-2.5 h-2.5 text-white" />
                      </div>
                    )}
                  </button>

                  <button
                    className={cn(
                      "w-full flex items-center gap-3 p-3 rounded-lg border-2 transition-all",
                      paymentMethod === 'alipay'
                        ? "border-blue-500 bg-blue-50"
                        : "border-gray-200 hover:border-gray-300"
                    )}
                    onClick={(e) => {
                      e.stopPropagation();
                      createOrder('alipay');
                    }}
                    disabled={isCreatingOrder}
                  >
                    <Image
                      src="/home/<USER>"
                      alt="支付宝支付"
                      width={24}
                      height={24}
                    />
                    <span className="flex-1 text-left">支付宝支付</span>
                    {paymentMethod === 'alipay' && (
                      <div className="w-4 h-4 rounded-full bg-blue-500 flex items-center justify-center">
                        <Check className="w-2.5 h-2.5 text-white" />
                      </div>
                    )}
                  </button>
                </div>
              </div>

              {/* 二维码 */}
              <div className="mb-3">
                <div className="flex flex-col items-center relative">
                  <div className="w-40 h-40 border border-gray-200 rounded-lg flex items-center justify-center relative overflow-hidden">
                    {isCreatingOrder ? (
                      <div className="flex flex-col items-center">
                        <Loader2 className="w-8 h-8 animate-spin text-primary mb-2" />
                        <span className="text-xs text-gray-500">创建订单中...</span>
                      </div>
                    ) : qrCodeUrl ? (
                      <div className="w-36 h-36 flex items-center justify-center">
                        <QRCode
                          value={qrCodeUrl}
                          size={200}
                          style={{ height: "200px", width: "200px", display: "block" }}
                          viewBox={`0 0 256 256`}
                        />
                      </div>
                    ) : (
                      <span className="text-xs text-gray-500">请选择支付方式</span>
                    )}

                    {/* 支付状态覆盖层 */}
                    {isPolling && paymentStatus === PaymentStatus.Processing && (
                      <div className="absolute inset-0 bg-black bg-opacity-10 flex items-center justify-center rounded-lg">
                        <div className="bg-white p-2 rounded-md shadow-md text-xs flex flex-col items-center">
                          <Loader2 className="w-4 h-4 animate-spin text-primary mb-1" />
                          <span>处理中...</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="text-xs text-gray-600 text-center mt-1">
                  {paymentStatus === PaymentStatus.Pending && qrCodeUrl ? '扫码支付' :
                   paymentStatus === PaymentStatus.Processing ? '支付处理中...' :
                   paymentStatus === PaymentStatus.Success ? '支付成功' :
                   paymentStatus === PaymentStatus.Failed ? '支付失败' :
                   paymentStatus === PaymentStatus.Timeout ? '支付超时' :
                   '扫码支付'}
                </div>
              </div>

              {/* 协议 */}
              <div className="mt-2 text-center text-xs text-gray-500">
                <label className="flex items-center justify-center gap-2">
                  <input type="checkbox" className="w-3 h-3" defaultChecked />
                  <span>购买即同意
                    <a href="/value-added-services" target="_blank" rel="noopener noreferrer" className="text-[#824dfc] hover:underline">《下载券服务协议》</a>
                  </span>
                </label>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
