'use client';

import { useEffect } from 'react';
import { useMembershipStore } from '@/store/useMembershipStore';
import { useUserStore } from '@/store/useUserStore';

/**
 * Store 初始化组件
 * 用于在应用启动时初始化各种 store 数据
 * - 初始化会员套餐数据
 * - 初始化用户信息数据
 */
export default function StoreInitializer() {
  const { plans, loading, fetchMembershipPlans } = useMembershipStore();
  const { id, loading: userLoading, getGuestUserInfo } = useUserStore();

  // 在组件挂载时初始化会员套餐数据
  useEffect(() => {
    // 如果套餐列表为空且不在加载中，则获取数据
    if (plans.length === 0 && !loading) {
      fetchMembershipPlans();
    }
  }, [plans.length, loading, fetchMembershipPlans]);

  // 在组件挂载时初始化用户数据
  useEffect(() => {
    // 如果用户ID为0（默认状态）且不在加载中，则获取用户信息
    if (id === 0 && !userLoading) {
      getGuestUserInfo();
    }
  }, [id, userLoading, getGuestUserInfo]);

  // 这是一个纯功能组件，不需要渲染任何内容
  return null;
}
