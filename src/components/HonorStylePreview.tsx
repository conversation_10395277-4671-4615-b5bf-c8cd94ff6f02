import React from 'react';
import BookmarkIcon from '@/assets/svg/bookmark.svg';
import TrophyIcon from '@/assets/svg/trophy.svg';
import MedalIcon from '@/assets/svg/medal.svg';
import StarMedalIcon from '@/assets/svg/star-medal.svg';

interface HonorStylePreviewProps {
  style: string;
  text?: string;
}

export default function HonorStylePreview({ style, text = '校级优秀学生奖学金' }: HonorStylePreviewProps) {
  const renderStyle = () => {
    switch (style) {
      case '1':
        return (
          <div className="flex items-center gap-2 whitespace-nowrap">
            <span className="self-center">{text}</span>
          </div>
        );
      case '2':
        return (
          <div className="flex items-center gap-2 whitespace-nowrap">
            <div className="w-2 h-2 bg-black self-center"></div>
            <span className="self-center">{text}</span>
          </div>
        );
      case '3':
        return (
          <div className="flex items-center gap-2 whitespace-nowrap">
            <div className="w-0 h-0 border-t-[5px] border-b-[5px] border-l-[7px] border-t-transparent border-b-transparent border-l-black self-center"></div>
            <span className="self-center">{text}</span>
          </div>
        );
      case '4':
        return (
          <div className="flex items-center gap-2 whitespace-nowrap">
            <BookmarkIcon width={5} height={7} className="self-center text-black size-[10px]" />
            <span className="self-center">{text}</span>
          </div>
        );
      case '5':
        return (
          <div className="flex items-center gap-2 whitespace-nowrap">
            <TrophyIcon width={10} height={10} className="self-center text-black size-[10px]" />
            <span className="self-center">{text}</span>
          </div>
        );
      case '6':
        return (
          <div className="flex items-center gap-2 whitespace-nowrap">
            <MedalIcon width={10} height={10} className="self-center text-black size-[10px]" />
            <span className="self-center">{text}</span>
          </div>
        );
      case '7':
        return (
          <div className="flex items-center gap-2 whitespace-nowrap">
            <StarMedalIcon width={8} height={10} className="self-center text-black size-[10px]" />
            <span className="self-center">{text}</span>
          </div>
        );
      default:
        return (
          <div className="flex items-center gap-2 whitespace-nowrap">
            <span className="self-center">{text}</span>
          </div>
        );
    }
  };

  return renderStyle();
}
