'use client';

import React from 'react';
import { cn } from '@/lib/utils';

interface HonorStyleItemProps {
  /**
   * 荣誉项目的标题
   */
  title?: string;

  /**
   * 自定义内容组件，将显示在项目中
   */
  children: React.ReactNode;

  /**
   * 是否选中当前样式
   */
  isSelected?: boolean;

  /**
   * 点击事件处理函数
   */
  onClick?: () => void;

  /**
   * 自定义类名
   */
  className?: string;
}

/**
 * 荣誉样式项组件
 *
 * 用于显示荣誉墙中的样式选项，可以包含自定义内容
 */
const HonorStyleItem: React.FC<HonorStyleItemProps> = ({
  title,
  children,
  isSelected = false,
  onClick,
  className,
}) => {
  return (
    <div
      className={cn(
        "flex items-center justify-between w-full text-[14px] text-[#2E2F66] bg-white p-3 rounded-md border transition-all cursor-pointer",
        isSelected
          ? "border-primary ring-1 ring-purple-200"
          : "border-gray-200 hover:border-purple-300",
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-center gap-2 flex-1">
        {children}
        {title && <span className="truncate">{title}</span>}
      </div>
      {isSelected && (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-primary flex-shrink-0 ml-2">
          <polyline points="20 6 9 17 4 12"></polyline>
        </svg>
      )}
      {!isSelected && (
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="text-gray-400 flex-shrink-0 ml-2">
          <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
      )}
    </div>
  );
};

export default HonorStyleItem;
