'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useModuleStore } from '@/store/useModuleStore';
import { useUserStore } from '@/store/useUserStore';
import { ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface ModuleNavigatorProps {
  currentModuleId: string;
  className?: string;
}

/**
 * 模块导航器组件
 * 用于在模块之间导航，显示"下一步"或"完成"按钮
 */
export default function ModuleNavigator({ currentModuleId, className }: ModuleNavigatorProps) {
  const { modules, customModules, setActiveModule, saveResume, resumeId } = useModuleStore();
  const { is_logged_in } = useUserStore();
  const [isSaving, setIsSaving] = useState(false);

  // 获取可见模块（包括传统模块和自定义模块，按索引排序）
  const visibleModules = [
    // 添加传统的 modules（只显示 is_visible: true 的）
    ...Object.values(modules).filter(module => module.is_visible),
    // 添加所有的 customModules，转换为 Module 格式（包括空的自定义模块）
    ...customModules.map(customModule => ({
      id: customModule.id,
      name: customModule.name,
      type: 'custom' as const,
      is_visible: true,
      is_custom: true,
      is_required: false,
      support_ai: false,
      index: customModule.index,
      item: customModule.items
    }))
  ].sort((a, b) => a.index - b.index);

  // 查找当前模块的索引
  const currentModuleIndex = visibleModules.findIndex(module => module.id === currentModuleId);

  // 判断是否是最后一个模块
  const isLastModule = currentModuleIndex === visibleModules.length - 1;

  // 处理下一步点击
  const handleNextClick = async () => {
    setIsSaving(true);

    try {
      // 先保存当前模块的数据
      await saveResume();

      // 只有在已登录的情况下才显示保存成功提示
      if (is_logged_in) {
        toast.success(isLastModule ? '简历已完成并保存' : '已保存，进入下一步', {
          position: 'top-center'
        });
      }

      if (isLastModule) {
        // 如果是最后一个模块，跳转到预览页
        if (resumeId) {
          window.location.href = `/make/preview/${resumeId}/`;
        } else {
        }
      } else {
        // 获取下一个模块的ID
        const nextModule = visibleModules[currentModuleIndex + 1];
        if (nextModule) {
          // 设置下一个模块为活动模块
          setActiveModule(nextModule.id);
        }
      }
    } catch (error) {
      toast.error('保存失败，请重试', {
        position: 'top-center'
      });
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <div className={cn("flex justify-end w-full", className)}>
      {isLastModule && resumeId ? (
        <Link
          href={`/make/preview/${resumeId}`}
          onClick={handleNextClick}
          className={cn(
            "flex items-center justify-center gap-2 bg-primary text-white py-2 px-5 rounded-md transition-all",
            "hover:bg-purple-600 active:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-200",
            "cursor-pointer hover:shadow-md",
            isSaving && "opacity-50 cursor-not-allowed pointer-events-none"
          )}
        >
          <span className="text-base font-medium">完成</span>
          <ArrowRight className="w-4 h-4" />
        </Link>
      ) : (
        <button
          onClick={handleNextClick}
          disabled={isSaving}
          className={cn(
            "flex items-center justify-center gap-2 bg-primary text-white py-2 px-5 rounded-md transition-all",
            "hover:bg-purple-600 active:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-200",
            "cursor-pointer hover:shadow-md",
            isSaving && "opacity-50 cursor-not-allowed"
          )}
        >
          <span className="text-base font-medium">
            {isLastModule ? '完成' : '下一步'}
          </span>
          <ArrowRight className="w-4 h-4" />
        </button>
      )}
    </div>
  );
}
