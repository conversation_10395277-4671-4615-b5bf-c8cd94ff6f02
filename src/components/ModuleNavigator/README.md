# ModuleNavigator 组件

这是一个用于模块间导航的公共组件，可以在各个模块组件中引入使用。

## 功能

- 显示"下一步"或"完成"按钮
- 点击"下一步"时自动跳转到下一个模块
- 根据模块索引自动判断是否显示"下一步"或"完成"

## 使用方法

```tsx
import ModuleNavigator from '@/components/ModuleNavigator';

export default function YourModuleComponent() {
  // 你的模块组件代码
  
  return (
    <div>
      {/* 你的模块内容 */}
      
      {/* 在底部添加导航器 */}
      <div className="mt-6 flex justify-center">
        <ModuleNavigator currentModuleId="your_module_id" />
      </div>
    </div>
  );
}
```

## 参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| currentModuleId | string | 是 | 当前模块的ID |
| className | string | 否 | 自定义样式类名 |

## 示例

在基本信息模块中使用：

```tsx
import ModuleNavigator from '@/components/ModuleNavigator';

export default function BasicInfo() {
  // 基本信息模块代码
  
  return (
    <div>
      {/* 基本信息表单 */}
      
      {/* 底部导航 */}
      <div className="mt-8 flex justify-center">
        <ModuleNavigator currentModuleId="basic_info" />
      </div>
    </div>
  );
}
```
