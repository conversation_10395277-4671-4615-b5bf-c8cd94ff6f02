'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle } from '@/components/ui/dialog';
import { X, AlertCircle, Crown, LogIn } from 'lucide-react';
import { aiApi } from '@/api/client/ai';
import { PrivilegeType, ModalType, BatchValidatePrivilegeResponse } from '@/api/client/types/ai';
import LoginDialog from '@/components/Auth/LoginDialog';
import MembershipWithDownloadCouponDialog from '@/components/MembershipWithDownloadCouponDialog';
import { toast } from 'sonner';

interface PrivilegeValidationDialogProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  description?: string;
  modalType?: ModalType;
  // 外部弹窗状态
  isLoginDialogOpen: boolean;
  setIsLoginDialogOpen: (open: boolean) => void;
  isMembershipDialogOpen: boolean;
  setIsMembershipDialogOpen: (open: boolean) => void;
}

/**
 * 权限校验结果弹窗组件
 */
function PrivilegeValidationDialog({
  isOpen,
  onClose,
  title = '权限提示',
  description = '请先完成相关操作',
  modalType = ModalType.ModalTypeLogin,
  isLoginDialogOpen,
  setIsLoginDialogOpen,
  isMembershipDialogOpen,
  setIsMembershipDialogOpen
}: PrivilegeValidationDialogProps) {

  const handleActionClick = () => {
    // 关闭当前弹窗
    onClose();

    // 根据弹窗类型打开相应的弹窗
    if (modalType === ModalType.ModalTypeLogin) {
      setIsLoginDialogOpen(true);
    } else if (modalType === ModalType.ModalTypeMembership) {
      setIsMembershipDialogOpen(true);
    }
  };

  const getActionButtonText = () => {
    return modalType === ModalType.ModalTypeLogin ? '立即登录' : '立即升级';
  };

  const getActionIcon = () => {
    return modalType === ModalType.ModalTypeLogin ? (
      <LogIn className="w-4 h-4" />
    ) : (
      <Crown className="w-4 h-4" />
    );
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="!w-[400px] !max-w-[400px] p-6 !rounded-xl">
          <DialogTitle className="sr-only">{title}</DialogTitle>
          
          {/* 关闭按钮 */}
          <button
            onClick={onClose}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">关闭</span>
          </button>

          {/* 内容区域 */}
          <div className="flex flex-col items-center text-center pt-4">
            {/* 图标 */}
            <div className="w-16 h-16 rounded-full bg-orange-100 flex items-center justify-center mb-4">
              <AlertCircle className="w-8 h-8 text-orange-500" />
            </div>

            {/* 标题 */}
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {title}
            </h3>

            {/* 描述 */}
            <p className="text-sm text-gray-600 mb-6 leading-relaxed">
              {description}
            </p>

            {/* 按钮组 */}
            <div className="flex gap-3 w-full">
              <button
                onClick={onClose}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleActionClick}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-purple-600 rounded-lg transition-colors flex items-center justify-center gap-2"
              >
                {getActionIcon()}
                {getActionButtonText()}
              </button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* 登录弹窗 */}
      <LoginDialog
        isOpen={isLoginDialogOpen}
        onClose={() => setIsLoginDialogOpen(false)}
      />

      {/* 会员充值弹窗 */}
      <MembershipWithDownloadCouponDialog
        isOpen={isMembershipDialogOpen}
        onClose={() => setIsMembershipDialogOpen(false)}
      />
    </>
  );
}

/**
 * 权限校验 Hook
 * 提供权限校验功能和弹窗管理
 */
export function usePrivilegeValidation() {
  const [validationDialog, setValidationDialog] = useState<{
    isOpen: boolean;
    title?: string;
    description?: string;
    modalType?: ModalType;
  }>({
    isOpen: false
  });

  // 将弹窗状态提升到 Hook 层级
  const [isLoginDialogOpen, setIsLoginDialogOpen] = useState(false);
  const [isMembershipDialogOpen, setIsMembershipDialogOpen] = useState(false);

  /**
   * 校验权限
   * @param privilegeTypes 需要校验的权限类型数组
   * @returns Promise<boolean> 是否通过校验
   */
  const validatePrivileges = async (privilegeTypes: PrivilegeType[]): Promise<boolean> => {
    try {
      const result = await aiApi.batchValidatePrivilege({
        privilege_types: privilegeTypes
      });

      if (result.all_allowed) {
        return true;
      } else {
        // 权限校验失败，显示弹窗
        setValidationDialog({
          isOpen: true,
          title: result.modal_title || '权限提示',
          description: result.modal_description || '请先完成相关操作',
          modalType: result.modal_type || ModalType.ModalTypeLogin
        });
        return false;
      }
    } catch (error) {
      console.error('权限校验失败:', error);
      toast.error('权限校验失败，请稍后重试', {
        position: 'top-center'
      });
      return false;
    }
  };

  const closeValidationDialog = () => {
    setValidationDialog({ isOpen: false });
  };

  const PrivilegeValidationDialogComponent = () => (
    <PrivilegeValidationDialog
      isOpen={validationDialog.isOpen}
      onClose={closeValidationDialog}
      title={validationDialog.title}
      description={validationDialog.description}
      modalType={validationDialog.modalType}
      isLoginDialogOpen={isLoginDialogOpen}
      setIsLoginDialogOpen={setIsLoginDialogOpen}
      isMembershipDialogOpen={isMembershipDialogOpen}
      setIsMembershipDialogOpen={setIsMembershipDialogOpen}
    />
  );

  return {
    validatePrivileges,
    PrivilegeValidationDialog: PrivilegeValidationDialogComponent
  };
}

export default PrivilegeValidationDialog;
