'use client';

import { Toaster } from '@/components/ui/sonner';
import FingerPrintInitializer from '@/components/FingerPrintInitializer';
import StoreInitializer from '@/components/StoreInitializer';
// 移除 AuthProvider 导入，使用状态管理库代替

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <>
      <Toaster />
      <FingerPrintInitializer />
      <StoreInitializer />
      {children}
    </>
  );
}
