'use client';

import { Suspense } from 'react';
import { useBdVidTracker } from '@/hooks/useBdVidTracker';

/**
 * BD_VID追踪组件内部实现
 * 使用useSearchParams需要在Suspense边界内
 */
function BdVidTrackerInner() {
  // 使用自定义Hook处理bd_vid追踪逻辑
  useBdVidTracker();

  // 这个组件不渲染任何UI，只是执行逻辑
  return null;
}

/**
 * BD_VID追踪组件
 *
 * 这个组件的作用是在全局范围内自动检测和保存URL中的bd_vid参数
 * 它会被放在根布局中，确保所有页面都能自动处理bd_vid参数
 *
 * 使用方式：
 * 1. 在layout.tsx中引入这个组件
 * 2. 放在Providers内部即可
 * 3. 无需在其他页面重复添加逻辑
 */
export default function BdVidTracker() {
  return (
    <Suspense fallback={null}>
      <BdVidTrackerInner />
    </Suspense>
  );
}
