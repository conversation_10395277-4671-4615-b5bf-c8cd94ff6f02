import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';
import { FINGERPRINT_KEY } from './lib/fingerprint';
import { serverUserApi } from './api/server/user';

/**
 * 中间件函数，用于处理所有匹配的请求
 * 1. 将cookie中的浏览器指纹添加到请求头中，以便服务端组件可以访问
 * 2. 检查用户中心页面的登录状态，未登录则重定向到首页
 * 3. 处理路由的尾部斜杠问题：
 *    - 以.html结尾的路由：不带尾部斜杠
 *    - 其他路由：带尾部斜杠
 *
 * 这个中间件会在所有页面路由和API路由的请求处理前执行
 */
export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;



  // 检查是否访问用户中心页面
  if (pathname.startsWith('/user')) {
    try {
      // 直接调用服务端API获取用户信息来验证登录状态
      const userInfo = await serverUserApi.getGuestUserInfo();

      // 如果获取用户信息失败或用户未登录，重定向到首页
      if (!userInfo || !userInfo.is_logged_in) {
        const url = request.nextUrl.clone();
        url.pathname = '/';
        return NextResponse.redirect(url);
      }
    } catch {
      // 如果API调用出错，重定向到首页
      const url = request.nextUrl.clone();
      url.pathname = '/';
      return NextResponse.redirect(url);
    }
  }

  // 创建响应对象，继续处理请求
  const response = NextResponse.next();

  // 从cookie中获取浏览器指纹
  const fingerprint = request.cookies.get(FINGERPRINT_KEY)?.value;

  // 如果存在指纹，将其添加到请求头中
  if (fingerprint) {
    // 将指纹添加到响应头中
    // 这样服务端组件可以通过headers() API访问这个值
    response.headers.set('X-Fingerprint', fingerprint);

    // 也可以添加到其他自定义头中，如果需要的话
    // response.headers.set('X-Custom-Header', 'some-value');
  }

  // 返回修改后的响应
  return response;
}

/**
 * 配置中间件匹配的路径
 */
export const config = {
  // 匹配所有路由，除了静态资源
  matcher: [
    /*
     * 匹配所有路由，但排除以下路径：
     * - _next/static (静态文件)
     * - _next/image (图片优化API)
     * - favicon.ico (网站图标)
     *
     * 这个配置会匹配所有页面路由和API路由
     */
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
