<svg width="200" height="156" viewBox="0 0 200 156" fill="none" xmlns="http://www.w3.org/2000/svg">
<mask id="mask0_335_3625" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="0" width="200" height="156">
<rect width="200" height="156" rx="7" fill="white"/>
</mask>
<g mask="url(#mask0_335_3625)">
<circle cx="67.0752" cy="67.0752" r="66.5752" transform="matrix(-0.92146 -0.388473 -0.388473 0.92146 232.336 83.5569)" stroke="url(#paint0_linear_335_3625)"/>
<circle opacity="0.9" cx="67.0752" cy="67.0752" r="66.5752" transform="matrix(-0.974919 -0.222561 -0.222561 0.974919 247.185 40.0377)" stroke="#F2EDFF" stroke-dasharray="4 4"/>
<circle opacity="0.8" cx="67.0752" cy="67.0752" r="66.5752" transform="matrix(-0.998755 -0.0498869 -0.0498869 0.998755 259.593 -1.94812)" stroke="#F6F2FF"/>
<circle opacity="0.7" cx="67.0752" cy="67.0752" r="66.5752" transform="matrix(-0.992244 0.124303 0.124303 0.992244 269.863 -42)" stroke="#ECE4FF" stroke-dasharray="4 4"/>
<circle cx="67.0752" cy="67.0752" r="67.0752" transform="matrix(-0.797157 0.603772 0.603772 0.797157 135.052 54.1447)" fill="url(#paint1_linear_335_3625)"/>
</g>
<defs>
<linearGradient id="paint0_linear_335_3625" x1="0.42905" y1="60.4072" x2="140.035" y2="57.6622" gradientUnits="userSpaceOnUse">
<stop stop-color="#E9E0FF"/>
<stop offset="1" stop-color="#F6F3FF"/>
</linearGradient>
<linearGradient id="paint1_linear_335_3625" x1="82.5317" y1="132.835" x2="67.0752" y2="3.77707e-06" gradientUnits="userSpaceOnUse">
<stop stop-color="#F1F2FF"/>
<stop offset="0.776942" stop-color="#F9F6FF"/>
<stop offset="0.991942" stop-color="white"/>
</linearGradient>
</defs>
</svg>
