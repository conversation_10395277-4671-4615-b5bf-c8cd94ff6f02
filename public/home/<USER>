<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_990_624)">
<rect width="22" height="22" rx="6" fill="#D9D2FF"/>
</g>
<path d="M9.0973 5.65846C8.79969 4.99674 9.28374 4.24829 10.0093 4.24829H11.9905C12.7161 4.24829 13.2001 4.99674 12.9025 5.65846L12.1672 7.29355C12.0057 7.65251 11.6487 7.88338 11.2552 7.88338H10.7447C10.3511 7.88338 9.9941 7.65251 9.83266 7.29355L9.0973 5.65846Z" fill="white"/>
<path d="M9.94263 9.72523C10.041 9.38216 10.3547 9.14575 10.7116 9.14575H11.2884C11.6452 9.14575 11.959 9.38216 12.0574 9.72523L13.5352 14.8788C13.6198 15.1738 13.5282 15.4911 13.2995 15.6957L11.5332 17.2749C11.2296 17.5464 10.7704 17.5464 10.4668 17.2749L8.70053 15.6957C8.47177 15.4911 8.38018 15.1738 8.46477 14.8788L9.94263 9.72523Z" fill="white"/>
<defs>
<filter id="filter0_b_990_624" x="-4" y="-4" width="30" height="30" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_990_624"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_990_624" result="shape"/>
</filter>
</defs>
</svg>
